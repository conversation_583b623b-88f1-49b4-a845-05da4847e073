import React, { useEffect } from "react";
import PropTypes from 'prop-types';
import useTableTabs from "@src/hooks/useTableTabs";
import useRSTabStore from "@src/store/rsTabStore";
import { useUserStore } from "@src/store";
import { Pagination, Table } from "@src/components/ui/Table";
import { useGetRequisitions } from '../api';
import { Tabs } from "@src/components/ui/Tabs";
import { Link, useNavigate } from 'react-router-dom';
import { Status } from './Status';
import { Button } from '@components/ui/Button';
import { cn } from '@src/utils/cn';
import DownNavIcon from '@assets/icons/downnav.svg?react';
import Download from '@assets/icons/download.svg?react';
import { DropdownMenu } from "@src/components/ui/DropdownMenu/DropdownMenu";
import { formatDateToDMY } from "@src/utils/dateFormat";
import { Spinner } from '@components/ui/Spinner';
import { remapDocType, remapRefNumber } from "@src/utils/remap";
import { SearchBar } from '@components/ui/SearchBar/SearchBar';
import { useDownloadTemplatePdf } from "@src/hooks/useDownloadTemplatePdf";
const ADMIN_GROUPS = ['Root User', 'Admin', 'Purchasing Admin'];

// const docTypeOptions = [
//   { key: 'R.S.', value: 'requisition' },
//   { key: 'Canvass', value: 'canvass' },
//   { key: 'Order', value: 'order' },
//   { key: 'Delivery', value: 'delivery_receipt'},
//   { key: 'Invoice', value: 'invoice' },
//   { key: 'Voucher', value: 'payment_request'},
//   { key: 'Non-R.S.', value: 'non_requisition' },
// ]

export const Dashboard_v2 = ({
  currentPage,
  currentLimit,
  setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
  permissions,
}) => {
  const navigate = useNavigate();
  const { mutate: downloadPdf, isPending: isPdfDownloading } = useDownloadTemplatePdf(true);
  const { user } = useUserStore();
  const { resetTabs } = useRSTabStore();
  const isAdmin = ADMIN_GROUPS.includes(user.role.name);
  const { create: createPermission, get: getPermission } = permissions;

  const { data: metaData, isLoading: metaLoading } = useGetRequisitions({
    page: 1,
    limit: 1,
    sortBy: null,
    filterBy: null,
    requestType: null,
  }, { enabled: getPermission });

  const [requestType, setRequestType] = React.useState(isAdmin ? 'my_approval' : null);
  const [searchQuery, setSearchQuery] = React.useState({});
  
  const computeTableConfig = () => {
    const baseConfig = [
        {
            key: 'my_request',
            value: 'My Requests',
        },
        {
            key: 'my_approval',
            value: 'For My Approval/Assigned',
        },
    ];

    if (isAdmin) {
        baseConfig.push({
            key: 'all',
            value: 'All',
        });
    }

    return baseConfig;
  }

  const tableConfig = React.useMemo(() => computeTableConfig(), [isAdmin]);

  const { activeTab, tabs, setActiveTab } = useTableTabs(setPage, currentPage, tableConfig, 1);

  const { data: requisitions, isLoading } = useGetRequisitions(
    {
      page: currentPage,
      limit: currentLimit,
      sortBy,
      filterBy: searchQuery,
      requestType: requestType,
    },
    { enabled: !metaLoading && Boolean(metaData) },
  );

  const requestTypeMapping = () => {
    if (requestType === 'my_request') {
      return {
        total: requisitions?.meta?.myRequestsTotal,
        data: requisitions?.my_request
      }
    }
    else if (requestType === 'my_approval') {
      return {
        total: requisitions?.meta?.myApprovalsTotal,
        data: requisitions?.my_approval
      }
    }
    else {
      return {
        total: requisitions?.meta?.allTotal,
        data: requisitions?.all
      }
    }
  }

  const { total, data } = React.useMemo(() => requestTypeMapping(), [requestType, requisitions]);

  useEffect(() => {
    if (!metaLoading && metaData) {
      const approver = metaData?.meta?.myApprovalsTotal > 0;
      const initial = isAdmin ? 'my_approval' : approver ? 'my_approval' : 'my_request';
      setRequestType(initial);
      setActiveTab(initial);
    }
  }, [metaLoading, metaData]);

  useEffect(() => {
    // add resetting of sort if needed, currently shares sorting across tabs
    if (activeTab === 'my_approval') {
      setRequestType(activeTab);
    }
    else if (activeTab === 'my_request') {
      setRequestType(activeTab);
    }
    else {
      setRequestType(null);
    }
  }, [activeTab]);

  const HEADERS = [
    { key: 'ref_number', value: 'Ref. Number', css: 'w-[12%]'},
    { key: 'doc_type', value: 'Document Type', css: 'w-[10%]'},
    { key: 'requestor', value: 'Requestor'},
    { key: 'company', value: 'Company'}, 
    { key: 'project', value: 'Proj/Dept'},
    { key: 'updatedAt', value: 'Last Updated', css: 'w-[10%]'},
    { key: 'status', value: 'Status', css: 'min-w-24'},
  ];
  
  const tdDesign = {
    updatedAt: {
      render: ({ updated_at }) => {
        return formatDateToDMY(updated_at)
      }
    },
    requestor: {
      render: ({ requestor_name }) => {
        return (<div className="text-wrap break-words">{requestor_name ?? '---'}</div>)
      }
    },
    project: {
      render: ({ project_name, department_name}) => {
        return (<div className="text-wrap break-words">{project_name ?? '---' + ' / ' + department_name ?? '---'}</div>)
      }
    },
    company: {
      render: ({ company_name}) => {
        return (<div className="text-wrap break-words">{company_name ?? '---'}</div>)
      }
    },
    ref_number: {
      render: ({ ref_number, doc_type, id, grouping_id, status }) =>
      {
        const redirectLink = remapRefNumber(doc_type, grouping_id, id, status)
        return (
          <Link
            to={redirectLink}
            onClick={() => resetTabs()}
            className="text-blue-500 underline underline-offset-4"
          >
            {ref_number}
          </Link>
        )
      }
    },
    doc_type: {
      render: ({ doc_type }) => {
        return (doc_type ?? '---')
      }
    },
    status: {
      css: '',
      render: Status,
    },
  }

  const searchItems =[
    {
      name: 'ref_number',
      type: 'text',
      placeholder: 'Input Ref. Number',
      isParent: true,
      label: 'Search',
    }, // Add new set of filters
  ];

  const handleDownloadPDF = () => {
    downloadPdf({
      requestType,
    })
  }

  if (metaLoading || isLoading) {
    return (
      <div className=" flex items-center justify-center h-[50vh]"> 
        <Spinner className="w-8 h-8"/>
      </div>
    )
  }

  return (
    <React.Fragment>
      <div className="flex flex-col">
        {/* <SearchBar
          searchItems={searchItems}
          onSearch={val => {
            setSearchQuery(val)}
          }
          subClassName="lg:grid-cols-6"
          setPage={setPage}
        /> */}

        <div className="my-6">
          <Tabs activeTab={activeTab} tabs={tabs}/>
        </div>

        <div className="flex space-x-2 flex-wrap sm:flex-nowrap justify-end">
          <Button
            variant="action"
            hover="action"
            className="color: bg-[#F0F1F1] text-[#420001] w-auto mt-2 sm:mt-0"
            icon={Download}
            iconPosition="L"
            isLoading={isPdfDownloading}
            disabled={isPdfDownloading || data.length === 0} // add disabled when no data
            onClick={handleDownloadPDF}
          >
            Download
          </Button>
        </div>

      <div
        className={cn('flex flex-col md:flex-row md:justify-end md:items-center gap-4 mb-4 md:absolute md:top-8 md:right-8 text-sm', {
          'justify-between': createPermission,
        })}
      >
        {createPermission && (
          <DropdownMenu
            className="w-full"
            triggerLabel="Create New Request"
            triggerProps={{
              title: "Create New Request",
              variant: "submit",
              className: "w-fit p-4 leading-5 rounded-md",
              icon: DownNavIcon,
              iconSize: "w-2 h-2",
            }}
            menuOptionsDesign={[
              {
                label: "Requisition Slip",
                onClick: () => navigate('create')
              },
              {
                label: "Non-RS Payment",
                onClick: () => navigate('/app/non-requisition-slip/create')
              }
            ]}
          />
        )}
      </div>

      <Pagination
        total={total || 0}
        setPage={setPage}
        setLimit={setLimit}
        hasLimit={true}
        >
          <Table
            headers={HEADERS}
            data={data || []}
            tdDesign={tdDesign}
            onSort={setSort}
            isLoading={isLoading}
            currentSort={currentSort}
            infoDescription={"Create a new request to add content"} 
            />
        </Pagination>
      </div>
    </React.Fragment>
  )
}


Dashboard_v2.propTypes = {
  currentPage: PropTypes.number.isRequired,
  currentLimit: PropTypes.number.isRequired,
  setPage: PropTypes.func.isRequired,
  setLimit: PropTypes.func.isRequired,
};
