import React from 'react';
import { cn } from '@utils/cn';
import { cva } from 'class-variance-authority';
import PropTypes from 'prop-types';

const STATUSES = {
  draft: 'Draft',
  for_approval: 'For Approval',
  assigning: 'Assigning',
  assigned: 'Assigned',

  // CANVASSING
  partially_canvassed: 'Partially Canvassed',
  canvass_approval: 'Canvass Approval',
  canvass_for_approval: 'Canvass Approval',
  cs_draft: 'CS Draft',
  cs_approval: 'For CS Approval',
  cs_rejected: 'CS Rejected',
  cs_approved: 'CS Approved',

  // Purchase Order
  po_approval: 'PO Approval',
  purchase_order: 'Purchase Order',
  for_po_review: 'For PO Review',
  for_po_approval: 'For PO Approval',
  for_delivery: 'For Delivery',
  cancelled_po: 'Cancelled PO',
  reject_po: 'PO Rejected',
  for_sending: 'For Sending',
  for_po_sending: 'For Sending',

  //Payment Request
  approved: 'Approved',
  for_pr_approval: 'For PR Approval',

  'partially ordered': 'Partially Ordered',
  ordered: 'Ordered',
  pending_settlement: 'Pending Settlement',
  on_hold: 'On Hold',
  cancelled: 'Cancelled',
  returned: 'Returned',
  rejected: 'Rejected',
  delivered: 'Delivered',
  paying: 'Paying',
  submitted: 'Submitted',
  closed: 'Closed',
  dr_draft: 'RR Draft',
  dr_for_invoice_receiving: 'For Invoice Receiving',
  Delivered: 'Delivered',
  'Fully Delivered': 'Fully Delivered',
  'Partially Delivered': 'Partially Delivered',
  'Fully Delivered with Returns': 'Fully Delivered with Returns',
  'Partially Delivered with Returns': 'Partially Delivered with Returns',

  'IR Draft': 'IR Draft',
  'Invoice Received': 'Invoice Received',

  // notes status
  Delivery: 'Delivery',
  Approver: 'Approver',
  Requestor: 'Requestor',

  default: '',
};

const statusStyles = cva(
  'p-1 min py-2 w-full justify-center items-center inline-flex text-xs leading-5 font-bold rounded-full whitespace-pre-wrap capitalize',
  {
    variants: {
      status: {
        //yellow
        draft: 'bg-[#F5EAC5] text-[#D5AB18]',
        'for approval': 'bg-[#F5EAC5] text-[#D5AB18]',
        assigning: 'bg-[#F5EAC5] text-[#D5AB18]',
        assigned: 'bg-[#F5EAC5] text-[#D5AB18]',
        for_po_review: 'bg-[#5B3D2C33] text-[#5B3D2C]',
        //orange
        for_approval: 'bg-[#F0963D33] text-[#F0963D]',
        for_pr_approval: 'bg-[#F0963D33] text-[#F0963D]',
        partially_canvassed: 'bg-[#F0963D33] text-[#F0963D]',
        canvass_approval: 'bg-[#F0963D33] text-[#F0963D]',
        canvass_for_approval: 'bg-[#F0963D33] text-[#F0963D]',
        for_po_approval: 'bg-[#F0963D33] text-[#F0963D]',
        for_sending: 'bg-[#2F80ED33] text-[#2F80ED]',
        for_po_sending: 'bg-[#2F80ED33] text-[#2F80ED]',
        for_po_closed: 'bg-[#5B3D2C33] text-[#5B3D2C]',
        'purchase order': 'bg-[#F0963D33] text-[#F0963D]',
        'po approval': 'bg-[#F0963D33] text-[#F0963D]',

        //-- end of status
        'partially ordered': 'bg-[#F0963D33] text-[#F0963D]',
        ordered: 'bg-[#F0963D33] text-[#F0963D]',
        'pending settlement': 'bg-[#F0963D33] text-[#F0963D]',
        // red
        'on hold': 'bg-[#E7532E33] text-[#E7532E]',
        cancelled: 'bg-[#E7532E33] text-[#E7532E]',
        returned: 'bg-[#E7532E33] text-[#E7532E]',
        rejected: 'bg-[#E7532E33] text-[#E7532E]',
        cancelled_po: 'bg-[#5F636833] text-[#5F6368]',
        reject_po: 'bg-[#DC433B33] text-[#DC433B]',
        // green
        delivered: 'bg-[#85BA4033] text-[#85BA40]',
        approved: 'bg-[#85BA4033] text-[#85BA40]',
        paying: 'bg-[#85BA4033] text-[#85BA40]',
        submitted: 'bg-[#85BA4033] text-[#85BA40]',
        for_delivery: 'bg-[#1EA52B33] text-[#1EA52B]',
        //
        closed: 'bg-[#4DA9EA4D] text-[#4DA9EA]',
        // Delivery statuses
        dr_draft: 'bg-[#5F636833] text-[#5F6368]',
        dr_for_invoice_receiving: 'bg-[#F0963D33] text-[#F0963D]',
        'Fully Delivered': 'bg-[#6FCF97] text-white',
        Delivered: 'bg-[#F0963D33] text-[#F0963D]',
        'Partially Delivered': 'bg-[#56CCF2] text-white',
        'IR Draft': 'bg-[#5F636833] text-[#5F6368]',
        'Invoice Received': 'bg-[#35C54933] text-[#35C549]',

        // notes status
        Delivery: 'bg-[#4DA9EA4D] text-[#4DA9EA]',
        Approver: 'bg-[#D8EFFC] text-[#2F80ED]',
        Requestor: 'bg-[#DDFCD8] text-[#219653]',

        cs_draft: 'bg-[#5F636833] text-[#5F6368]',
        cs_approval: 'bg-[#F0963D33] text-[#F0963D]',
        cs_rejected: 'bg-[#DC433B33] text-[#DC433B]',
        cs_approved: 'bg-[#1EA52B33] text-[#1EA52B]',

        default: '',
      },
    },
    defaultVariants: {
      status: 'default',
    },
  },
);

const Status = ({ status, className }) => {
  if (!status) return null;

  return (
    <span className={cn(statusStyles({ status }), className)}>
      {STATUSES[status]}
    </span>
  );
};

Status.propTypes = {
  status: PropTypes.string,
  className: PropTypes.string,
};

export { Status };
