import React, { useState, useEffect, act } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Table, Pagination } from '@components/ui/Table';
import { Tabs } from '@components/ui/Tabs';
import { SearchBar } from '@components/ui/SearchBar/SearchBar';
import { PurchaseOrder } from '@features/purchase-order/components/PurchaseOrder';
import { TableLayout } from '@src/components/layouts/TableLayout';
import { Status } from './Status';
import {
  useGetRequisitionSlip,
  useGetRequisitionDeliveryReceipts,
  useGetRSCanvassDocument,
  useGetRSPaymentsDocument,
  useGetReturnsDocument,
  useGetInvoiceDocument,
} from '../api';
import { useGetAllRSCanvass } from '@features/canvass/api';
import { formatDateToDMY } from '@utils/dateFormat';
import useDeliveryReceiptStore from '@store/deliveryReceiptStore';
import useRSTabStore from '@src/store/rsTabStore';
import { remapStatus } from '@src/utils/remap';
import { emojiRegex } from '@src/utils/regexUtils';

export const RSDocumentTab = ({
  activeTab,
  setActiveTab,
  id,
  currentPage,
  currentLimit,
  setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
  redirectToOrders,
}) => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState({});
  const [searchError, setSearchError] = useState('');
  const { updateMode } = useDeliveryReceiptStore();
  const {
    data: requisition,
    isLoading: isLoadingRequisition,
    refetch: refetchRequisition,
  } = useGetRequisitionSlip(id, {
    enabled: !!id,
  });

  const {
    data: deliveryReceiptsResponse,
    isLoading: isLoadingDeliveryReceipts,
    error: deliveryReceiptsError,
    refetch: refetchDeliveryData,
  } = useGetRequisitionDeliveryReceipts(
    {
      requisitionId: id,
      page: currentPage,
      limit: currentLimit,
      sortBy,
      search: searchQuery.drNumber,
    },
    {
      enabled: activeTab === 'deliveries',
      retry: (failureCount, error) => {
        if (error?.response?.status === 404) return false;
        return failureCount < 3;
      },
    },
  );

  const {
    data: canvassDetails,
    isLoading: isFetchingCanvassDetails,
    refetch: refetchCanvassData,
  } = useGetAllRSCanvass(
    {
      id,
      page: currentPage,
      limit: currentLimit,
      sortBy,
      filterBy: searchQuery,
    },
    {
      enabled: activeTab === 'canvasses',
      retry: (failureCount, error) => {
        if (error?.response?.status === 404) return false;
        return failureCount < 3;
      },
    },
  );

  const {
    data: paymentsDocuments,
    isLoading: isFetchingPaymentsDocuments,
    refetch: refetchPaymentsData,
  } = useGetRSPaymentsDocument(
    {
      id,
      page: currentPage,
      limit: currentLimit,
      sortBy,
      search: searchQuery.prNumber,
    },
    {
      enabled: activeTab === 'payments',
      retry: (failureCount, error) => {
        if (error?.response?.status === 404) return false;
        return failureCount < 3;
      },
    },
  );

  const {
    data: drReturnsDetails,
    isLoading: isFetchingReturnsDocuments,
    refetch: refetchReturnsData,
  } = useGetReturnsDocument(
    {
      id,
      page: currentPage,
      limit: currentLimit,
      sortBy,
      search: searchQuery.drNumber,
    },
    {
      enabled: activeTab === 'returns',
      retry: (failureCount, error) => {
        if (error?.response?.status === 404) return false;
        return failureCount < 3;
      },
    },
  );

  const {
    data: invoiceDetails,
    isLoading: isFetchingInvoiceDocuments,
    refetch: refetchInvoiceData,
  } = useGetInvoiceDocument(
    {
      id,
      page: currentPage,
      limit: currentLimit,
      sortBy,
      search: searchQuery.irNumber,
    },
    {
      enabled: activeTab === 'invoices',
      retry: (failureCount, error) => {
        if (error?.response?.status === 404) return false;
        return failureCount < 3;
      },
    },
  );

  const handleTabChange = tab => {
    setActiveTab(tab);
    // refreshActiveTable(tab);
    setPage(1);
    // Remove sortBy from URL params
    const url = new URL(window.location.href);
    url.searchParams.delete('sortBy');
    navigate(url.pathname + url.search, { replace: true });
  };

  const tabs = [
    {
      key: 'canvasses',
      value: 'Canvasses',
      onClick: () => handleTabChange('canvasses'),
    },
    {
      key: 'orders',
      value: 'Orders',
      onClick: () => handleTabChange('orders'),
    },
    {
      key: 'deliveries',
      value: 'Deliveries',
      onClick: () => handleTabChange('deliveries'),
    },
    {
      key: 'invoices',
      value: 'Invoices',
      onClick: () => handleTabChange('invoices'),
    },
    {
      key: 'payments',
      value: 'Payments',
      onClick: () => handleTabChange('payments'),
    },
  ];

  const headerConfigs = {
    canvasses: [
      { key: 'canvassNumber', value: 'Canvass No.' },
      { key: 'updatedAt', value: 'Last Updated' },
      { key: 'lastApprover', value: 'Next Approver' },
      { key: 'status', value: 'Status' },
    ],
    orders: [
      { key: 'orderNumber', value: 'Order No.' },
      { key: 'updatedAt', value: 'Last Updated' },
      { key: 'lastApprover', value: 'Last Approver' },
      { key: 'status', value: 'Status' },
    ],
    deliveries: [
      { key: 'drNumber', value: 'RR Number' },
      { key: 'supplier', value: 'Supplier' },
      { key: 'latestDeliveryDate', value: 'Last Delivery Date' },
      { key: 'latestDeliveryStatus', value: 'Status' },
    ],
    invoices: [
      { key: 'irNumber', value: 'Invoice No.' },
      { key: 'supplierInvoiceNo', value: 'Supplier Invoice No.' },
      { key: 'issuedInvoiceDate', value: 'Supplier Invoice Issued Date' },
      { key: 'invoiceAmount', value: 'Supplier Invoice Amount' },
      { key: 'updatedAt', value: 'Last Updated' },
      { key: 'status', value: 'Status',  css: 'w-[180px]' },
    ],
    payments: [
      { key: 'prNumber', value: 'Payment No.' },
      { key: 'lastUpdate', value: 'Last Updated' },
      { key: 'lastApprover', value: 'Last Approver' },
      { key: 'status', value: 'Status' },
    ],
  };

  const tdDesign = {
    canvasses: {
      canvassNumber: {
        render: value => {
          const {
            id: canvassId,
            requisitionId,
            requisition,
            csLetter,
            draftCsNumber,
            csNumber,
            status,
          } = value || {};
          const { companyCode } = requisition || {};

          const isDraft = status === 'draft';
          const canvassNumber = isDraft
            ? `CS-TMP-${companyCode}${csLetter}${draftCsNumber}`
            : `CS-${companyCode}${csLetter}${csNumber}`;

          return (
            <React.Fragment>
              {canvassId ? (
                <Link
                  to={`/app/requisition-slip/${requisitionId}/canvass/${canvassId}`}
                  className="text-blue-500 underline underline-offset-4 font-semibold"
                >
                  {canvassNumber}
                </Link>
              ) : (
                '--'
              )}
            </React.Fragment>
          );
        },
      },
      updatedAt: {
        render: value => (
          <p>
            {value.updatedAt
              ? formatDateToDMY(value.updatedAt, true, 'short')
              : '--'}
          </p>
        ),
      },
      lastApprover: {
        render: value => {
          if(value?.canvassApprovers){
            return (
            <p>
              {value?.canvassApprovers[0]?.approver
                ? `${value?.canvassApprovers[0].approver?.firstName} ${value?.canvassApprovers[0].approver?.lastName}`
                : '---'}
            </p>
          );
          } else {
            return '--'
          }
          
        },
      },
      status: {
        render: value => {
          const canvassStatus = remapStatus(value?.status);
          return <Status status={canvassStatus} />;
        },
      },
    },
    deliveries: {
      drNumber: {
        css: 'whitespace-normal overflow-visible text-clip w-fit text-blue-500 underline underline-offset-4 cursor-pointer',
        render: ({ id, drNumber, isDraft }) => {
          const mode = isDraft ? 'draft' : 'view';
          return (
            <Link
              to={`/app/receiving-report/${id}/requisition-slip/${requisition.id}`}
              onClick={() => updateMode(mode)}
              replace
            >
              {drNumber}
            </Link>
          );
        },
      },
      latestDeliveryStatus: {
        render: row => (
          <Status
            status={row.isDraft ? 'dr_draft' : 'Delivered'}
            className="w-64"
          />
        ),
      },
    },
    orders: {
      status: {
        render: row => <Status status={row.status} />,
      },
    },
    invoices: {
      irNumber: {
        css: 'whitespace-normal overflow-visible text-clip w-fit text-blue-500 underline underline-offset-4 cursor-pointer',
        render: ({ id, irNumber, isDraft }) => {
          const mode = isDraft ? 'draft' : 'view';
          return (
            <Link
              to={`/app/requisition-slip/${requisition.id}/invoice/${id}/`}
              onClick={() => updateMode(mode)}
              replace
            >
              {irNumber}
            </Link>
          );
        },
      },
      status: {
        render: row => <Status status={row.status} />,
      },
    },
    payments: {
      prNumber: {
        render: ({ prNumber, id }) => {
          return (
            <Link
              to={`/app/requisition-slip/${requisition.id}/payment-request/${id}`}
              className="text-blue-500 underline underline-offset-4 font-semibold"
            >
              {prNumber}
            </Link>
          );
        },
      },
      status: {
        render: row => <Status status={row.status} />,
      },
    },
    returns: {
      drNumber: {
        render: ({ drNumber, id }) => {
          return (
            <Link
              to={`/app/receiving-report/${id}/requisition-slip/${requisition.id}`}
              className="text-blue-500 underline underline-offset-4 font-semibold"
            >
              {drNumber}
            </Link>
          );
        },
      },
      latestDeliveryStatus: {
        render: row => (
          <Status status={row.latestDeliveryStatus} className="w-64" />
        ),
      },
    },
  };

  const data = {
    canvasses: canvassDetails?.data,
    deliveries: deliveryReceiptsResponse?.data,
    invoices: invoiceDetails?.data,
    payments: paymentsDocuments?.data,
    returns: drReturnsDetails?.data,
  };

  const totalData = {
    canvasses: canvassDetails,
    deliveries: deliveryReceiptsResponse,
    invoices: invoiceDetails,
    payments: paymentsDocuments,
    returns: drReturnsDetails,
  };

  const isFetching = {
    canvasses: isFetchingCanvassDetails,
    deliveries: isLoadingDeliveryReceipts,
    payments: isFetchingPaymentsDocuments,
    returns: isFetchingReturnsDocuments,
  };

  const searchItems = {
    canvasses: [
      {
        name: 'canvassNumber',
        type: 'text',
        placeholder: 'Search Canvass Number',
        isParent: true,
        label: 'Search',
      },
    ],
    deliveries: [
      {
        name: 'drNumber',
        type: 'text',
        placeholder: 'Search Receiving Report Number',
        isParent: true,
        label: 'Search',
      },
    ],
    invoices: [
      {
        name: 'irNumber',
        type: 'text',
        placeholder: 'Search Invoice Number, Supplier Invoice No.',
        isParent: true,
        label: 'Search',
      },
    ],
    payments: [
      {
        name: 'prNumber',
        type: 'text',
        placeholder: 'Search Payment Request Number',
        isParent: true,
        label: 'Search',
      },
    ],
    returns: [
      {
        name: 'drNumber',
        type: 'text',
        placeholder: 'Search Returned Delivery Receipt Number',
        isParent: true,
        label: 'Search',
      },
    ],
  };

  const handleSearch = (val) => {
    const searchValue = val[searchItems[activeTab][0].name];
    
    if (emojiRegex.test(searchValue)) {
      setSearchError('Emojis are not allowed in search');
      return;
    }
    
    const newSearchQuery = { ...val };
    if (newSearchQuery.hasOwnProperty('search') && newSearchQuery.search === '') {
      delete newSearchQuery.search;
    }
    setSearchQuery(newSearchQuery);
    setSearchError('');
  };

  return (
    <>
      <Tabs tabs={tabs} activeTab={activeTab} />
      {activeTab === 'orders' ? (
        <TableLayout>
          {paginationProps => (
            <PurchaseOrder {...paginationProps} hasLimit={false} id={id} />
          )}
        </TableLayout>
      ) : (
        <>
          <SearchBar
            searchItems={searchItems[activeTab]}
            onSearch={handleSearch}
            setPage={setPage}
            className="w-full pl-0 pr-0 bg-transparent"
            error={searchError}
            validateInput={(value) => emojiRegex.test(value)}
          />
          <Pagination
            total={totalData[activeTab]?.total ?? totalData[activeTab]?.length}
            setPage={setPage}
            setLimit={setLimit}
            hasLimit={false}
          >
            <Table
              headers={headerConfigs[activeTab]}
              data={data[activeTab]}
              tdDesign={tdDesign[activeTab]}
              isLoading={isFetching[activeTab]}
              onSort={setSort}
              currentSort={currentSort}
              infoHeader="No Records Found"
              infoDescription=""
            />
          </Pagination>
        </>
      )}
    </>
  );
};
