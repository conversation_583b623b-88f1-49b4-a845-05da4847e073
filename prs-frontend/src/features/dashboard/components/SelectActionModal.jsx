import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Modal } from '@components/ui/Modal';
import { Button } from '@src/components/ui/Button';
import RightCaret from '@assets/icons/right-caret.svg?react';
import { useApproverContext } from './ApproverContext';

const SelectActionModal = ({
  user,
  requisition,
  isOpen,
  onClose,
  onAssign,
  onNavigate,
  requestType,
  onAddItems,
  navigationItems,
  isPurchasingHead,
  isPurchasingStaff,
  requisitionActions,
  onSelectRelatedDocuments,
}) => {
  const { requisitionStatus, assignedTo } = useApproverContext();
  const isNonOfm = requestType === 'non-ofm' || requestType === 'non-ofm-tom';
  const isRequestor = user.id === requisition.createdByUser.id;
  const navigate = useNavigate();
  const canStillCanvass = requisition?.hasItemsAvailableForCanvassing;
  const navigateToCanvassSheet = () => {
    navigate('canvass/create', { replace: true });
  };

  const actionMappings = {
    hasPOForReview: {
      text: 'Review PO',
      onNavigate: () => {
        const TABS = {
          primary: 'related-documents',
          secondary: 'orders',
        };
        onSelectRelatedDocuments(TABS); // Trigger the tab change
        onClose();
      },
    },
    hasPOForApproval: {
      text: 'Approve PO',
      onNavigate: () => {
        const TABS = {
          primary: 'related-documents',
          secondary: 'orders',
        };
        onSelectRelatedDocuments(TABS); // Trigger the tab change
        onClose();
      },
    },
  };

  const assignedToUser = user.id === requisition?.assignedTo?.id;
  const allApproversApproved = requisition?.requisitionApprovers?.data?.every(
    approver => approver.status === 'approved',
  );

  return (
    <Modal
      size="small"
      header="Select Action"
      isOpen={isOpen}
      onClose={onClose}
    >
      <div>
        Here are the current available actions for this request. Action options
        under this menu will be updated as the request progresses.
      </div>
      <div className="cursor-pointer text-[#754445] font-bold leading-8">
        {requisitionStatus === 'assigned' &&
          isPurchasingStaff &&
          assignedToUser &&
          isNonOfm && (
            <button
              className="w-full flex justify-between items-center"
              onClick={onAddItems}
            >
              Add Items <RightCaret />
            </button>
          )}

        {/* Delivery Receipt should have condition */}

        {requisitionStatus !== 'submitted' &&
          (isPurchasingHead || (isPurchasingStaff && !assignedTo)) && (
            <div
              className="w-full flex justify-between items-center"
              onClick={onAssign}
            >
              Assign <RightCaret />
            </div>
          )}

        {/* Remove this if canvass is supported on the BE */}
        {allApproversApproved && assignedToUser && canStillCanvass && (
          <div
            className="w-full flex justify-between items-center"
            onClick={() => navigateToCanvassSheet()}
          >
            Enter Canvass <RightCaret />
          </div>
        )}

        {Object.entries(requisitionActions).map(([actionKey, isEnabled]) => {
          if (isEnabled && actionMappings[actionKey]) {
            const { text, onNavigate } = actionMappings[actionKey];
            return (
              <div
                key={actionKey}
                className="w-full flex justify-between items-center"
                onClick={onNavigate}
              >
                {text} <RightCaret />
              </div>
            );
          }
          return null;
        })}

        {/* Remove the DR and PR if supported on the BE */}

        {requisitionActions.hasForDelivery &&
          (assignedToUser || isRequestor) && (
            <div
              className="w-full flex justify-between items-center"
              onClick={() =>
                navigate(`/app/receiving-report/create/${requisition?.id}`)
              }
            >
              Confirm Delivery
              <RightCaret />
            </div>
          )}
        {(assignedToUser || isRequestor) &&
          requisition?.actions?.canCreateInvoice && (
            <div
              className="w-full flex justify-between items-center"
              onClick={() =>
                navigate(
                  `/app/requisition-slip/${requisition?.id}/invoice/create`,
                )
              }
            >
              Receive Invoice <RightCaret />
            </div>
          )}
        {assignedToUser && requisition?.actions?.canCreatePaymentRequest && (
          <div
            className="w-full flex justify-between items-center"
            onClick={() =>
              navigate(
                `/app/requisition-slip/${requisition.id}/payment-request/create`,
              )
            }
          >
            Create Payment Request <RightCaret />
          </div>
        )}
      </div>
      <Button onClick={onClose} variant="outline" hover="outline">
        Close window
      </Button>
    </Modal>
  );
};

export default SelectActionModal;
