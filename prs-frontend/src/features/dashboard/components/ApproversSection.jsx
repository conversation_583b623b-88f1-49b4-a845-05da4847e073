import React, { useEffect, useState, useRef } from 'react';
import { Form, TextArea } from '@src/components/ui/Form';
import { useParams } from 'react-router-dom';
import { Button } from '@src/components/ui/Button';
import AutoComplete from '@src/components/ui/Form/AutoComplete';
import { Modal } from '@components/ui/Modal';
import { setDefaultDateValue } from '@utils/dateFormat';
import { useNotification } from '@src/hooks/useNotification';
import { useOutsideClick } from '@src/hooks/useOutsideClick';
import { useApproverContext } from './ApproverContext';
import { ApprovalStatus } from './ApprovalStatus';
import MenuIcon from '@assets/icons/vertical-ellipsis.svg?react';
import { useUserStore } from '@src/store';
import {
  useGetRSApprovers,
  useAddRequisitionApprover,
  useUpdateApprover,
} from '../api';
import { Spinner } from '@src/components/ui/Spinner';

const MODAL_TYPES = {
  NONE: 'NONE',
  ADD_APPROVER: 'ADD_APPROVER',
  EDIT_APPROVER: 'EDIT_APPROVER',
  DELETE_APPROVER: 'DELETE_APPROVER',
};

export const ApproversSection = ({ dateRequired, isRefetching }) => {
  const { showNotification } = useNotification();
  const { id } = useParams();
  const { user } = useUserStore();
  const { approvers, addApprover, altApprovers } = useApproverContext();

  const [isModalAddApprover, setIsModalAddApprover] = useState(false);
  const [selectedApprover, setSelectedApprover] = useState();

  const mainFormRef = useRef(null);

  const [modalState, setModalState] = useState({
    isOpen: false,
    type: MODAL_TYPES.NONE,
    data: null,
  });

  const openModal = (type, data = null) => {
    setModalState({ isOpen: true, type, data });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      type: MODAL_TYPES.NONE,
      data: null,
    });
  };

  const {
    data: approversOptions,
    refetch: refetchApproversOptions,
    isRefetching: isRefetchApproversOptions,
    isLoading: isApproversOptionsLoading,
  } = useGetRSApprovers(id);

  const { mutateAsync: addRequisitionApprover } = useAddRequisitionApprover();
  const { mutateAsync: updateApprover } = useUpdateApprover();
  const sortedApprovers = approvers?.sort((a, b) => a.level - b.level);

  const hasAltApprover = approvers.some(item => item.isAltApprover === true);

  const isAssignedApprover = approvers?.some(
    approver => approver.approverId === user.id,
  );

  const getAssignedApproverLevel = () => {
    const assignedApprover = approvers?.find(
      approver => approver.approverId === user.id,
    );
    return assignedApprover ? assignedApprover.level : null;
  };

  const getAssignedApproverStatus = () => {
    const assignedApprover = approvers?.find(
      approver => approver.approverId === user.id,
    );
    return assignedApprover ? assignedApprover.status : null;
  };

  useEffect(() => {
    if (isModalAddApprover) {
      refetchApproversOptions();
    }
  }, [isModalAddApprover, refetchApproversOptions]);

  const submitAddApprover = async () => {
    const dataValues = {
      approverId: String(selectedApprover.value),
    };

    try {
      await addRequisitionApprover(
        { id: id, data: dataValues },
        {
          onSuccess: () => {
            showNotification({
              type: 'success',
              message: 'Approver added successfully!',
            });
          },
          onError: () => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message ||
                'An error occurred while adding the approver.',
            });
          },
        },
      );

      addApprover(selectedApprover, getAssignedApproverLevel());

      setIsModalAddApprover(false);
      setSelectedApprover(null);
    } catch (error) {
      showNotification({
        type: 'error',
        message:
          error?.response?.data?.message ||
          'An error occurred while adding the approver.',
      });
    }
  };

  const handleDeleteApprover = async () => {
    const dataValues = {
      approverId: null,
    };

    try {
      await updateApprover(
        { reqId: id, approverId: modalState.data.recordId, data: dataValues },
        {
          onSuccess: () => {
            closeModal();
            refetchApproversOptions();
            showNotification({
              type: 'success',
              message: 'Approver removed successfully!',
            });
          },
          onError: () => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message ||
                'An error occurred while adding the approver.',
            });
          },
        },
      );
    } catch (error) {
      showNotification({
        type: 'error',
        message:
          error?.response?.data?.message ||
          'An error occurred while adding the approver.',
      });
    }
  };

  const handleEditApprover = async () => {
    const dataValues = {
      approverId: selectedApprover.value,
    };

    try {
      await updateApprover(
        { reqId: id, approverId: modalState.data.recordId, data: dataValues },
        {
          onSuccess: () => {
            closeModal();
            refetchApproversOptions();
            showNotification({
              type: 'success',
              message: 'Approver updated successfully!',
            });
          },
          onError: () => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message ||
                'An error occurred while adding the approver.',
            });
          },
        },
      );
    } catch (error) {
      showNotification({
        type: 'error',
        message:
          error?.response?.data?.message ||
          'An error occurred while adding the approver.',
      });
    }
  };

  const checkAltApprover = approver => {
    const _dateRequired = new Date(setDefaultDateValue(dateRequired)).setHours(
      0,
      0,
      0,
      0,
    );

    if (approver?.approver?.userLeaves?.length > 0) {
      const found = approver?.approver?.userLeaves?.find(leave => {
        const startDate = new Date(leave?.startDate).setHours(0, 0, 0, 0);
        const endDate = new Date(leave?.endDate).setHours(0, 0, 0, 0);
        return startDate <= _dateRequired && _dateRequired <= endDate;
      });

      if (found && approver?.altApprover) {
        return approver?.altApprover?.fullName;
      }
    }

    return approver?.approver?.fullName;
  };

  return (
    <>
      <div className="max-w-full mx-auto bg-[#FFFFFF] p-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h4 className="font-semibold">Approvers</h4>
          {/* NOTE: show add button to the assigned approvers only */}
          <>
            {isAssignedApprover &&
              getAssignedApproverStatus() != 'approved' && (
                <Button
                  variant="outline"
                  hover="outline"
                  className="px-8 py-1 w-auto"
                  onClick={() => setIsModalAddApprover(true)}
                >
                  Add
                </Button>
              )}
          </>
        </div>
        {isRefetching ? (
          <Spinner className="mx-auto" />
        ) : (
          <>
            {approvers?.length !== 0 ? (
              <div className="space-y-3">
                {sortedApprovers?.map(approver => (
                  <ApproverItem
                    approversList={approvers}
                    key={approver.id}
                    approver={approver}
                    name={checkAltApprover(approver)}
                    status={approver?.status}
                    onEdit={() =>
                      openModal(MODAL_TYPES.EDIT_APPROVER, {
                        approverId: approver?.approver.id,
                        recordId: approver.id,
                      })
                    }
                    onDelete={() =>
                      openModal(MODAL_TYPES.DELETE_APPROVER, {
                        approverId: approver?.approver.id,
                        recordId: approver.id,
                      })
                    }
                  />
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                <p>No approvers found</p>
              </div>
            )}
          </>
        )}
      </div>
      <Modal
        size="small"
        header={
          modalState.type === MODAL_TYPES.ADD_APPROVER
            ? 'Add Approver'
            : 'Edit Approver'
        }
        onClose={closeModal}
        isOpen={
          modalState.type === MODAL_TYPES.ADD_APPROVER ||
          modalState.type === MODAL_TYPES.EDIT_APPROVER
        }
      >
        <div className="flex flex-col gap-6">
          <p className="text-sm">
            {modalState.type === MODAL_TYPES.ADD_APPROVER
              ? 'You are about to add an approver. Please select your designated approver and press "Add Approver" if you want to proceed with this action.'
              : 'You are about to edit an approver. Please select your designated approver and press "Update Approver" if you want to proceed with this action.'}
          </p>

          <AutoComplete
            className="max-w-[600px] mt-4"
            onSelect={setSelectedApprover}
            options={approversOptions?.data?.map(approver => ({
              key: `${approver.fullName}`,
              value: approver.id,
            }))}
            label="Select Approver"
          />

          <div className="flex justify-center gap-2">
            <Button variant="outline" hover="outline" onClick={closeModal}>
              Cancel
            </Button>
            <Button variant="submit" onClick={handleEditApprover}>
              {modalState.type === MODAL_TYPES.ADD_APPROVER
                ? 'Add Approver'
                : 'Update Approver'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Approver Modal */}
      <Modal
        size="small"
        header="Delete Approver"
        onClose={closeModal}
        isOpen={modalState.type === MODAL_TYPES.DELETE_APPROVER}
      >
        <div className="flex flex-col gap-6">
          <p className="text-sm">
            You are about to delete this Approver. This action is irreversible.
            Press &quot;Continue&quot; if you want to proceed with this action.
          </p>

          <div className="flex justify-center gap-2">
            <Button variant="outline" hover="outline" onClick={closeModal}>
              Cancel
            </Button>
            <Button variant="submit" onClick={handleDeleteApprover}>
              Continue
            </Button>
          </div>
        </div>
      </Modal>
      <Modal
        size="small"
        header={'Add Approver'}
        onClose={() => setIsModalAddApprover(false)}
        isOpen={isModalAddApprover}
      >
        You are about to add an approver. Please select your designated approver
        and press &quot;Add Approver&quot; if you want to proceed with this
        action.
        <AutoComplete
          className="max-w-[600px] mt-4"
          onSelect={setSelectedApprover}
          options={approversOptions?.data?.map(approver => ({
            key: `${approver.fullName}`,
            value: approver.id,
          }))}
          label="Select Approver"
        />
        <div className="flex gap-3 mt-8">
          <Button
            variant="outline"
            hover="outline"
            onClick={() => setIsModalAddApprover(false)}
          >
            Cancel
          </Button>
          <Button
            variant="submit"
            disabled={!selectedApprover}
            onClick={() => submitAddApprover()}
          >
            Add Approver
          </Button>
        </div>
      </Modal>
    </>
  );
};

const ApproverItem = ({
  approver,
  name,
  status,
  onEdit,
  onDelete,
  approversList,
}) => {
  const { user } = useUserStore();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const toggleDialog = () => {
    setIsDialogOpen(prev => !prev);
  };

  const dialogRef = useRef(null);
  useOutsideClick(dialogRef, () => setIsDialogOpen(false), isDialogOpen);

  const isApproverPending = approver?.status === 'pending';
  const isAddedByApprover = approver?.addedBy === user?.id && isApproverPending;

  // const isAdditionalApprover = approver?.isAdditionalApprover;
  // const isAltApprover = approver?.isAltApprover;

  // const shouldShowMenuIcon =
  //   (isAltApprover && isApproverPending) ||
  //   (isApproverPending && isAdditionalApprover);

  return (
    <div className="flex items-center gap-3">
      <div className="flex-1">
        <div className="relative">
          <div className="text-sm font-medium mb-2">
            {isAddedByApprover && <span className="font-bold">*:</span>}
            {name}
          </div>
          {isAddedByApprover && (
            <MenuIcon
              className="absolute right-0 top-1 h-5 w-auto cursor-pointer"
              onClick={toggleDialog}
            />
          )}

          {isDialogOpen && (
            <div
              className="absolute right-0 top-6 bg-white border border-gray-300 rounded-lg shadow-md z-10"
              ref={dialogRef}
            >
              <Button
                type="button"
                variant="noColor"
                hover="action"
                className="w-full inline-flex justify-start rounded-none"
                onClick={() => {
                  onEdit();
                  toggleDialog();
                }}
              >
                Edit
              </Button>
              <Button
                variant="noColor"
                hover="action"
                className="w-full inline-flex justify-start rounded-none"
                onClick={() => {
                  onDelete();
                  toggleDialog();
                }}
              >
                Delete
              </Button>
            </div>
          )}
          {status !== 'default' && <ApprovalStatus status={status} />}
          {status === 'default' && <div className="text-gray-400">--</div>}
        </div>
      </div>
    </div>
  );
};
