import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@src/components/ui/Button';
import { useNotification } from '@src/hooks/useNotification';
import { Form, Input, File, TextArea, Select } from '@src/components/ui/Form';
import { Modal, ConfirmModal } from '@src/components/ui/Modal';
import { CreateRSItemsTable } from './CreateRSItemsTable';
import { SteelbarsTable } from './SteelbarsTable';
import PlusIcon from '@assets/icons/plus.svg?react';
import { formatDateToDMY } from '@src/utils/dateFormat';
import { formatDateToLocalTime } from '@src/utils/dateFormat';
import { setDefaultDateValue } from '@src/utils/dateFormat';
import {
  transformRequisitionArray,
  steelbarsItemsExtractor,
} from '@src/utils/itemListSelector';
import { useRequisitionItemsStore } from '@src/store/requisitionItemsStore';
import { useUserStore } from '@src/store';
import { Tabs } from '@src/components/ui/Tabs';
import XIcon from '@assets/icons/xicon.svg?react';
import useTableTabs from '@src/hooks/useTableTabs';
import {
  useGetDeliveryAddress,
  usegetChargeToClientList,
  useGetRequisitionSlip,
  useCreateRequisition,
  useUpdateRequisition,
  useSubmitRequisition,
  useUpdateRequisitionItemList,
  useUploadRequisitionAttachments,
} from '../api';
import { useGetOFMItems as useGetOFMItemsList } from '@src/features/ofm-list/api';
import { useGetOFMItems } from '@src/features/ofm/api';
import { useGetNonOfmItems } from '@src/features/non-ofm/api';

import { OFMTRansferModal } from './OFMTRansferModal';
import { NonOFMTRansferModal } from './NonOFMTRansferModal';
import { AddItemModal } from './AddItemModal';
import { useGetRequisitionOptions } from '../api/get-requistion-options';

import { NoItems } from './NoItems';

import _ from 'lodash';

const MODES = {
  EDIT: 'edit',
  VIEW: 'view',
};

const requestTypes = [
  { key: 'OFM', value: 'ofm' },
  { key: 'OFM Transfer of Materials', value: 'ofm-tom' },
  { key: 'Non-OFM', value: 'non-ofm' },
  { key: 'Non-OFM Transfer of Materials', value: 'non-ofm-tom' },
].sort((a, b) => a.key.localeCompare(b.key));

const CreateRS = ({ setPage, setLimit, setSort, currentSort, currentPage }) => {
  const { showNotification } = useNotification();
  const {
    requisitionItems,
    steelbarItems,
    addSteelbarItem,
    removeSteelbarItem,
    updateSteelbarQuantity,
    addRequisitionItem,
    clearRequisitionItemsStore,
    updateRequisitionQuantity,
    updateSteelbarNote,
    updateRequisitionNote,
    removeRequisitionItem,
  } = useRequisitionItemsStore();
  const [hasAttachments, setHasAttachments] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [modalData, setModalData] = useState({
    isOpen: false,
    type: '',
  });

  const [optionsData, setOptionsData] = useState({
    categories: [],
    chargeTo: [],
    companies: [],
    deliverTo: [],
    departments: [],
    projects: [],
    requestType: [],
  });
  const [uploadProgress, setUploadProgress] = useState(0);

  const fileRef = useRef(null);
  const mainFormRef = useRef(null);
  const transferFormRef = useRef(null);

  const navigate = useNavigate();
  const location = useLocation();

  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isFromSubmission, setIsFromSubmission] = useState(false);
  const [RSType, setRSType] = useState('');
  const [chargeToCategory, setChargeToCategory] = useState('');
  const [transferFormVisible, setTransferFormVisible] = useState(false);
  const [submitType, setSubmitType] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  // states for selectList logic
  const { id } = useParams();

  const [removedAttachments, setRemovedAttachments] = useState([]);
  const [tempProjectsList, setTempProjectsList] = useState([]);
  const [tempCompaniesList, setTempCompaniesList] = useState([]);

  const { user } = useUserStore();
  const { setActiveTab, activeTab, tabs } = useTableTabs(setPage, currentPage);

  const {
    data: requisition,
    isFetching: isRequisitionFetching,
    error,
  } = useGetRequisitionSlip(id, { enabled: !!id });

  const isRequestor = !id
    ? true
    : user?.id === requisition?.createdByUser?.id &&
      user?.fullNameUser === requisition?.createdByUser?.fullName;

  const {
    data: chargeToClientList,
    ischargeToClientListFetching,
    chargeToClientListerror,
    refetch: refetchchargeToClientList,
  } = usegetChargeToClientList({
    category: chargeToCategory,
  });

  const { data: requisitionOptions } = useGetRequisitionOptions({});

  const {
    data: OFMListItems,
    isLoading,
    refetch,
  } = useGetOFMItemsList({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });

  const isOFMEnabled = useMemo(
    () => requisition?.type === 'ofm' || requisition?.type === 'ofm-tom',
    [requisition],
  );

  const { data: OFMItems } = useGetOFMItems({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });

  const { data: nonOfmItems, isFetching: isFetchingNonOFMS } =
    useGetNonOfmItems({
      paginate: false,
      sortBy: { initial: 'ASC' },
    });

  const {
    mutateAsync: createRequisition,
    error: createRequisitionError,
    isPending: isCreatingRequisition,
  } = useCreateRequisition();

  const {
    mutateAsync: updateRequisition,
    error: updateRequisitionError,
    isPending: isUpdatingRequisition,
  } = useUpdateRequisition();

  const {
    mutateAsync: submitRequisition,
    error: submitRequisitionnError,
    isPending: isSubmittingRequisition,
  } = useSubmitRequisition();

  const dropdownOptionsSetter = () => {
    setOptionsData({
      categories: requisitionOptions?.categories?.map(category => ({
        key: _.startCase(category),
        value: category,
      })),
      chargeTo: requisitionOptions?.chargeTo?.map(data => ({
        key: data,
        value: data,
      })),
      requestType: requisitionOptions?.requestType?.map(type => ({
        key: type,
        value: type,
      })),
      companies: requisitionOptions?.companies?.map(company => ({
        key: company?.initial + ' - ' + company?.name,
        value: company?.id,
        ...company,
      })),
      deliverTo: requisitionOptions?.deliverTo?.map(address => ({
        key: address?.address,
        value: address?.address,
      })),
      departments: requisitionOptions?.departments?.map(department => ({
        key: department?.name,
        value: department?.id,
      })),
      projects: requisitionOptions?.projects?.map(project => ({
        key: project?.name,
        value: project?.id,
        ...project,
      })),
    });
  };

  useEffect(() => {
    if (requisitionOptions) {
      dropdownOptionsSetter();
    }
  }, [requisitionOptions]);

  // Clear when switching between RS type (will be deprecated once refactoring starts?)
  useEffect(() => {
    clearRequisitionItemsStore();
  }, [RSType]);

  useEffect(() => {
    refetchchargeToClientList();
  }, [chargeToCategory]);

  const requisitionItemListDraft = transformRequisitionArray(
    requisition?.requisitionItemLists?.data,
  );

  let steelbarItemsList = [];
  useEffect(() => {
    steelbarItemsList = steelbarsItemsExtractor(
      requisition?.requisitionItemLists?.data,
      OFMItems?.data,
      isOFMEnabled,
    ).map(items => {
      return {
        ...items,
        quantity: parseFloat(items?.quantity).toFixed(3) || 0,
      };
    });
  }, [requisition, RSType]);

  const itemsWithoutSteelbars = requisitionItemListDraft?.filter(
    item => !item.isSteelbars,
  );

  useEffect(() => {
    if (requisition?.type) {
      setRSType(requisition?.type);
      if (requisition?.requisitionItemLists?.data) {
        if (steelbarItemsList?.length > 0) {
          addSteelbarItem(steelbarItemsList);
        }

        if (itemsWithoutSteelbars?.length > 0) {
          const nonSteelBarsFormatted = itemsWithoutSteelbars.map(items => {
            return {
              ...items,
              quantity: parseFloat(items?.quantity).toFixed(3) || 0,
            };
          });
          addRequisitionItem(nonSteelBarsFormatted);
        }
      }
    }
  }, [requisition, RSType]);
  // Used to sync steelbar items when draft/submitted as there are rendering issues
  useEffect(() => {
    if (
      requisition &&
      steelbarItemsList?.length > 0 &&
      steelbarItems?.length === 0
    ) {
      addSteelbarItem(steelbarItemsList);
    }
  }, [steelbarItemsList]);

  useEffect(() => {
    if (requisition?.chargeTo) {
      setChargeToCategory(requisition.chargeTo);
    }
    setAttachments(requisition?.attachments?.data);
  }, [requisition]);

  useEffect(() => {
    if (requisitionItems?.length < 1 && steelbarItems?.length > 0) {
      setActiveTab('steel-bars');
    }
  }, [steelbarItems, requisitionItems]);

  const chargeToList = chargeToClientList?.result?.data?.map(list => ({
    key: list?.name,
    value: list?.id,
  }));

  const chargeToCategories = [
    {
      key: 'Company',
      value: 'company',
    },
    {
      key: 'Supplier',
      value: 'supplier',
    },
    {
      key: 'Association',
      value: 'association',
    },
    {
      key: 'Project',
      value: 'project',
    },
  ].sort((a, b) => a.key.localeCompare(b.key));

  const closeModal = () => {
    setModalData(prevData => ({
      ...prevData,
      isOpen: !prevData?.isOpen,
    }));
  };

  const removeAttachment = (id, attachment) => {
    // To removed attachments with current index
    const newIndex = removedAttachments.length;
    const removedAttachment = {
      formKey: `updateAttachments[${newIndex}]`,
      id: id,
      path: attachment.path,
    };

    setRemovedAttachments(prev => [...prev, removedAttachment]);

    // Remove from current attachments
    setAttachments(prevAttachments =>
      prevAttachments.filter(attachment => attachment.id !== id),
    );
  };

  const handleSaveAsDraft = async values => {
    const formData = new FormData();
    const items = [...requisitionItems, ...steelbarItems];

    const hasItemQtyExceedingLimit = items.find(
      item => item?.remainingGfq < item?.quantity,
    );

    if (!items.length) {
      showNotification({
        type: 'error',
        message: 'Please add an item to continue.',
      });
    } else if (hasItemQtyExceedingLimit) {
      showNotification({
        type: 'error',
        message: `Item ${hasItemQtyExceedingLimit?.itmDes} quantity exceeded the its remaining QFG of ${hasItemQtyExceedingLimit?.remainingGfq}.`,
      });
    } else {
      const baseData = {
        ...values,
        comment: values.comment ?? '',
        companyId: values?.companyId?.toString(),
        category: values?.category,
        departmentId: values?.departmentId?.toString(),
        projectId: values?.projectId?.toString(),
        dateRequired: requisition
          ? setDefaultDateValue(values?.dateRequired)
          : values?.dateRequired,
        attachments: values?.attachments,
        itemList: items,
        ...(submitType === 'draft' ? { isDraft: 'true' } : {}),
        ...(submitType === 'submit' ? { isDraft: 'false' } : {}),
        ...(requisition?.id && submitType === 'submit'
          ? { id: requisition.id }
          : {}),
      };

      if (values?.chargeToId) {
        baseData.chargeToId = values?.chargeToId?.toString();
      } else {
        delete baseData.chargeToId;
      }

      try {
        delete baseData.attachments;

        const isComplexType = value =>
          typeof value === 'object' && value !== null;

        Object.entries(baseData).forEach(([key, value]) => {
          if (isComplexType(value)) {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value);
          }
        });

        values.attachments.forEach(attachment => {
          formData.append('attachments', attachment.file);
        });

        const handleRequisitionSubmission = async formData => {
          try {
            if (removedAttachments.length) {
              // Add removed attachments data
              const removeAttachmentFormData = new FormData();

              removedAttachments.forEach(removedAttachment => {
                removeAttachmentFormData.append(
                  `${removedAttachment.formKey}[id]`,
                  removedAttachment.id,
                );
                removeAttachmentFormData.append(
                  `${removedAttachment.formKey}[path]`,
                  removedAttachment.path,
                );
              });
              removeAttachmentFormData.append(
                'companyId',
                values?.companyId?.toString(),
              );

              await updateRequisition(
                { id, requisitionData: removeAttachmentFormData },
                {
                  onSuccess: res => {
                    modalSetter(false, '');
                    showNotification({
                      type: 'success',
                      message: 'Attachments updated successfully',
                    });
                    clearRequisitionItemsStore();
                    setRemovedAttachments(null);
                  },
                },
              );
            }

            await submitRequisition(formData, {
              onSuccess: () => {
                modalSetter(false, '');
                showNotification({
                  type: 'success',
                  message: 'Requisition submitted successfully',
                });
                clearRequisitionItemsStore();
                navigate('/app/dashboard');
              },
            });
          } catch (error) {
            const errorMessage =
              error?.response?.data?.message || error?.message;
            showNotification({
              type: 'error',
              message: errorMessage,
            });
            setIsConfirmModalOpen(false);
          }
        };

        if (submitType === 'draft') {
          if (requisition?.id) {
            // Add removed attachments data
            removedAttachments.forEach(removedAttachment => {
              formData.append(
                `${removedAttachment.formKey}[id]`,
                removedAttachment.id,
              );
              formData.append(
                `${removedAttachment.formKey}[path]`,
                removedAttachment.path,
              );
            });
            await updateRequisition(
              { id, requisitionData: formData },
              {
                onSuccess: res => {
                  modalSetter(false, '');
                  showNotification({
                    type: 'success',
                    message: 'Requisition draft updated successfully',
                  });
                  clearRequisitionItemsStore();
                  navigate('/app/dashboard');
                },
              },
            );
          } else {
            await createRequisition(formData, {
              onSuccess: res => {
                modalSetter(false, '');
                showNotification({
                  type: 'success',
                  message: 'Draft created successfully',
                });
                clearRequisitionItemsStore();
                navigate(`/app/dashboard/create/${res?.data?.requisition?.id}`);
              },
            });
          }
        } else if (submitType === 'submit') {
          handleRequisitionSubmission(formData);
        }
      } catch (error) {
        const errorMessage = error?.response?.data?.message || error?.message;
        showNotification({
          type: 'error',
          message: errorMessage,
        });
        setIsConfirmModalOpen(false);
      }
    }
  };

  const handleFormChange = values => {
    setRSType(values?.type);
    setChargeToCategory(values?.chargeTo);
  };

  const modalSetter = (isOpen, mode) => {
    setModalData({
      isOpen: isOpen,
      mode: mode,
    });
  };

  const DraftDetails = () => {
    return (
      <>
        {isRequestor && (
          <Button
            className="w-full xl:w-fit mt-2 xl:mt-0"
            variant="outline"
            hover="outline"
            onClick={() => {
              modalSetter(true, 'draft');
              setSubmitType('draft');
            }}
            isLoading={isRequisitionFetching}
            // disabled={!isFormValid}
          >
            Save Draft
          </Button>
        )}
        {requisition && (
          <div className="flex flex-wrap gap-2 xl:space-x-2">
            <span className="font-bold text-[#219653]">Draft Saved:</span>
            <span>
              {requisition?.updatedAt
                ? formatDateToDMY(requisition?.updatedAt)
                : 'N/A'}
            </span>
            <span className="xl:inline">|</span>
            <span>
              {requisition?.updatedAt
                ? formatDateToLocalTime(requisition?.updatedAt)
                : 'N/A'}
            </span>
          </div>
        )}
      </>
    );
  };

  const addItemsToTable = values => {
    const itemsWithSteelbars = values.filter(item => item.isSteelbars === true);

    const itemsWithoutSteelbars = values.filter(item => !item.isSteelbars);

    if (itemsWithSteelbars?.length > 0) {
      addSteelbarItem(itemsWithSteelbars);
    }

    if (itemsWithoutSteelbars?.length > 0) {
      addRequisitionItem(itemsWithoutSteelbars);
    }

    showNotification({
      type: 'success',
      message: 'Items updated successfully',
    });

    modalSetter(false, '');
  };

  useEffect(() => {
    if (steelbarItems.length / 10 < currentPage) {
      if (currentPage === 1) {
        return;
      } else {
        setPage(currentPage - 1);
      }
    }

    if (requisitionItems.length / 10 < currentPage) {
      if (currentPage === 1) {
        return;
      } else {
        setPage(currentPage - 1);
      }
    }
  }, [steelbarItems.length, requisitionItems.length]);

  if (isRequisitionFetching) return <div>Loading...</div>;

  return (
    <>
      <div className="flex items-center p-2 border-b mb-10 text-lg font-bold">
        {requisition?.rsNumber ? (
          <p>
            R.S. Number <span className="text-[#F2994A]">(draft)</span>:
            <span className="ml-4">{requisition?.rsNumber}</span>
          </p>
        ) : (
          <p>R.S. Number : --- </p>
        )}
      </div>
      <Form
        onSubmit={handleSaveAsDraft}
        hasErrorSpace={false}
        ref={mainFormRef}
        options={{
          shouldUnregister: true,
          defaultValues: {
            chargeTo: requisition?.chargeTo ?? '',
            chargeToId: requisition?.chargeToId ?? '',
            category: requisition?.category ?? '',
            type: requisition?.type ?? '',
            dateRequired: setDefaultDateValue(requisition?.dateRequired) || '',
            companyId: requisition?.company?.id ?? '',
            comment: requisition?.comment?.data?.[0]?.note || '',
            projectId: requisition?.project?.id ?? '',
            departmentId: requisition?.department?.id
              ? requisition?.department?.id
              : user?.department?.id,
            deliverTo: requisition?.deliveryAddress ?? '',
            purpose: requisition?.purpose ?? '',
          },
        }}
        watchfields={['type', 'chargeTo', 'dateRequired', 'chargeToId', 'type']}
        onChange={handleFormChange}
      >
        {({ control, register, watch, setValue }) => {
          const companyData = watch('companyId');
          const projectData = watch('projectId');
          const categoryData = watch('category');
          const typeData = watch('type');
          return (
            <div className="flex flex-col gap-4">
              <div className="bg-white p-4 rounded-lg">
                <p className="font-bold text-lg">Request Details</p>
                <hr className="my-4" />
                <div className="flex flex-col space-y-6">
                  <div className="grid grid-cols-2 gap-4 flex-1 flex-grow relative">
                    <Select
                      label="Category"
                      name="category"
                      control={control}
                      {...register('category')}
                      options={optionsData?.categories}
                      defaultValue={requisition?.category}
                      disabled={!isRequestor}
                      onChange={(value, _) => {
                        const categoryReset = () => {
                          setValue('companyId', '');
                          setValue('projectId', '');
                          setValue('deliverTo', '');
                        };

                        if (value === 'project') {
                          categoryReset();
                          setOptionsData(prevData => ({
                            ...prevData,
                            companies: requisitionOptions?.companies?.map(
                              company => ({
                                key: company?.initial + ' - ' + company?.name,
                                value: company?.id,
                                ...company,
                              }),
                            ),
                          }));
                        }
                        if (value === 'company') {
                          categoryReset();
                          setOptionsData(prevData => ({
                            ...prevData,
                            companies: requisitionOptions?.companies?.map(
                              company => ({
                                key: company?.initial + ' - ' + company?.name,
                                value: company?.id,
                                ...company,
                              }),
                            ),
                            projects: requisitionOptions?.projects?.map(
                              project => ({
                                key: project?.name,
                                value: project?.id,
                                ...project,
                              }),
                            ),
                          }));
                        }
                        if (value === 'association') {
                          categoryReset();
                          setOptionsData(prevData => ({
                            ...prevData,
                            companies: requisitionOptions?.companies
                              ?.filter(data => data.category === 'association')
                              ?.map(company => ({
                                key: company?.initial + ' - ' + company?.name,
                                value: company?.id,
                                ...company,
                              })),
                          }));
                        }

                        setSelectedCategory(value);
                      }}
                    />
                    <Select
                      label="Type of Request"
                      searchable
                      name="type"
                      control={control}
                      {...register('type')}
                      options={requestTypes}
                      defaultValue={requisition?.type}
                      disabled={!isRequestor}
                    />
                    <Select
                      searchable
                      label="Company"
                      name="companyId"
                      control={control}
                      {...register('companyId')}
                      options={optionsData?.companies}
                      disabled={!isRequestor}
                    />
                    <Select
                      searchable
                      label="Project"
                      name="projectId"
                      control={control}
                      {...register('projectId')}
                      options={optionsData?.projects}
                      disabled={!isRequestor}
                      onChange={(value, _) => {
                        const address = optionsData?.projects?.find(
                          project => project?.id === value,
                        )?.address;
                        setValue('deliverTo', address);

                        const data = optionsData?.projects?.filter(
                          project => project?.id === value,
                        )[0]?.company;

                        const company = requisitionOptions?.companies?.find(
                          company => company?.name === data?.name,
                        );

                        const mainCompany = {
                          key: company?.initial + ' - ' + company?.name,
                          value: data?.id,
                        };

                        if (categoryData === 'project') {
                          setOptionsData(prevData => ({
                            ...prevData,
                            companies: [mainCompany],
                          }));
                        }

                        if (categoryData === 'project') {
                          setValue('companyId', mainCompany?.value);
                        }
                      }}
                    />

                    <Select
                      searchable
                      label="Department"
                      name="departmentId"
                      control={control}
                      {...register('departmentId')}
                      options={optionsData?.departments}
                      disabled={!isRequestor}
                    />
                    <Select
                      label="Deliver to"
                      name="deliverTo"
                      control={control}
                      searchable
                      {...register('deliverTo')}
                      options={
                        companyData && projectData
                          ? [
                              ...optionsData?.deliverTo,
                              ...optionsData?.projects?.map(project => ({
                                key: project?.address,
                                value: project?.address,
                              })),
                            ]
                          : optionsData?.deliverTo
                      }
                      disabled={!isRequestor}
                    />
                    <Input
                      control={control}
                      {...register('dateRequired')}
                      name="dateRequired"
                      type="date"
                      label="Date Required"
                      minDate={setDefaultDateValue(new Date())}
                      disabled={!isRequestor}
                    />

                    <Input
                      type="text"
                      name="purpose"
                      label="Purpose"
                      control={control}
                      {...register('purpose')}
                      disabled={!isRequestor}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg">
                <p className="font-bold text-lg">
                  Charge To{' '}
                  <sup className="font-normal font-sm opacity-[0.5] italic">
                    (optional)
                  </sup>{' '}
                </p>
                <hr className="my-4" />
                <div className="flex flex-col space-y-6">
                  <div className="grid grid-cols-2 gap-4 flex-1 flex-grow relative">
                    <Select
                      label={
                        <>
                          Charge to (Category) <i>(optional)</i>
                        </>
                      }
                      name="chargeTo"
                      control={control}
                      {...register('chargeTo')}
                      options={chargeToCategories}
                      disabled={!isRequestor}
                      searchable
                      onChange={(value, fieldName) => {
                        if (value === 'company') {
                          setValue('chargeToId');
                        }
                      }}
                    />
                    <Select
                      searchable
                      label={
                        <>
                          Charge to (Client)
                          <i>(optional)</i>
                        </>
                      }
                      name="chargeToId"
                      control={control}
                      {...register('chargeToId')}
                      options={chargeToList}
                      disabled={!isRequestor}
                      onChange={(value, fieldName) => {
                        // Only autofill when explicitly selecting a client
                        if (fieldName === 'chargeToId' && value) {
                          const currentCategory = watch('chargeTo');
                          if (currentCategory === 'company') {
                            setValue('companyId', value);
                          } else if (currentCategory === 'project') {
                            setValue('projectId', value);
                            const selectedProject = projects?.data?.find(
                              project => String(project.id) === String(value),
                            );
                            if (selectedProject?.company?.id) {
                              setValue('companyId', selectedProject.company.id);
                            }
                          }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className=" p-2 ">
                <div className="flex justify-between items-center my-4">
                  <p className="text-lg font-bold m-0"> Item/s</p>
                  <div className="my-2 flex gap-2">
                    {!transferFormVisible && RSType !== 'transfer' && (
                      <Button
                        iconPosition="L"
                        className="w-fit self-end"
                        variant="submit"
                        type="button"
                        icon={PlusIcon}
                        onClick={() => modalSetter(true, RSType)}
                        disabled={RSType === '' || !isRequestor}
                      >
                        Add Item/s
                      </Button>
                    )}
                  </div>
                </div>

                {steelbarItems?.length && requisitionItems?.length ? (
                  <div className="my-6">
                    <Tabs activeTab={activeTab} tabs={tabs} />
                  </div>
                ) : null}

                {steelbarItems?.length || requisitionItems?.length ? (
                  <>
                    {activeTab === 'items' ? (
                      <CreateRSItemsTable
                        status="draft"
                        isRequestor={isRequestor}
                        tableData={requisitionItems}
                        type={RSType}
                        isEditMode={isRequestor}
                        onCancel={() => {}}
                        onSave={() => {}}
                        onNoteChange={(id, value) =>
                          updateRequisitionNote(id, value)
                        }
                        onQuantityChange={(id, value) =>
                          updateRequisitionQuantity(id, value)
                        }
                        setPage={setPage}
                        setLimit={setLimit}
                        setSort={setSort}
                        currentSort={currentSort}
                        onRemove={(id, _) => {
                          removeRequisitionItem(id);
                        }}
                      />
                    ) : (
                      <SteelbarsTable
                        tableData={steelbarItems}
                        isEditMode={isRequestor}
                        onQuantityChange={(id, value) =>
                          updateSteelbarQuantity(id, value)
                        }
                        onNoteChange={(id, value) =>
                          updateSteelbarNote(id, value)
                        }
                        setPage={setPage}
                        setLimit={setLimit}
                        setSort={setSort}
                        currentSort={currentSort}
                        onRemove={id => {
                          if (steelbarItems.length < 2) {
                            setActiveTab('items');
                          }
                          removeSteelbarItem(id);
                        }}
                      />
                    )}
                  </>
                ) : (
                  <NoItems
                    onAddItems={() => modalSetter(true, RSType)}
                    buttonDisabled={RSType === '' || !isRequestor}
                  />
                )}
              </div>

              <div className="bg-white p-4 rounded-lg">
                <p className="font-bold text-lg">Attachments and Notes</p>
                <hr className="my-4" />
                <div className="grid grid-cols-2 gap-x-6 gap-y-4">
                  <div className="flex flex-col">
                    <File
                      isUploading={isUploading}
                      ref={fileRef}
                      control={control}
                      name="attachments"
                      attachments={requisition?.attachments?.data}
                      hasAttachments={setHasAttachments}
                      disabled={!isRequestor}
                    />

                    {isUploading && (
                      <div className="flex items-center">
                        <div className="text-sm mr-2 font-semibold">
                          {uploadProgress}%
                        </div>
                        <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
                          <div
                            className="bg-blue-500 h-full transition-all duration-200 ease-out"
                            style={{ width: `${uploadProgress}%` }}
                          />
                        </div>
                      </div>
                    )}
                    <div className="flex flex-col gap-2 bg-gray-50 mb-2 rounded-lg shadow-sm max-h-36 overflow-y-auto">
                      {attachments &&
                        attachments?.map(({ id, modelId, fileName }, index) => (
                          <div
                            key={`${modelId}-${index}`}
                            className="flex w-full"
                          >
                            <div className="w-full">
                              <Input
                                id={id}
                                name={`${fileName}.${index}`}
                                type="text"
                                value={fileName}
                                readOnly
                                disabled
                                className="w-full relative disabled:bg-gray-50 disabled:opacity-80 disabled:shadow-none text-black font-normal border-0"
                                hasIcon={isRequestor}
                                renderIcon={XIcon}
                                iconHandler={() => {
                                  removeAttachment(id, attachments[index]);
                                }}
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                    <div className="flex justify-between align-middle">
                      <span className="text-[#4F4F4F] text-[12px]">
                        The maximum size for each file is 25 MB. File formats -
                        PNG, JPG, JPEG, PDF, Excel, CSV.
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col flex-grow">
                    <TextArea
                      label="Notes"
                      name="comment"
                      control={control}
                      placeholder="Input notes here"
                      disabled={!isRequestor}
                      maxCharacters={100}
                    />
                  </div>
                </div>
              </div>
            </div>
          );
        }}
      </Form>
      <hr className="my-6" />
      <div className="flex justify-between items-center ">
        <div className="flex gap-4 items-center text-sm">
          <DraftDetails />
        </div>

        {isRequestor && (
          <div className="flex items-center gap-2">
            <Button
              className="w-full xl:w-fit mt-2 xl:mt-0"
              variant="outline"
              hover="outline"
              onClick={() => modalSetter(true, 'cancel')}
              isLoading={isRequisitionFetching}
              // disabled={!isFormValid}
            >
              Cancel
            </Button>
            {/* {id ? ( */}
            <Button
              className="w-full xl:w-fit mt-2 xl:mt-0"
              variant="submit"
              size="lg"
              onClick={() => {
                modalSetter(true, 'submit');
                setSubmitType('submit');
              }}
              isLoading={isRequisitionFetching}
              // disabled={!isFormValid}
            >
              Submit
            </Button>
            {/* ) : null} */}
          </div>
        )}
      </div>
      <div className="flex flex-col sm:flex-row sm:justify-end sm:items-center gap-4 mb-4 sm:absolute sm:top-[34px] sm:right-4 text-sm">
        <div className="flex flex-col xl:flex-row xl:items-center xl:space-x-4">
          <DraftDetails />
        </div>
      </div>

      <AddItemModal
        data={OFMListItems}
        onClose={() => {
          modalSetter(false, '');
        }}
        onAddItems={values => {
          addItemsToTable(values);
        }}
        isOpen={
          modalData?.isOpen &&
          (modalData?.mode === 'ofm' || modalData?.mode === 'ofm-tom')
        }
      />
      <NonOFMTRansferModal
        data={nonOfmItems}
        onClose={() => {
          modalSetter(false, '');
        }}
        nonOfmDraftList={requisitionItems?.reduce((acc, item) => {
          acc.push(item?.itemName);
          return acc;
        }, [])}
        onAddItems={values => {
          addRequisitionItem(values);
          modalSetter(false, '');
        }}
        isOpen={
          modalData?.isOpen &&
          (modalData?.mode === 'non-ofm' || modalData?.mode === 'non-ofm-tom')
        }
      />
      <ConfirmModal
        isOpen={modalData?.isOpen && modalData?.mode === 'draft'}
        onClose={closeModal}
        onConfirm={() => mainFormRef.current?.requestSubmit()}
        header="Save Draft"
        message="You are about to save this request as a draft. Make sure all items are correct. Press save if you want to proceed with this action."
        confirmButtonText="Save"
        isLoading={isCreatingRequisition}
      />
      <Modal
        size="small"
        header="Submit Requisition Slip"
        onClose={closeModal}
        isOpen={modalData?.isOpen && modalData?.mode === 'submit'}
      >
        <span className="my-4 text-sm">
          You are about to submit this request. Make sure all items are correct.
          Press submit if you want to proceed with this action.
        </span>
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            hover="outline"
            onClick={() => modalSetter(false, '')}
          >
            Cancel
          </Button>
          <Button
            variant="submit"
            isLoading={isSubmittingRequisition}
            disabled={isSubmittingRequisition}
            // onClick={handleSaveAsDraft}
            onClick={() => mainFormRef.current?.requestSubmit()}
          >
            Submit
          </Button>
        </div>
      </Modal>
      <Modal
        size="small"
        header="Cancel Requisition Slip"
        onClose={closeModal}
        isOpen={modalData?.isOpen && modalData?.mode === 'cancel'}
      >
        <span className="my-4 text-sm">
          You are about to cancel this request. Press continue if you want to
          proceed with this action.
        </span>
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            hover="outline"
            onClick={() => modalSetter(false, '')}
          >
            Cancel
          </Button>
          <Button
            variant="submit"
            onClick={() => {
              modalSetter(false, '');
              navigate('/app/dashboard');
            }}
          >
            Continue
          </Button>
        </div>
      </Modal>
    </>
  );
};

export { CreateRS };
