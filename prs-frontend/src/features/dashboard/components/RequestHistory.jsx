import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Table, Pagination } from '@components/ui/Table';
import { Tabs } from '@components/ui/Tabs';
import { SearchBar } from '@components/ui/SearchBar/SearchBar';
import { useGetRequestHistory, useGetRequisitionSlip } from '../api';
import { formatDateToDMY } from '@utils/dateFormat';
import { remapStatus } from '@src/utils/remap';
import { Status } from './Status';
import { emojiRegex } from '@src/utils/regexUtils';

export const RequestHistory = ({
  currentPage,
  currentLimit,
  setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
}) => {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState('canvass');
  const [searchQuery, setSearchQuery] = useState({});
  const [searchError, setSearchError] = useState('');

  const handleTabChange = key => {
    setActiveTab(key);
  };
  const searchItems = {
    invoice: [
      {
        name: 'irNumber',
        type: 'text',
        placeholder: 'Search Invoice Number, Supplier Invoice No.',
        isParent: true,
        label: 'Search',
      },
    ],
  }

  let currentSearchValue;
  const currentTabSearchConfig = searchItems[activeTab];

  if (currentTabSearchConfig && currentTabSearchConfig.length > 0) {
    const searchKeyForApi = currentTabSearchConfig[0].name;
    if (searchQuery.hasOwnProperty(searchKeyForApi)) {
      currentSearchValue = searchQuery[searchKeyForApi];
    }
  }

  const { data: requestHistory, isLoading } = useGetRequestHistory({
    requisitionId: id,
    page: currentPage,
    limit: currentLimit,
    sortBy,
    type: activeTab,
    search: currentSearchValue,
  });

  const { data: requisition } = useGetRequisitionSlip(id);

  const tableHeaders = {
    canvass: [
      { key: 'canvassNumber', value: 'Canvass No.' },
      { key: 'item', value: 'Item' },
      { key: 'supplier', value: 'Supplier' },
      { key: 'price', value: 'Price' },
      { key: 'discount', value: 'Discount' },
      { key: 'canvassDate', value: 'Canvass Date' },
      { key: 'status', value: 'Status' },
    ],
    order: [
      { key: 'poNumber', value: 'PO Number' },
      { key: 'supplier', value: 'Supplier' },
      { key: 'poPrice', value: 'Purchase Order Price' },
      { key: 'dateOrdered', value: 'Date Ordered' },
      { key: 'status', value: 'Status' },
    ],
    delivery: [
      { key: 'drNumber', value: 'Delivery Receipt Number' },
      { key: 'supplier', value: 'Supplier' },
      { key: 'dateOrdered', value: 'Date Ordered' },
      { key: 'quantityOrdered', value: 'Quantity Ordered' },
      { key: 'dateDelivered', value: 'Date Delivered' },
      { key: 'quantityDelivered', value: 'Quantity Delivered' },
      { key: 'status', value: 'Status' },
    ],
    invoice: [
      { key: 'irNumber', value: 'Invoice Number' },
      { key: 'supplierInvoiceNo', value: 'Supplier Invoice No.' },
      { key: 'issuedInvoiceDate', value: 'Supplier Invoice Issued Date' },
      { key: 'invoiceAmount', value: 'Supplier Invoice Amount' },
      { key: 'updatedAt', value: 'Last Updated' },
      { key: 'status', value: 'Status' },
    ],
    payment: [
      { key: 'prNumber', value: 'Payment Req No.' },
      { key: 'supplier', value: 'Supplier' },
      { key: 'amount', value: 'Amount' },
      { key: 'status', value: 'Status' },
    ],
    return: [
      { key: 'drNumber', value: 'Delivery Receipt Number' },
      { key: 'item', value: 'Item' },
      { key: 'supplier', value: 'Supplier' },
      { key: 'quantityOrdered', value: 'Quantity Ordered' },
      { key: 'quantityReturned', value: 'Quantity Returned' },
      { key: 'returnDate', value: 'Return Date' },
      { key: 'status', value: 'Status' },
    ],
    item: [
      { key: 'item', value: 'Item Name' },
      { key: 'quantityRequested', value: 'Quantity Requested' },
      { key: 'quantityOrdered', value: 'Quantity Ordered' },
      { key: 'quantityDelivered', value: 'Quantity Delivered' },
      { key: '', value: 'Last Updated By' },
    ],
  };

  const tdDesign = {
    supplier: {
      render: value => <p className='text-wrap break-all'>{value?.supplier}</p>
    },
    discount: {
      render: value => <p>{Number(value?.discount).toFixed(2)}</p>
    },
    price: {
      render: value => <p>{Number(value?.price).toFixed(2)}</p>
    },
    item: {
      render: value => <p className='text-wrap break-all'>{value?.item}</p>
    },
    canvassDate: {
      render: value => <p>{formatDateToDMY(value.canvassDate)}</p>,
    },
    dateOrdered: {
      render: value => <p>{formatDateToDMY(value.dateOrdered)}</p>,
    },
    dateDelivered: {
      render: value => <p>{formatDateToDMY(value.dateDelivered)}</p>,
    },
    issuedInvoiceDate: {
      render: value => <p>{formatDateToDMY(value.issuedInvoiceDate)}</p>,
    },
    updatedAt: {
      render: value => <p>{formatDateToDMY(value.updatedAt)}</p>,
    },
    returnDate: {
      render: value => <p>{formatDateToDMY(value.returnDate)}</p>,
    },
    quantityOrdered: {
      render: value => <p>{parseFloat(value.quantityOrdered).toFixed(3)}</p>,
    },
    quantityDelivered: {
      render: value => <p>{parseFloat(value.quantityDelivered).toFixed(3)}</p>,
    },
    status: {
      render: value => <Status status={activeTab === 'canvass' ? remapStatus(value.status) : value.status} />
    }
  };

  const tabs = [
    {
      key: 'canvass',
      value: 'Canvasses',
      onClick: () => handleTabChange('canvass'),
    },
    { key: 'order', value: 'Orders', onClick: () => handleTabChange('order') },
    {
      key: 'delivery',
      value: 'Deliveries',
      onClick: () => handleTabChange('delivery'),
    },
    {
      key: 'invoice',
      value: 'Invoices',
      onClick: () => handleTabChange('invoice'),
    },
    {
      key: 'payment',
      value: 'Payments',
      onClick: () => handleTabChange('payment'),
    },
    {
      key: 'return',
      value: 'Returns',
      onClick: () => handleTabChange('return'),
    },
    { key: 'item', value: 'Items', onClick: () => handleTabChange('item') },
  ];

  const handleSearch = (val) => {
    const searchValue = val[searchItems[activeTab][0].name];
    
    if (emojiRegex.test(searchValue)) {
      setSearchError('Emojis are not allowed in search');
      return;
    }
    
    const newSearchQuery = { ...val };
    if (newSearchQuery.hasOwnProperty('search') && newSearchQuery.search === '') {
      delete newSearchQuery.search;
    }
    setSearchQuery(newSearchQuery);
    setSearchError('');
  };

  return (
    <div className="flex flex-col gap-4">
      <div>
        <span className="font-bold text-lg">R.S. Number:</span>{' '}
        {requisition?.rsNumber}
      </div>
      <Tabs tabs={tabs} />
      {activeTab === 'invoice' && (
        <SearchBar
          searchItems={searchItems[activeTab]}
          onSearch={handleSearch}
          setPage={setPage}
          className="w-full pl-0 pr-0 bg-transparent"
          error={searchError}
          validateInput={(value) => emojiRegex.test(value)}
        />
      )}
      <Pagination
        total={requestHistory?.total}
        setPage={setPage}
        setLimit={setLimit}
        hasLimit={false}
      >
        <Table
          headers={tableHeaders[activeTab] || tableHeaders.canvass}
          data={requestHistory?.data}
          tdDesign={tdDesign}
          isLoading={isLoading}
          onSort={setSort}
          currentSort={currentSort}
        />
      </Pagination>
    </div>
  );
};
