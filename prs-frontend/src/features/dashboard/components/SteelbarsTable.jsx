import React, { useState, useEffect } from 'react';
import { Table, Pagination } from '@components/ui/Table';
import { Button } from '@src/components/ui/Button';
import TrashIcon from '@assets/icons/trash_new.svg?react';
import { useSearchParams } from 'react-router-dom';
import { Input } from '@src/components/ui/Form';

export const SteelbarsTable = ({
  isEditMode,
  notesHidden,
  isForApprover,
  onRemove,
  onQuantityChange,
  onNoteChange,
  type,
  tableData,
  module,
  setLimit,
  setSort,
  tdDesign = {
    grade: {
      render: row => (
        <span>
          {row.steelbars?.grade || row.steelbarDetails?.grade || '---'}
        </span>
      ),
    },
    itemCd: {
      render: row => (
        <Input type="text" value={row.itemCd ? row.itemCd : row.itemName} />
      ),
    },
    itmDes: {
      render: row => (
        <div className="bg-gray-200 p-2 rounded-md opacity-50 text-gray-600 cursor-not-allowed">
          <span className="text-wrap ">
            {row.itmDes ? row.itmDes : row.itemName}
          </span>
        </div>
      ),
    },
    acctCd: {
      render: row => <span>{row.acctCd ? row.acctCd : row.accountCode}</span>,
    },
    diameter: {
      render: row => (
        <Input
          disabled
          className="text-center"
          type="text"
          value={
            row.steelbars?.diameter || row.steelbarDetails?.diameter || '---'
          }
        />
      ),
    },
    length: {
      render: row => (
        <Input
          disabled
          className="text-center"
          type="text"
          value={row.steelbars?.length || row.steelbarDetails?.length || '---'}
        />
      ),
    },
    weight: {
      render: row => (
        <Input
          disabled
          className="text-center"
          type="text"
          value={row.steelbars?.weight || row.steelbarDetails?.weight || '---'}
        />
      ),
    },
    unit: {
      render: row => (
        <Input
          disabled
          className="text-center"
          type="text"
          value={row?.unit || '---'}
        />
      ),
    },
    remainingGfq: {
      render: row => (
        <Input
          disabled
          className="text-center"
          type="text"
          value={row?.remainingGfq || '---'}
        />
      ),
    },
    notes: {
      render: row =>
        notesHidden ? (
          <span>{row.notes}</span>
        ) : (
          <Input
            type="text"
            value={row.notes}
            maxLength={100}
            onChange={e => onNoteChange(row.acctCd, e.target.value)}
          />
        ),
    },
    quantity: {
      render: row => {
        return (
          <>
            {isEditMode ? (
              <Input
                maxNumberLength={3}
                min="0"
                step={0.001}
                type="number"
                value={row?.quantity || ''}
                onChange={e => onQuantityChange(row.id, Number(e.target.value))}
              />
            ) : row.quantity ? (
              row.quantity
            ) : (
              row.quantityRequested
            )}
          </>
        );
      },
    },
    remove: {
      render: row => (
        <Button
          variant="icon"
          hover="submit"
          icon={TrashIcon}
          iconSize="ml-0"
          className="bg-[#fce6e6] hover:bg-[#f49c9c]"
          disabled={!isEditMode || (isForApprover && tableData?.length === 1)}
          onClick={() => onRemove(row.id)}
        />
      ),
    },
  },
  isPOCustomStyle = false,
}) => {
  const [currentSort, setCurrentSort] = useState([]);
  const [filteredData, setFilteredData] = useState(tableData);
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    if (currentSort.length) {
      const data = [...tableData];
      data.sort((a, b) => {
        if (currentSort[0][1] === 'ASC') {
          if (
            currentSort[0][0] === 'diameter' ||
            currentSort[0][0] === 'length' ||
            currentSort[0][0] === 'weight' ||
            currentSort[0][0] === 'quantity' ||
            currentSort[0][0] === 'remainingGfq'
          ) {
            return (
              parseFloat(a[currentSort[0][0]]) -
              parseFloat(b[currentSort[0][0]])
            );
          }

          return a[currentSort[0][0]]
            .toString()
            .toLowerCase()
            ?.localeCompare(b[currentSort[0][0]].toString().toLowerCase());
        }

        if (
          currentSort[0][0] === 'diameter' ||
          currentSort[0][0] === 'length' ||
          currentSort[0][0] === 'weight' ||
          currentSort[0][0] === 'quantity' ||
          currentSort[0][0] === 'remainingGfq'
        ) {
          return (
            parseFloat(b[currentSort[0][0]]) - parseFloat(a[currentSort[0][0]])
          );
        }

        return b[currentSort[0][0]]
          .toString()
          .toLowerCase()
          ?.localeCompare(a[currentSort[0][0]].toString().toLowerCase());
      });
      setFilteredData(data);
    } else {
      setFilteredData(tableData);
    }
  }, [currentSort]);

  useEffect(() => {
    if (tableData) {
      const data = [...tableData];
      setFilteredData(data);
    }
  }, [tableData]);

  const setPage = page => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', page.toString());
      return newParams;
    });
  };

  const cHeaders = [
    { key: 'quantity', value: 'Qty/Pcs', css: 'w-100' },
    isEditMode || isForApprover
      ? {
          key: 'remove',
          value: 'Action',
          css: 'w-[40px]',
          hasNoSort: true,
        }
      : null,
  ].filter(Boolean);

  const headers = [
    { key: 'itmDes', value: 'Item', css: 'max-w-full min-w-[600px]' },
    { key: 'unit', value: 'Unit', css: 'w-[100px]' },
    { key: 'remainingGfq', value: 'Remaining GFQ', css: 'w-[180px]' },
    { key: 'diameter', value: 'Dia(mm)', css: 'w-[100px]' },
    { key: 'length', value: 'Length(mm)', css: 'w-[100px]' },
    { key: 'weight', value: 'Weight(kg/m)', css: 'w-[100px]' },
    ...(isEditMode
      ? cHeaders
      : [
          {
            key: 'quantity',
            value:
              module === 'requisition-slip' || module === 'canvassing'
                ? 'Qty/Pcs'
                : 'Qty',
            css: 'w-[50px]',
          },
        ]),
  ];

  const baseHeaders = headers;

  const tdDesignControls = tdDesign;

  const transformTableData = filteredData.map(item => {
    return { ...item, remainingGfq: parseFloat(item?.remainingGfq).toFixed(3) };
  });

  const handleSort = field => {
    setCurrentSort(prev => {
      const direction = prev[0]?.[1] === 'ASC' ? 'DESC' : 'ASC';

      if (prev[0]?.[1] === 'DESC') return [];

      return [[field, direction]];
    });
  };

  //tdDesigns
  return (
    <Pagination total={filteredData?.length} setPage={setPage} asHOC={true}>
      {({ currentPage, limit }) => (
        <>
          {isPOCustomStyle && (
            <div className="text-[#EB5757] text-center bg-[#FFE2E2] border border-[#CED4DA] mb-6 py-1">
              {
                'The quantity requested and ordered does not match. Please check the highlighted items.'
              }
            </div>
          )}
          <Table
            headers={baseHeaders}
            data={transformTableData}
            tdDesign={tdDesignControls}
            onSort={handleSort}
            currentSort={currentSort}
            page={currentPage}
            limit={limit}
            POtableCustomStyle={isPOCustomStyle}
          />
        </>
      )}
    </Pagination>
  );
};
