import React, { useState, useEffect, useRef } from 'react';
import { Modal } from '@src/components/ui/Modal';
import { Input } from '@src/components/ui/Form';
import { Button } from '@src/components/ui/Button';

export const NonOFMTRansferModal = ({
  isOpen,
  status,
  data,
  onClose,
  onAddItems,
  nonOfmDraftList,
}) => {
  const searchRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState(nonOfmDraftList || []);
  const [disabledItems, setDisabledItems] = useState([]);
  const itemsData = data ? data?.data : [];
  const items = itemsData?.map(itm => itm?.itemName);
  // to synchronize whenever it gets cleared

  useEffect(() => {
    if (nonOfmDraftList) {
      if (status === 'assigned') {
        const filteredForAssigned = items.filter(item =>
          nonOfmDraftList.includes(item),
        );

        setSelectedItems([]);
        setDisabledItems(filteredForAssigned);
      } else {
        setSelectedItems(nonOfmDraftList);
      }
    }
  }, [nonOfmDraftList]);

  const toggleSelection = item => {
    if (selectedItems.includes(item)) {
      setSelectedItems(selectedItems.filter(selected => selected !== item));
    } else {
      setSelectedItems([...selectedItems, item]);
    }
  };
  const clearSelection = () => {
    setSelectedItems([]);
  };

  const filteredItems =
    status === 'assigned'
      ? items.filter(item => !nonOfmDraftList.includes(item))
      : items.filter(item =>
          item?.toLowerCase().includes(searchTerm.toLowerCase()),
        );

  const handleAddItems = () => {
    const filtered = itemsData?.filter(item =>
      selectedItems.some(name => item.itemName === name),
    );

    const itemsToBeAdded = filtered.map(item => ({
      ...item,
      notes: '',
    }));

    onAddItems(itemsToBeAdded);
    onClose();
  };

  const handleClose = () => {
    clearSelection();
    onClose();
    setSearchTerm('');
    searchRef.current.value = '';
  };

  return (
    <Modal
      size="medium"
      header="Add Items"
      onClose={handleClose}
      isOpen={isOpen}
    >
      {/* Instructions */}
      <div className="p-1">
        <p className="text-sm text-gray-600 mb-4">
          Please select the Item/s from the list below. Make sure all items
          selected are correct. Press Add Item/s to continue.
        </p>

        {/* Search */}
        <div className="flex items-center w-full gap-2 mb-4">
          <div className="flex-1">
            <Input
              ref={searchRef}
              type="text"
              isSearch={true}
              placeholder="Input List Name"
            />
          </div>
          <Button
            onClick={() => {
              setSearchTerm(searchRef?.current?.value);
            }}
            className="w-24"
            hover="action"
            variant="action"
          >
            Search
          </Button>
          <Button
            onClick={() => {
              searchRef.current.value = '';
              setSearchTerm('');
            }}
            className="w-24"
            hover="outline"
            variant="outline"
            disabled={!searchTerm}
          >
            Clear
          </Button>
        </div>

        {/* Lists */}
        <div className="grid grid-cols-2 gap-4 w-full">
          {/* Items List */}
          <div className="col-span-1 w-full">
            <h3 className="font-semibold mb-2">Items List</h3>
            <div className="border rounded-lg p-3 overflow-y-auto min-h-40 max-h-40 w-full">
              {status === 'assigned' && (
                <>
                  {disabledItems.map(item => (
                    <div
                      key={item}
                      className="flex gap-2 mb-2 text-sm items-start w-full"
                    >
                      <input
                        type="checkbox"
                        checked
                        disabled
                        className="flex-shrink-0 mt-1"
                      />
                      <span className="break-all pr-2 flex-1" title={item}>
                        {item}
                      </span>
                    </div>
                  ))}
                </>
              )}
              {filteredItems.map(item => (
                <div
                  key={item}
                  className="flex gap-2 mb-2 text-sm items-start w-full"
                >
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item)}
                    // disabled={selectedItems.includes(item)}
                    onChange={() => toggleSelection(item)}
                    className="flex-shrink-0 mt-1"
                  />
                  <span className="break-all pr-2 flex-1" title={item}>
                    {item}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Selected Items */}
          <div className="col-span-1 overflow-x-hidden w-full">
            <h3 className="font-semibold mb-2">Selected Item/s</h3>
            <div className="border rounded-lg p-3 overflow-y-auto min-h-40 max-h-40 text-sm">
              {status === 'assigned' && (
                <>
                  {disabledItems.map(item => (
                    <div
                      key={item}
                      className="flex gap-2 mb-2 items-start w-full"
                    >
                      <span className="break-all pr-2 flex-1" title={item}>
                        {item}
                      </span>
                      <button
                        disabled
                        className="text-red-500 hover:text-red-700 flex-shrink-0 mt-1 opacity-[0.5]"
                        onClick={() => toggleSelection(item)}
                      >
                        ✕
                      </button>
                    </div>
                  ))}
                </>
              )}
              {selectedItems.map(item => (
                <div key={item} className="flex gap-2 mb-2 items-start w-full">
                  <span className="break-all pr-2 flex-1" title={item}>
                    {item}
                  </span>
                  <button
                    // disabled={selectedItems.includes(item)}
                    className="text-red-500 hover:text-red-700 flex-shrink-0 mt-1"
                    onClick={() => toggleSelection(item)}
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-end px-4 py-3 border-t border-b">
        <p className="text-sm  text-gray-700">
          Total:{' '}
          <span className="font-semibold">
            {status === 'assigned'
              ? selectedItems.length + disabledItems.length
              : selectedItems.length}
          </span>{' '}
          Items Selected
        </p>
      </div>
      <div className="flex justify-center gap-2">
        <Button variant="outline" hover="outline" onClick={handleClose}>
          Cancel
        </Button>
        <Button
          variant="submit"
          // isLoading={isLoading}
          // disabled={isLoading}
          onClick={handleAddItems}
        >
          Add Item/s
        </Button>
      </div>
    </Modal>
  );
};
