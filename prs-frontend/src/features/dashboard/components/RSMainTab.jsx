import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { Tabs } from '@src/components/ui/Tabs';
import { Form, Input, TextArea, Select } from '@components/ui/Form';
import { SearchBar } from '@components/ui/SearchBar/SearchBar';
import { ConfirmModal, CancelModal, Modal } from '@components/ui/Modal';
import { Button } from '@src/components/ui/Button';
import FileIcon from '@src/assets/icons/file.svg?react';
import CommentIcon from '@src/assets/icons/comment.svg?react';
import PlusIcon from '@assets/icons/plus.svg?react';
import Download from '@assets/icons/download.svg?react';
import { useRequisitionItemsStore } from '@src/store/requisitionItemsStore';
import { capitalizeFirstLetter } from '@src/utils/capitalizeFirstLetter';
import {
  transformRequisitionArray,
  steelbarsItemsExtractor,
} from '@src/utils/itemListSelector';
import {
  useGetRequisitionSlip,
  useGetDeliveryAddress,
  usegetChargeToClientList,
  useGetRequisitionAttachments,
  useGetRequisitionNotes,
  useCancelRequisition,
  useUpdateRequisition,
  useGetRequisitionSlipItems,
  useCreateAttachment,
  useUpdateComment,
  useSubmitRequisition,
  useMarkAsSeen,
  useMarkNotesAsSeen,
} from '../api';

import { useGetNotes } from '@features/notes/api';
import { setDefaultDateValue } from '@src/utils/dateFormat';
import { Pill } from '@src/components/ui/Pill';
import { useNotification } from '@src/hooks/useNotification';
import { useApproverContext } from './ApproverContext';
import { ItemsTable } from './ItemsTable';
import { StatusSection } from './StatusSection';
import { AssignedStaffSection } from './AssignedStaffSection';
import { ApproversSection } from './ApproversSection';
import AttachmentModalContent from '@features/supplier/components/AttachmentModalContent';
import { useGetOFMItems } from '@src/features/ofm/api';
import { CreateRSItemsTable } from './CreateRSItemsTable';
import { SteelbarsTable } from './SteelbarsTable';
// import Printer from '@assets/icons/printer.svg?react';
import RSSavingControls from './RSSavingControls';
import { unitsConfig } from '@config/unitsConfig';
import { itemTypes } from '@config/itemTypesConfig';
import { useDeleteAttachment } from '@src/features/attachments/api';
import { File } from '@src/components/ui/Form';
import { addNonOfmItemSchema } from '@src/schema/non-ofm.schema';
import { useUserStore } from '@store';
import { usedeleteRequisitionItem } from '../api/delete-requisition-item';
import { useGetRequisitionOptions } from '../api/get-requistion-options';
import { useGetNonOfmItems } from '@src/features/non-ofm/api';
import { NonOFMTRansferModal } from './NonOFMTRansferModal';
import { useDownloadTemplatePdf } from '@src/hooks/useDownloadTemplatePdf';
import { Spinner } from '@src/components/ui/Spinner';
export const RSMainTab = ({
  setPage,
  setLimit,
  setSort,
  currentSort,
  isForApprover,
  isRequestor,
}) => {
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isCancelReqModalOpen, setIsCancelReqModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('items');
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isUploading, _] = useState(false);
  const [savedAttachmentValues, setSavedAttachmentValues] = useState(null);
  const [savedNoteValues, setSavedNoteValues] = useState(null);
  const [isConfirmRsSubmit, setIsConfirmRsSubmit] = useState(false);
  const [isConfirmModalNote, setIsConfirmModalNote] = useState(false);
  const [isConfirmModalAttachment, setIsConfirmModalAttachment] =
    useState(false);
  const [, setHasAttachments] = useState(false);
  const [isDeleteAttachmentModalOpen, setIsDeleteAttachmentModalOpen] =
    useState(false);
  const [attachmentToDelete, setAttachmentToDelete] = useState(null);
  const [modalData, setModalData] = useState({
    isOpen: false,
    type: '',
  });
  const [isReEditMode, setIsReEditMode] = useState(false);
  const [deleteItemModalData, setDeleteItemModalData] = useState({
    isOpen: false,
    itemId: '',
  });

  const addItemRef = useRef(null);
  const fileRef = useRef(null);
  const noteRef = useRef(null);
  const mainFormRef = useRef(null);

  const {
    requisitionItems,
    steelbarItems,
    updateRequisitionNote,
    updateRequisitionQuantity,
    removeSteelbarItem,
    removeRequisitionItem,
    clearRequisitionItemsStore,
    removeSubmittedRequisitionItem,
    addRequisitionItem,
    addSubmittedRequisitionItem,
    addSteelbarItem,
    updateSteelbarQuantity,
    updateSteelbarNote,
    setAddItemMode,
    addItemMode,
  } = useRequisitionItemsStore();

  const {
    mutateAsync: updateRequisition,
    error: updateRequisitionError,
    isPending: isUpdatingRequisition,
  } = useUpdateRequisition();

  const { mutateAsync: deleteAttachment } = useDeleteAttachment();
  const { mutateAsync: markNotesSeen } = useMarkNotesAsSeen();

  const {
    mutateAsync: deleteRequisitionItem,
    error: deleteRequisitionItemError,
    isPending: isDeletingRequisitionItem,
  } = usedeleteRequisitionItem();

  const { data: requisitionOptions } = useGetRequisitionOptions({});

  const deliverToOptions = requisitionOptions?.deliverTo?.map(address => ({
    key: address?.address,
    value: address?.address,
    ...address,
  }));

  const getDeliverTo = id => {
    const options = deliverToOptions?.find(item => item.id === Number(id));
    return options?.address;
  };

  const { id } = useParams();

  const [dateData, setDateData] = useState({
    dateFrom: '',
    dateTo: '',
  });

  const {
    data: requisition,
    refetch: refetchRequisition,
    isRefetching: isRequisitionRefetching,
  } = useGetRequisitionSlip(id, {
    enabled: !!id,
  });

  const {
    data: notesData,
    refetch: refetchNotes,
    isFetching: isLoadingNotes,
  } = useGetNotes(
    {
      model: 'requisition',
      modelId: id,
      dateFrom: dateData?.dateFrom,
      dateTo: dateData.dateTo,
    },
    {
      enabled: !!id,
    },
  );

  const { user } = useUserStore();
  const assignedToUser = user.id === requisition?.assignedTo?.id;

  const {
    data: itemsData,
    isLoading: isLoadingItems,
    refetch: refetchItemsData,
  } = useGetRequisitionSlipItems(
    {
      id,
      search: searchQuery,
      steelbars: activeTab === 'steel-bars',
      paginate: false,
    },
    {
      enabled: !!id,
    },
  );

  const { mutate: downloadPdf, isPending: isPdfDownloading } =
    useDownloadTemplatePdf();

  const handleSearch = values => {
    const searchValue = values.search ? values.search.trim() : '';
    setSearchQuery(searchValue);
  };

  const searchItems = [
    {
      name: 'search',
      type: 'text',
      placeholder: 'Search Items',
      isParent: true,
      label: 'Search',
    },
  ];

  const {
    data: attachments,
    refetch: refetchAttachments,
    isFetching: isLoadingAttachments,
  } = useGetRequisitionAttachments(
    {
      id,
      dateFrom: dateData?.dateFrom,
      dateTo: dateData?.dateTo,
    },
    { enabled: !!id },
  );

  const { mutateAsync: markAsSeen } = useMarkAsSeen();

  const {
    mutateAsync: cancelRequisition,
    error: cancelRequisitionError,
    isPending: iScancellingRequisition,
  } = useCancelRequisition();

  const isOFMEnabled = useMemo(
    () => requisition?.type === 'ofm' || requisition?.type === 'ofm-tom',
    [requisition],
  );

  const isPurchasingStaff = user.role.name == 'Purchasing Staff';

  const { data: OFMItems, isLoading: isFetchingOFMItems } = useGetOFMItems({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });

  const { data: nonOfmItems, isFetching: isFetchingNonOFMS } =
    useGetNonOfmItems({
      paginate: false,
      sortBy: { initial: 'ASC' },
    });

  const { data: deliveryAddress } = useGetDeliveryAddress({});
  const deliverTo =
    deliveryAddress &&
    deliveryAddress?.companies?.map(company => ({
      key: company?.address,
      value: company?.address,
    }));

  const {
    data: chargeToClientList,
    ischargeToClientListFetching,
    chargeToClientListerror,
  } = usegetChargeToClientList({
    category: requisition?.chargeTo,
  });
  const chargeToList = chargeToClientList?.result?.data?.map(list => ({
    key: list?.name,
    value: list?.id,
  }));

  const { mutateAsync: createAttachment, isPending: isAttachmentPending } =
    useCreateAttachment({});
  const { mutateAsync: updateComment } = useUpdateComment();

  const handleTabChange = key => {
    setActiveTab(key);
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      return newParams;
    });
  };

  const seenAttachment = async () => {
    await markAsSeen({ id: id, type: modalData?.type });
    if (modalData?.type === 'attachment') {
      refetchAttachments();
    } else {
      refetchNotes();
    }
  };

  const canDownload =
    requisition?.createdByUser?.id === user?.id ||
    user?.role?.name.includes('Purchasing');

  const { requisitionStatus } = useApproverContext();
  const isOFM = requisition?.type === 'ofm' || requisition?.type === 'ofm-tom';
  const isNonOFM =
    requisition?.type === 'non-ofm' || requisition?.type === 'non-ofm-tom';

  let steelbarItemsList = steelbarsItemsExtractor(
    itemsData?.data,
    OFMItems?.data,
    isOFMEnabled,
  ).map(items => {
    return {
      ...items,
      quantity: parseFloat(items?.quantity).toFixed(3) || 0,
    };
  });

  const requisitionItemListDraft = transformRequisitionArray(
    requisition?.requisitionItemLists?.data,
  );

  let itemsWithoutSteelbars = requisitionItemListDraft.filter(
    item => !item.isSteelbars,
  );

  const tabs = [
    {
      key: 'items',
      value: 'Items',
      onClick: () => handleTabChange('items'),
    },
    {
      key: 'steel-bars',
      value: 'Steel Bars',
      onClick: () => handleTabChange('steel-bars'),
    },
  ];

  useEffect(() => {
    if (
      itemsData?.data.length ===
      steelbarItemsList?.length + itemsWithoutSteelbars.length
    ) {
      if (steelbarItemsList?.length > 0) {
        const steelbarItemListWithQuantity = steelbarItemsList.map(bars => {
          const steelbarFromReq = requisition?.requisitionItemLists?.data?.find(
            sbars => sbars.itemDetails.id === bars?.id,
          );
          return {
            ...bars,
            notes: steelbarFromReq?.notes || '',
            quantity: parseFloat(steelbarFromReq?.quantity).toFixed(3) || 0,
          };
        });
        addSteelbarItem(steelbarItemListWithQuantity);
      }

      if (itemsWithoutSteelbars.length > 0) {
        const nonSteelBarsFormatted = itemsWithoutSteelbars.map(items => {
          return {
            ...items,
            quantity: parseFloat(items?.quantity).toFixed(3) || 0,
          };
        });
        addRequisitionItem(nonSteelBarsFormatted);
      }
    }
  }, [requisition, id, itemsData]);

  useEffect(() => {
    if (requisition?.type === 'ofm' || requisition?.type === 'ofm-tom') {
      if (requisitionItems?.length < itemsWithoutSteelbars?.length) {
        addRequisitionItem(requisitionItems);
      }

      if (steelbarItems?.length < steelbarItemsList?.length) {
        addSteelbarItem(steelbarItems);
      }
    }
  }, [activeTab, requisitionItems.length, steelbarItems.length]);

  useEffect(() => {
    if (requisition?.type === 'non-ofm' || 'non-ofm-tom') {
      addRequisitionItem(itemsWithoutSteelbars);
    }
  }, [requisition?.type, id]);

  useEffect(() => {
    if (itemsWithoutSteelbars?.length < 1 && steelbarItemsList?.length > 0) {
      setActiveTab('steel-bars');
    }
  }, [steelbarItemsList, itemsWithoutSteelbars]);

  const onCloseReqModal = async () => {
    try {
      await cancelRequisition(id, {
        onSuccess: res => {
          showNotification({
            type: 'success',
            message: 'Requisition Cancelled successfully',
          });
          clearRequisitionItemsStore();
          navigate('/app/dashboard');
        },
      });
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.message;
      showNotification({
        type: 'error',
        message: errorMessage,
      });
    }

    setIsCancelReqModalOpen(false);
  };

  const onCheckAttachment = () => {
    setModalData(prevData => ({
      type: 'attachment',
      isOpen: !prevData?.isOpen,
    }));
  };

  const onCheckNote = () => {
    setModalData(prevData => ({
      type: 'note',
      isOpen: !prevData?.isOpen,
    }));
  };

  const closeModal = () => {
    setModalData({
      isOpen: false,
      type: '',
    });
    setAddItemMode(false);
  };

  const closeAttachmentAndNotesModal = () => {
    if (attachments?.hasNewNotifications && modalData?.type === 'attachment') {
      seenAttachment();
    }

    if (notesData?.hasNewNotifications && modalData?.type === 'note') {
      markNotesSeen({
        model: 'requisition',
        modelId: id,
      });
    }

    setModalData(prevData => ({
      ...prevData,
      isOpen: !prevData?.isOpen,
    }));
  };

  const handleAddItem = values => {
    const isDuplicate = requisitionItems.some(
      entry => entry.itemName === values.itemName,
    );

    if (isDuplicate) {
      showNotification({
        type: 'error',
        message:
          'Duplicate Item Name detected! Please enter a unique Item Name.',
      });
      return;
    } else {
      const data = {
        // id: Number(Date.now().toString().substring(0, 6)),
        ...values,
        acctCd: Math.random().toString(36).substring(2, 10),
        notes: 'new item',
      };

      addSubmittedRequisitionItem(data);
      addItemRef.current.resetForm();
    }
  };

  function getNewItems(original, current) {
    const originalItemNames = new Set(
      original.map(item => item.itemDetails.itemName),
    );

    const newItems = current.filter(
      item => !originalItemNames.has(item.itemName),
    );

    return newItems;
  }

  const handleEditRS = () => {
    if (isRequestor) {
      setIsReEditMode(true);
    }
  };

  const handleDownload = () => {
    downloadPdf({
      type: 'requisition',
      id: id,
    });
  };

  const handleUpdateRS = async () => {
    const formData = new FormData();
    const baseData = {
      dateRequired: setDefaultDateValue(requisition?.dateRequired),
      purpose: requisition?.purpose,
      chargeTo: requisition?.chargeTo,
      chargeToId: requisition?.chargeToId?.toString() || '',
      type: requisition?.type,
      companyId: requisition?.company?.id.toString(),
      projectId: requisition?.project?.id.toString(),
      departmentId: requisition?.department?.id.toString(),
      deliverTo: requisition?.deliveryAddress,
      itemList: requisitionItems,
      id: requisition?.id.toString(),
    };

    if (addItemMode) {
      const uniqueItems = getNewItems(
        requisition?.requisitionItemLists?.data,
        requisitionItems,
      );

      if (uniqueItems.length > 0) {
        baseData.itemList = uniqueItems;
      }
      baseData.status = requisition?.status;
    } else {
      baseData.itemList =
        steelbarItems.length > 0
          ? requisitionItems.concat(
              steelbarItems.map(item => {
                return {
                  ...item,
                  tradeCode: item?.trade_code,
                  quantity: item?.quantity,
                  notes: '...',
                };
              }),
            )
          : requisitionItems;
    }
    try {
      const isComplexType = value =>
        typeof value === 'object' && value !== null;

      Object.entries(baseData).forEach(([key, value]) => {
        isComplexType(value)
          ? formData.append(key, JSON.stringify(value))
          : formData.append(key, value);
      });
      for (var pair of formData.entries()) {
        console.log(pair[0] + ', ' + pair[1]);
      }

      await updateRequisition(
        { id, requisitionData: formData },
        {
          onSuccess: () => {
            showNotification({
              type: 'success',
              message: 'Requisition updated successfully',
            });
            clearRequisitionItemsStore();
            refetchItemsData();
            refetchRequisition();
            setAddItemMode(false);
            setIsConfirmRsSubmit(false);
            navigate('/app/dashboard');
          },
        },
      );
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.message;
      showNotification({
        type: 'error',
        message: errorMessage,
      });
      setIsConfirmRsSubmit(false);
      setAddItemMode(false);
    }
  };

  const handleSubmitConfirm = () => {
    setIsConfirmRsSubmit(true);
  };

  const handleCancel = () => {
    setIsCancelModalOpen(true);
  };

  const handleCancelContinue = () => {
    if (
      addItemMode &&
      requisitionItems.length > requisition?.requisitionItemLists?.data.length
    ) {
      requisitionItems
        .filter(item => !item?.id)
        .forEach(item => {
          removeRequisitionItem(item?.acctCd);
        });
    }
    setIsCancelModalOpen(false);
    setAddItemMode(false);
    setIsReEditMode(false);
  };

  const confirmNoteSubmit = values => {
    setSavedNoteValues(values);
    setIsConfirmModalNote(true);
  };

  const confirmAttachmentSubmit = values => {
    setSavedAttachmentValues(values);
    setIsConfirmModalAttachment(true);
  };

  const submitAttachment = async () => {
    try {
      if (savedAttachmentValues.attachments.length > 0) {
        await createAttachment(
          {
            model: 'requisition',
            attachments: savedAttachmentValues.attachments,
            modelId: String(id),
          },
          {
            onSuccess: () => {
              showNotification({
                type: 'success',
                message: 'Attachments uploaded successfully',
              });
              fileRef.current?.removeAllFiles();
              setIsConfirmModalAttachment(false);
              setSavedAttachmentValues(null);
            },
            onError: error => {
              showNotification({
                type: 'error',
                message:
                  error?.response?.data?.message ||
                  'An error occurred while uploading an attachment.',
              });
            },
          },
        );
      } else {
        showNotification({
          type: 'error',
          message: 'No uploaded attachment/s found',
        });
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const submitNotes = async () => {
    // technically update comment
    try {
      if (savedNoteValues.notes.length > 0) {
        await updateComment(
          {
            id,
            comment: savedNoteValues.notes,
          },
          {
            onSuccess: () => {
              showNotification({
                type: 'success',
                message: 'Notes added successfully',
              });
              setIsConfirmModalNote(false);
              setSavedNoteValues(null);
              noteRef.current?.clear();
              refetchRequisition();
            },
            onError: error => {
              showNotification({
                type: 'error',
                message:
                  error?.response?.data?.message ||
                  'An error occurred while adding a note.',
              });
            },
          },
        );
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const handleRemoveAttachment = async () => {
    try {
      await deleteAttachment(
        { id: attachmentToDelete?.id },
        {
          onSuccess: async () => {
            showNotification({
              type: 'success',
              message: 'Attachment Removed successfully',
            });
            setIsDeleteAttachmentModalOpen(false);
            await refetchAttachments();
          },
          onError: error => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message ||
                'Failed to remove attachments',
            });
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  const { mutateAsync: submitRequisition, isPending: isSubmittingRequisition } =
    useSubmitRequisition();

  const handleResubmitRejectRS = async () => {
    const formData = new FormData();
    const itemsList =
      steelbarItems.length > 0
        ? requisitionItems.concat(
            steelbarItems.map(item => {
              return {
                ...item,
                tradeCode: item?.trade_code,
                quantity: item?.quantity,
              };
            }),
          )
        : requisitionItems;

    const baseData = {
      dateRequired: setDefaultDateValue(requisition?.dateRequired),
      purpose: requisition?.purpose,
      chargeTo: requisition?.chargeTo,
      chargeToId: requisition?.chargeToId?.toString() || '',
      type: requisition?.type,
      companyId: requisition?.company?.id.toString(),
      projectId: requisition?.project?.id.toString(),
      departmentId: requisition?.department?.id.toString(),
      deliverTo: requisition?.deliveryAddress,
      itemList: itemsList,
      isDraft: 'false',
      id: requisition?.id.toString(),
    };

    try {
      const isComplexType = value =>
        typeof value === 'object' && value !== null;

      Object.entries(baseData).forEach(([key, value]) => {
        if (isComplexType(value)) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value);
        }
      });

      const handleResubmit = async formData => {
        await submitRequisition(formData, {
          onSuccess: () => {
            showNotification({
              type: 'success',
              message: 'Requisition updated & resubmitted successfully',
            });
            refetchRequisition();
            clearRequisitionItemsStore();
            setIsConfirmRsSubmit(false);
            setIsReEditMode(false);
            navigate('/app/dashboard');
          },
        });
      };

      await updateRequisition(
        { id, requisitionData: formData },
        {
          onSuccess: () => {
            // showNotification({
            //   type: 'success',
            //   message: 'Requisition updated successfully',
            // });
            refetchRequisition();
            handleResubmit(formData);
          },
        },
      );
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.message;
      showNotification({
        type: 'error',
        message: errorMessage,
      });
      setIsConfirmRsSubmit(false);
      setIsReEditMode(false);
    }
  };

  const isCancelVisible = useMemo(() => {
    return (
      isRequestor &&
      (requisition.status === 'submitted' ||
        requisition.status === 'assigned' ||
        requisition.status === 'canvass_approval' ||
        requisition.status === 'partially_canvassed')
    );
  }, [isRequestor, requisition.status]);

  const deleteItem = async () => {
    const selectedItemId = deleteItemModalData?.itemId;

    if (requisition?.type === 'ofm' || requisition?.type === 'ofm-tom') {
      if (activeTab === 'items') {
        removeRequisitionItem(selectedItemId);
      } else {
        removeSteelbarItem(selectedItemId);
      }
    } else {
      removeSubmittedRequisitionItem(selectedItemId);
    }

    setDeleteItemModalData({
      isOpen: false,
      itemId: '',
    });
  };

  return (
    <>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="text-lg font-semibold flex justify-between items-center w-full">
          <div>
            <span className="font-bold text-lg">R.S. Number: </span>
            <span>{requisition?.rsNumber}</span>
          </div>

          {canDownload && (
            <Button
              variant="action"
              hover="action"
              className="color: bg-[#F0F1F1] text-[#420001] w-auto"
              icon={Download}
              isLoading={isPdfDownloading}
              disabled={!canDownload || isPdfDownloading}
              iconPosition="L"
              onClick={handleDownload}
            >
              Download
            </Button>
          )}
        </div>
        <div className="flex justify-between gap-3">
          {isRequestor && !isReEditMode && requisitionStatus === 'rejected' && (
            <Button
              type="button"
              className="min-w-24 w-full sm:w-fit mt-2 sm:mt-0"
              variant="outline"
              hover="outline"
              onClick={handleEditRS}
            >
              Edit
            </Button>
          )}
          {/* {!isReEditMode && (
            <Button
              variant="action"
              hover="action"
              className="color: bg-[#F0F1F1] text-[#420001]"
              icon={Printer}
              iconPosition="L"
            >
              Print
            </Button>
          )} */}
        </div>
      </div>
      <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
        <div className="flex-1 bg-[#FFFFFF] p-6 rounded-lg shadow-sm">
          <h2 className="text-xl font-bold mb-6">Request Details</h2>
          <hr className="my-2 col-span-2" />
          <Form
            onSubmit={handleResubmitRejectRS}
            hasErrorSpace={false}
            ref={mainFormRef}
            options={{
              shouldUnregister: true,
              defaultValues: {
                chargeToId: requisition?.chargeToId,
              },
            }}
          >
            {({ control, register }) => (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <Input
                  label="Type of Request"
                  defaultValue={
                    requisition?.type === 'ofm'
                      ? 'OFM'
                      : requisition?.type === 'ofm-tom'
                        ? 'OFM Transfer of Materials'
                        : requisition?.type === 'non-ofm'
                          ? 'Non-OFM'
                          : requisition?.type === 'non-ofm-tom'
                            ? 'Non-OFM Transfer of Materials'
                            : ''
                  }
                  disabled={true}
                />
                <Input
                  label="Date Required"
                  defaultValue={requisition?.dateRequired}
                  disabled={true}
                />
                <Input
                  label="Company"
                  defaultValue={requisition?.company?.name}
                  disabled={true}
                />
                <Input
                  label="Project"
                  defaultValue={requisition?.project?.name}
                  disabled={true}
                />
                <Input
                  label="Department"
                  defaultValue={requisition?.department?.name}
                  disabled={true}
                />
                <Input
                  label="Purpose"
                  defaultValue={requisition?.purpose}
                  disabled={true}
                />
                <Input
                  label="Deliver To"
                  defaultValue={requisition?.deliveryAddress}
                  disabled={true}
                />
                <Input
                  label="Charge To"
                  defaultValue={
                    chargeToList?.find(
                      item => item.value === requisition?.chargeToId,
                    )?.key
                  }
                  disabled={true}
                />
              </div>
            )}
          </Form>
          <hr className="my-2 col-span-2 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={onCheckAttachment}
              icon={FileIcon}
              iconSize="h-5 w-auto"
              variant="outline"
              hover="outline"
              label={
                <div className="flex justify-between align-middle">
                  <p className="pb-2">Attachments</p>
                  {attachments?.hasNewNotifications && (
                    <Pill className="text-[#EB5757] bg-[#FCE9D8]">
                      New Attachment/s
                    </Pill>
                  )}
                </div>
              }
            >
              Check Attachments
            </Button>
            <Button
              onClick={onCheckNote}
              icon={CommentIcon}
              iconSize="h-5 w-auto"
              variant="outline"
              hover="outline"
              label={
                <div className="flex justify-between align-middle">
                  <p className="pb-2">Notes</p>
                  {notesData?.hasNewNotifications && (
                    <Pill className="text-[#EB5757] bg-[#FCE9D8]">
                      New Comment/s
                    </Pill>
                  )}
                </div>
              }
            >
              Check Notes
            </Button>
            {isReEditMode && (
              <Form
                hasErrorSpace={false}
                options={{
                  shouldUnregister: true,
                }}
                onSubmit={confirmAttachmentSubmit}
              >
                {({ control }) => (
                  <div className="flex flex-col">
                    <File
                      isUploading={isUploading}
                      ref={fileRef}
                      control={control}
                      name={'attachments'}
                      hasAttachments={setHasAttachments}
                    />
                    <div className="flex justify-between items-center mt-1">
                      <div className="text-[#4F4F4F] text-[12px]">
                        The maximum size for each file is 25 MB. File formats -
                        PNG, JPG, JPEG, PDF, Excel, CSV.
                      </div>
                      <Button
                        variant="outline"
                        hover="outline"
                        className="w-32 h-8 text-sm text-[#420001] border-[#420001] ml-4"
                      >
                        Submit
                      </Button>
                    </div>
                  </div>
                )}
              </Form>
            )}
            {isReEditMode && (
              <Form
                hasErrorSpace={false}
                options={{
                  shouldUnregister: true,
                }}
                onSubmit={confirmNoteSubmit}
              >
                {({ control }) => (
                  <div className="col-span-1">
                    <div className="flex flex-col">
                      <TextArea
                        label="Add Notes"
                        name={'notes'}
                        control={control}
                        placeholder="Input notes"
                        ref={noteRef}
                        maxCharacters={60}
                        counterPosition="left"
                        className="mb-0"
                      />
                      <div className="flex justify-end items-center mt-[-15px] ">
                        <div className="ml-4">
                          {' '}
                          <Button
                            variant="outline"
                            hover="outline"
                            className="!w-32 h-8 text-sm"
                          >
                            Submit
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </Form>
            )}
          </div>
        </div>

        {/* Status & Approvers Section */}
        <div className="w-full lg:w-[300px]">
          <StatusSection status={requisitionStatus} />
          {requisitionStatus !== 'submitted' && <AssignedStaffSection />}

          <ApproversSection
            isRefetching={isRequisitionRefetching}
            dateRequired={requisition?.dateRequired}
          />
        </div>
      </div>
      <div className="mt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Items</h2>
          {(requisition?.type === 'non-ofm' ||
            requisition?.type === 'non-ofm-tom') &&
            isPurchasingStaff &&
            requisition?.status !== 'canvassing' && (
              <Button
                iconPosition="L"
                className="w-fit self-end"
                variant="submit"
                type="button"
                icon={PlusIcon}
                onClick={() =>
                  setModalData({ isOpen: true, type: requisition?.type })
                }
              >
                Add Item/s
              </Button>
            )}
        </div>

        {steelbarItemsList?.length && requisitionItems?.length ? (
          <div className="my-6">
            <Tabs activeTab={activeTab} tabs={tabs} />
          </div>
        ) : null}

        {activeTab === 'items' ? (
          <>
            <CreateRSItemsTable
              status={requisition?.status}
              role={user.role.name}
              hideActions={!isForApprover}
              isRequestor={isRequestor}
              notesHidden={isOFM && isForApprover}
              tableData={requisitionItems}
              type={`${requisition?.type}-submitted`}
              isEditMode={assignedToUser}
              isForApprover={isForApprover}
              onCancel={() => {}}
              onSave={() => {}}
              onNoteChange={(id, value) => updateRequisitionNote(id, value)}
              onQuantityChange={(id, value) =>
                updateRequisitionQuantity(id, value)
              }
              setPage={setPage}
              setLimit={setLimit}
              setSort={setSort}
              currentSort={currentSort}
              onRemove={id => {
                setDeleteItemModalData({
                  isOpen: true,
                  itemId: id,
                });
              }}
              submittedNonOfmItems={
                isNonOFM && addItemMode
                  ? requisition?.requisitionItemLists?.data
                  : []
              }
            />
          </>
        ) : (
          <SteelbarsTable
            module="requisition-slip"
            tableData={steelbarItems}
            isEditMode={isReEditMode || isForApprover}
            notesHidden={false}
            isForApprover={isForApprover && !isRequestor}
            onQuantityChange={(id, value) => updateSteelbarQuantity(id, value)}
            onNoteChange={(id, value) => updateSteelbarNote(id, value)}
            setPage={setPage}
            setLimit={setLimit}
            setSort={setSort}
            currentSort={currentSort}
            onRemove={id => {
              setDeleteItemModalData({
                isOpen: true,
                itemId: id,
              });
            }}
          />
        )}

        <hr className="my-10 col-span-2" />
        <div className="flex justify-between sm:flex-row items-center sm:space-x-2 space-y-2 sm:space-y-0">
          {isCancelVisible && (
            <div>
              <Button
                variant="outline"
                hover="outline"
                className="w-fit text-[#EB5757] border-[#EB5757] mr-2"
                onClick={() => setIsCancelReqModalOpen(true)}
              >
                Cancel Request
              </Button>
              <span className="font-bold text-sm sm:text-base">Warning:</span>
              <span className="text-sm sm:text-base">
                This action is irreversible. All progress will be lost once
                request is cancelled.
              </span>
            </div>
          )}
          {(isReEditMode ||
            addItemMode ||
            (isForApprover && !isRequestor) ||
            (isPurchasingStaff && requisition?.status === 'assigned')) && (
            <RSSavingControls
              onSubmit={handleSubmitConfirm}
              onCancel={handleCancel}
              saveDisabled={isSubmittingRequisition}
            />
          )}
        </div>
      </div>

      <Modal
        size="small"
        header={capitalizeFirstLetter(modalData?.type)}
        onClose={closeAttachmentAndNotesModal}
        isOpen={modalData?.isOpen && (!isLoadingAttachments || isLoadingNotes)}
      >
        {
          <AttachmentModalContent
            hasDelete={requisitionStatus === 'cancelled' ? false : true}
            // hasNewAttachment={false}
            onClear={() => {
              setDateData({
                dateFrom: '',
                dateTo: '',
              });
            }}
            onDateFilter={dateValues => {
              setDateData({
                dateFrom: dateValues?.dateFrom,
                dateTo: dateValues?.dateTo,
              });
            }}
            onRemoveAttachment={data => {
              setAttachmentToDelete(data);
              setIsDeleteAttachmentModalOpen(true);
            }}
            mode={modalData?.type}
            onClose={closeAttachmentAndNotesModal}
            data={
              modalData?.type === 'note'
                ? notesData
                : dateData.dateFrom === ''
                  ? attachments
                  : attachments
            }
          />
        }
      </Modal>

      <ConfirmModal
        isOpen={isConfirmModalAttachment}
        onClose={() => setIsConfirmModalAttachment(false)}
        onConfirm={() => submitAttachment()}
        header="Submit Attachment"
      />

      <ConfirmModal
        isOpen={isConfirmModalNote}
        onClose={() => setIsConfirmModalNote(false)}
        onConfirm={() => submitNotes()}
        header="Submit Comment"
      />

      <ConfirmModal
        isOpen={isDeleteAttachmentModalOpen}
        onClose={() => setIsDeleteAttachmentModalOpen(false)}
        onConfirm={handleRemoveAttachment}
        header="Delete Attachment"
        message="You are about to delete this attachment. This action is irreversible. Press continue if you want to proceed with this action."
      />

      <ConfirmModal
        isOpen={isConfirmRsSubmit}
        onClose={() => setIsConfirmRsSubmit(false)}
        onConfirm={() =>
          isReEditMode ? mainFormRef.current?.requestSubmit() : handleUpdateRS()
        }
        header="Update Requisition Slip"
        message="You are about to update this request. Make sure all items are correct.
          Press continue if you want to proceed with this action."
      />

      <CancelModal
        isOpen={isCancelReqModalOpen}
        onClose={() => setIsCancelReqModalOpen(false)}
        onContinue={onCloseReqModal}
        size="small"
        header="Cancel Request"
        message="You are about to cancel this request. This action is irreversible and all progress will be deleted. Press continue if you want to proceed with this action."
      />

      <CancelModal
        isOpen={isCancelModalOpen}
        onClose={handleCancelContinue}
        header="Cancel Changes"
        onContinue={() => {
          setIsCancelModalOpen(false);
          navigate('/app/dashboard');
        }}
        size="small"
      />
      <Modal
        size="small"
        header="Delete Item"
        onClose={() => {
          setDeleteItemModalData({
            isOpen: false,
            itemId: '',
          });
        }}
        isOpen={deleteItemModalData?.isOpen}
      >
        <span className="my-4 text-sm">
          You are about to delete this item from this request. Press continue if
          you want to proceed with this action.
        </span>
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            hover="outline"
            onClick={() => {
              setDeleteItemModalData({
                isOpen: false,
                itemId: '',
              });
            }}
          >
            Cancel
          </Button>
          <Button variant="submit" onClick={deleteItem}>
            Continue
          </Button>
        </div>
      </Modal>
      <NonOFMTRansferModal
        status={requisition?.status}
        data={nonOfmItems}
        onClose={closeModal}
        nonOfmDraftList={requisitionItems?.reduce((acc, item) => {
          acc.push(item?.itemName);
          return acc;
        }, [])}
        onAddItems={values => {
          const combined = [...values, ...requisitionItems];

          if (requisition?.status === 'assigned') {
            addRequisitionItem(combined);
          } else {
            addRequisitionItem(values);
          }
        }}
        isAddItemMode
        isOpen={
          (modalData?.isOpen &&
            (modalData?.type === 'non-ofm' ||
              modalData?.type === 'non-ofm-tom')) ||
          addItemMode
        }
      />
    </>
  );
};
