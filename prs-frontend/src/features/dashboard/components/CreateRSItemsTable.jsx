import React, { useState, useEffect } from 'react';
import { Table, Pagination } from '@components/ui/Table';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@src/components/ui/Button';
import TrashIcon from '@assets/icons/trash_new.svg?react';
import { Input, TextArea } from '@src/components/ui/Form';

import requisitionSlipType from '@src/features/requisitionSlip/constants/requisitionSlipType';

export const CreateRSItemsTable = ({
  status,
  role,
  hideActions,
  isEditMode,
  isRequestor,
  notesHidden,
  requisitionType,
  isForApprover,
  onRemove,
  onQuantityChange,
  onNoteChange,
  type,
  tableData,
  submittedNonOfmItems,
}) => {
  const [currentSort, setCurrentSort] = useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [filteredData, setFilteredData] = useState(tableData);

  useEffect(() => {
    if (currentSort.length) {
      const data = [...tableData];
      data.sort((a, b) => {
        if (currentSort[0][1] === 'ASC') {
          if (
            currentSort[0][0] === 'quantity' ||
            currentSort[0][0] === 'remainingGfq'
          ) {
            return (
              parseFloat(a[currentSort[0][0]]) -
              parseFloat(b[currentSort[0][0]])
            );
          }

          return a[currentSort[0][0]]
            .toString()
            .toLowerCase()
            ?.localeCompare(b[currentSort[0][0]].toString().toLowerCase());
        }

        if (
          currentSort[0][0] === 'quantity' ||
          currentSort[0][0] === 'remainingGfq'
        ) {
          return (
            parseFloat(b[currentSort[0][0]]) - parseFloat(a[currentSort[0][0]])
          );
        }

        return b[currentSort[0][0]]
          .toString()
          .toLowerCase()
          ?.localeCompare(a[currentSort[0][0]].toString().toLowerCase());
      });

      setFilteredData(data);
    } else {
      setFilteredData(tableData);
    }
  }, [currentSort]);

  useEffect(() => {
    if (tableData) {
      setFilteredData(tableData);
    }
  }, [tableData]);

  const baseHeaders = [
    { key: 'quantity', value: 'Qty', css: 'w-[110px]' },
    { key: 'notes', value: 'Notes', css: 'w-[400px]' },
    (status === 'draft' && !hideActions) || isForApprover
      ? {
          key: 'remove',
          value: 'Actions',
          css: 'w-[50px]',
          hasNoSort: true,
        }
      : null,
  ].filter(Boolean);

  const transformedData = filteredData?.map(item => {
    return {
      ...item,
      quantity: item?.quantity,
      remainingGfq: parseFloat(item?.remainingGfq).toFixed(3),
    };
  });

  //headers
  const ofmHeaders = [
    { key: 'itmDes', value: 'Item', css: 'w-00' },
    { key: 'unit', value: 'Unit', css: 'w-[50px]' },
    { key: 'remainingGfq', value: 'Remaining GFQ', css: 'w-[170px]' },
    ...baseHeaders,
  ];

  const nonOfmHeaders = [
    { key: 'itemName', value: 'Item', css: 'max-w-full min-w-[600px]' },
    { key: 'unit', value: 'Unit', css: 'w-[50px]' },
    ...baseHeaders,
  ];

  const submittedNonOfmHeaders = [
    { key: 'itemName', value: 'Item', css: 'max-w-full min-w-[600px]' },
    { key: 'unit', value: 'Unit', css: 'w-[50px]' },
    ...baseHeaders,
  ];

  const tdDesignControls = {
    quantity: {
      render: row => (
        <Input
          type="number"
          maxNumberLength={3}
          min="0"
          step={0.001}
          value={row.quantity || ''}
          disabled={
            (!isForApprover && !isRequestor) ||
            (!isEditMode &&
              (type === requisitionSlipType.NON_OFM_TOM_SUBMITTED ||
                type === requisitionSlipType.NON_OFM_SUBMITTED)) ||
            (!isForApprover && !isRequestor)
          }
          onChange={e => onQuantityChange(row.id, Number(e.target.value))}
        />
      ),
    },
    notes: {
      render: row =>
        notesHidden ? (
          <span>{row.notes}</span>
        ) : (
          <Input
            type="text"
            value={row.notes || ''}
            maxLength={500}
            onChange={e => onNoteChange(row.id, e.target.value)}
            disabled={
              !isRequestor ||
              (!isEditMode &&
                (type === requisitionSlipType.NON_OFM_TOM_SUBMITTED ||
                  type === requisitionSlipType.NON_OFM_SUBMITTED))
            }
          />
        ),
    },
    remove: {
      render: row => {
        const isSingleRowForApprover = isForApprover && tableData?.length === 1;
        const isViewOnly = !isRequestor && !isEditMode && !isForApprover;
        const isAcctCdSubmitted = submittedNonOfmItems?.some(
          item => item?.itemDetails?.acctCd === row.acctCd,
        );

        const isDisabled =
          isSingleRowForApprover || isViewOnly || isAcctCdSubmitted;

        // const removeId =
        //   type !== requisitionSlipType.OFM &&
        //   type !== requisitionSlipType.OFM_TOM
        //     ? row.itemId
        //     : row.id;

        return (
          <Button
            variant="icon"
            hover="submit"
            type="button"
            icon={TrashIcon}
            iconSize="ml-0"
            className="bg-[#fce6e6] hover:bg-[#f49c9c]"
            onClick={() => onRemove(row.id, row.itemId)}
            disabled={isDisabled}
          />
        );
      },
    },
  };

  //tdDesigns
  const ofmHeadersTDDesign = {
    itmDes: {
      render: row => {
        return (
          <div className="bg-gray-200 p-2 rounded-md opacity-50 text-gray-600 cursor-not-allowed">
            <span className="text-wrap ">{row?.itmDes}</span>
          </div>
        );
      },
    },
    acctCd: {
      render: row => <Input type="text" value={row.acctCd} disabled />,
    },
    unit: {
      render: row => (
        <Input
          className="text-center"
          type="text"
          value={row.unit || '---'}
          disabled
        />
      ),
    },
    remainingGfq: {
      render: row => <Input type="text" value={row.remainingGfq} disabled />,
    },
    tdDesignControls,
  };

  const nonOfmHeadersTDDesign = {
    itemName: {
      render: row => (
        <div className="bg-gray-200 p-2 rounded-md opacity-50 text-gray-600 cursor-not-allowed">
          <span className="text-wrap ">{row?.itemName}</span>
        </div>
      ),
    },
    acctCd: {
      render: row => <Input type="text" value={row.acctCd} disabled />,
    },
    unit: {
      render: row => <Input type="text" value={row.unit} disabled />,
    },
  };

  const submittedNonOfmHeadersTDDesign = {
    itemName: {
      render: row => (
        <div className="bg-gray-200 p-2 rounded-md opacity-50 text-gray-600 cursor-not-allowed">
          <span className="text-wrap ">{row?.itemName}</span>
        </div>
      ),
    },
    acctCd: {
      render: row => <Input type="text" value={row.acctCd || '---'} disabled />,
    },
    unit: {
      render: row => <Input type="text" value={row.unit} disabled />,
    },
    quantity: {
      render: row => (
        <Input
          type="number"
          maxNumberLength={3}
          defaultValue={row.quantity || ''}
          disabled={!(role === 'Purchasing Staff' && status === 'assigned')}
          onChange={e => onQuantityChange(row.id, Number(e.target.value))}
        />
      ),
    },
    notes: {
      render: row =>
        notesHidden ? (
          <span>{row.notes}</span>
        ) : (
          <Input
            type="text"
            value={row.notes || ''}
            maxLength={500}
            disabled={!(role === 'Purchasing Staff' && status === 'assigned')}
            onChange={e => onNoteChange(row.id, e.target.value)}
          />
        ),
    },
  };

  const setPage = page => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', page.toString());
      return newParams;
    });
  };

  const handleSort = field => {
    setCurrentSort(prev => {
      const direction = prev[0]?.[1] === 'ASC' ? 'DESC' : 'ASC';

      if (prev[0]?.[1] === 'DESC') return [];

      return [[field, direction]];
    });
  };

  const tdDesignSelector = () => {
    if (
      type === requisitionSlipType.OFM ||
      type === requisitionSlipType.OFM_TOM
    )
      return { ...ofmHeadersTDDesign, ...tdDesignControls };

    if (type === requisitionSlipType.OFM_SUBMITTED && isForApprover)
      return { ...ofmHeadersTDDesign, ...tdDesignControls };
    if (type === requisitionSlipType.OFM_TOM_SUBMITTED && isForApprover)
      return { ...ofmHeadersTDDesign, ...tdDesignControls };

    if (
      type === requisitionSlipType.NON_OFM ||
      type === requisitionSlipType.NON_OFM_TOM
    )
      return { ...nonOfmHeadersTDDesign, ...tdDesignControls };
    if (type === 'non-ofm-submitted' || type === 'non-ofm-tom-submitted')
      return {
        ...submittedNonOfmHeadersTDDesign,
        ...(status !== 'assigned' && role !== 'Purchasing Staff'
          ? tdDesignControls
          : {}),
      };

    return [];
  };

  const headerSelector = () => {
    if (type === 'ofm' || type === 'ofm-tom') return ofmHeaders;

    if (type === 'ofm-submitted' || type === 'ofm-tom-submitted')
      return ofmHeaders;

    if (type === 'non-ofm' || type === 'non-ofm-tom') return nonOfmHeaders;

    if (type === 'non-ofm-submitted' || type === 'non-ofm-tom-submitted')
      return submittedNonOfmHeaders;

    return [];
  };
  return (
    <Pagination total={tableData?.length} setPage={setPage} asHOC={true}>
      {({ currentPage, limit }) => (
        <Table
          headers={headerSelector()}
          data={transformedData}
          tdDesign={tdDesignSelector()}
          onSort={handleSort}
          currentSort={currentSort}
          page={currentPage}
          limit={limit}
          headerRowClassName="bg-[#fff] text-black"
        />
      )}
    </Pagination>
  );
};
