import React, { useState, useEffect, useMemo } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { Button } from '@src/components/ui/Button';
import { Tabs } from '@components/ui/Tabs';
import { ConfirmModal, CancelModal } from '@components/ui/Modal';
import HistoryIcon from '@assets/icons/history.svg?react';
import TouchIcon from '@assets/icons/touch.svg?react';
import { useGetRequisitionSlip } from '../api';
import { ApproverProvider } from './ApproverContext';
import { RSMainTab } from './RSMainTab';
import { RSDocumentTab } from './RSDocumentTab';
import AssignModal from './AssignModal';
import SelectActionModal from './SelectActionModal';
import { PendingAction } from './PendingAction';
import { useUserStore, useRequisitionItemsStore } from '@store';
import { setDefaultDateValue } from '@utils/dateFormat';
import useRSTabStore from '@src/store/rsTabStore';

export const RequisitionSlip = ({
  currentPage,
  currentLimit,
  setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
  resetPage,
}) => {
  const { user } = useUserStore();
  const {
    requisitionItems,
    addRequisitionItem,
    clearRequisitionItemsStore,
    setAddItemMode,
  } = useRequisitionItemsStore();

  const { id } = useParams();

  const {
    data: requisition,
    isLoading,
    refetch: refetchRequisition,
  } = useGetRequisitionSlip(id);

  const {
    activeTab,
    documentsTab = 'canvasses',
    setActiveTab,
    setDocumentsTab,
  } = useRSTabStore();

  const navigate = useNavigate();

  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  const [isSelectActionModal, setIsSelectActionModal] = useState(false);
  const [isAssignModal, setIsAssignModal] = useState(false);
  const [isPendingActionVisible, setIsPendingActionVisible] = useState(true);
  const [redirectToOrders, setRedirectToOrders] = useState(false);

  const handleTabChange = key => {
    setActiveTab(key);
  };

  const closeCancelModal = () => {
    setIsCancelModalOpen(false);
    navigate('/app/dashboard', { replace: true });
  };

  const closeSelectActionModal = () => {
    setIsSelectActionModal(false);
  };

  const handleAssign = () => {
    setIsSelectActionModal(false);
    setIsAssignModal(true);
  };

  const approversList = requisition?.requisitionApprovers?.data;

  const hasSucceedingApprovers = approversList?.some((a, i) => {
    const current = a.approver?.fullName;
    const next = approversList[i + 1]?.approver?.fullName;

    const sameApprover = current && next && current === next;
    const hasPendingStatus =
      a.status === 'pending' || approversList[i + 1]?.status === 'pending';

    return sameApprover && hasPendingStatus;
  });

  const handlePendingActionVisibility = () => {
    if (hasSucceedingApprovers) {
      setIsPendingActionVisible(true);
    } else {
      setIsPendingActionVisible(false);
    }
  };

  const handleSelectRelatedDocuments = tabs => {
    setActiveTab(tabs.primary);
    setDocumentsTab(tabs.secondary);
    setRedirectToOrders(true);
  };

  useEffect(() => {
    // Any setup code can go here.
    return () => {
      setAddItemMode(false);
      clearRequisitionItemsStore();
    };
  }, []);

  useEffect(() => {
    resetPage();
  }, [activeTab]);

  const tabs = [
    {
      key: 'rs-main',
      value: 'RS Main',
      onClick: () => handleTabChange('rs-main'),
    },
    {
      key: 'related-documents',
      value: 'Related Documents',
      onClick: () => handleTabChange('related-documents'),
    },
  ];

  const checkAltApprover = (approver, altApprovers) => {
    const approverData = approver?.data?.find(approver => {
      return approver.status === 'pending';
    });
    if (approverData) {
      const dateRequired = new Date(
        setDefaultDateValue(requisition?.dateRequired),
      ).setHours(0, 0, 0, 0);
      if (approverData?.approver?.userLeaves?.length) {
        const found = approverData.approver.userLeaves.find(leave => {
          const startDate = new Date(leave.startDate).setHours(0, 0, 0, 0);
          const endDate = new Date(leave.endDate).setHours(0, 0, 0, 0);
          return startDate <= dateRequired && dateRequired <= endDate;
        });

        if (!found && approverData?.approverId === user.id) {
          return true;
        } else if (!found && approverData?.approverId !== user.id) {
          return false;
        }

        if (
          found &&
          approverData?.altApproverId &&
          approverData?.altApproverId !== user.id
        ) {
          return false;
        } else if (
          found &&
          approverData?.altApproverId === null &&
          approverData?.approverId === user.id
        ) {
          return true;
        } else if (found && approverData?.altApproverId === user.id) {
          return true;
        }

        return false;
      } else if (approverData?.approverId === user.id) {
        return true;
      }
    }

    return false;
  };

  const isRequestor = requisition?.createdByUser?.id === user.id || false;

  const isForApprover = checkAltApprover(
    requisition?.requisitionApprovers,
    requisition?.requisitionAltApprover,
  );
  /*
  (requisition?.requisitionApprovers?.data?.find(
      approver => {
        return approver.status === 'pending'
      })?.approverId === user.id )|| false;
      */

  const hasAltApprover = requisition?.requisitionApprovers?.data.some(
    item => item.isAltApprover === true,
  );
  const approversLen = requisition?.requisitionApprovers?.data.length;
  const approvedStatusCount = requisition?.requisitionApprovers?.data?.filter(
    approver => approver.status === 'approved',
  ).length;
  const remainingNotApproveCnt = approversLen - approvedStatusCount;

  const isPurchasingHead = user.role.name == 'Purchasing Head';
  const isPurchasingStaff = user.role.name == 'Purchasing Staff';
  const isAdmin = user.role.name == 'Admin';

  const contextValue = useMemo(
    () => ({
      initialApprovers: requisition?.requisitionApprovers?.data || [],
      initalRequisitionStatus: requisition?.status,
      initialAssignedTo: requisition?.assignedTo,
    }),
    [
      requisition?.requisitionApprovers?.data,
      requisition?.status,
      requisition?.assignedTo,
    ],
  );

  if (isLoading) {
    return <div>Loading requisition data...</div>;
  }

  const openAddItemsForm = () => {
    setAddItemMode(true);
    closeSelectActionModal();
  };

  return (
    <ApproverProvider {...contextValue}>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col md:flex-row md:justify-end md:items-center gap-4 mb-4 md:absolute md:top-8 md:right-8 text-sm">
          <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
            {isAdmin && (
              <Link to={`/app/request-history/${id}`}>
                <Button
                  className="w-full md:w-fit mt-2 md:mt-0 text-[#420001] stroke-[#420001]"
                  variant="outline"
                  hover="outline"
                  icon={HistoryIcon}
                >
                  Request History
                </Button>
              </Link>
            )}
            <Button
              className="w-full md:w-fit mt-2 md:mt-0"
              variant="submit"
              hover="submit"
              icon={TouchIcon}
              onClick={() => {
                refetchRequisition();
                setIsSelectActionModal(true);
              }}
            >
              Select Action
            </Button>
          </div>
        </div>
        <Tabs tabs={tabs} activeTab={activeTab} />

        {/* NOTE: for approver only */}
        {activeTab === 'rs-main' &&
          isForApprover &&
          requisition?.status !== 'draft' &&
          requisition?.status !== 'rejected' &&
          requisition?.status !== 'cancelled' &&
          isPendingActionVisible && (
            <PendingAction
              hasAltApprover={hasAltApprover}
              requisitionID={requisition.id}
              onApprovalSubmit={handlePendingActionVisibility}
              remainingNotApproveCnt={remainingNotApproveCnt}
            />
          )}

        <div>
          <div>
            {activeTab === 'rs-main' ? (
              <RSMainTab
                isRequestor={isRequestor}
                isForApprover={isForApprover}
                setPage={setPage}
              />
            ) : (
              <RSDocumentTab
                activeTab={documentsTab}
                setActiveTab={setDocumentsTab}
                id={id}
                currentPage={currentPage}
                currentLimit={currentLimit}
                setPage={setPage}
                setLimit={setLimit}
                setSort={setSort}
                currentSort={currentSort}
                sortBy={sortBy}
                redirectToOrders={redirectToOrders}
              />
            )}
          </div>
        </div>

        <SelectActionModal
          user={user}
          requisition={requisition}
          requestType={requisition?.type}
          onAddItems={openAddItemsForm}
          isOpen={isSelectActionModal}
          onClose={closeSelectActionModal}
          onAssign={handleAssign}
          isPurchasingHead={isPurchasingHead}
          isPurchasingStaff={isPurchasingStaff}
          requisitionActions={requisition?.actions}
          onSelectRelatedDocuments={handleSelectRelatedDocuments}
        />

        <AssignModal
          openAssignModal={isAssignModal}
          closeAssignModal={() => setIsAssignModal(false)}
          openSelectActionModal={() => setIsSelectActionModal(true)}
        />
      </div>
    </ApproverProvider>
  );
};
