import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@lib/apiClient';

export const updateDeliveryReceipt = ({ id, data }) => {
  console.log(data);
  return api.put(`/v1/delivery-receipts/${id}`, data);
};

export const useUpdateDeliveryReceipt = (config = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateDeliveryReceipt,
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['delivery-receipt'] });
      queryClient.invalidateQueries({ queryKey: ['delivery-receipts'] });
      queryClient.invalidateQueries({ queryKey: ['getDeliveryReceiptNotes'] });
      queryClient.invalidateQueries({ queryKey: ['PODetailsForDelivery'] });
      queryClient.invalidateQueries({ queryKey: ['PurchaseOrderItems'] });
    },
    ...config,
  });
};