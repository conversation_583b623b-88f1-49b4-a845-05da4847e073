import React, { useState } from 'react';
import { Modal } from '@components/ui/Modal';
import { Form, Input, TextArea } from '@components/ui/Form';
import { Button } from '@components/ui/Button';
import { EditDetailsModal } from '@features/delivery-receipt/components/EditDetailsModal';
import { CancelModal } from '@components/ui/Modal';
import { cn } from '@src/utils/cn';

export const ItemDetailsModal = ({
  isOpen,
  onClose,
  item,
  onSave,
  onCancel,
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCancelReturnsModalOpen, setIsCancelReturnsModalOpen] =
    useState(false);
  const [selectedReturnItem, setSelectedReturnItem] = useState(null);
  const formRef = React.useRef(null);

  const handleEditClick = () => {
    setIsEditModalOpen(true);
    handleCloseModal();
  };

  const handleEditClose = () => {
    setIsEditModalOpen(false);
  };

  const handleSave = values => {
    onSave?.(values);
    setIsEditModalOpen(false);
  };

  const handleCloseModal = () => {
    onClose();
  };

  const handleCancelReturns = item => {
    setSelectedReturnItem(item);
    setIsCancelReturnsModalOpen(true);
  };

  const handleCancelReturnsConfirm = () => {
    setIsCancelReturnsModalOpen(false);
    setSelectedReturnItem(null);
    onClose();
  };

  return (
    <React.Fragment>
      <Modal header="Item Details" onClose={handleCloseModal} isOpen={isOpen}>
        <Form
          ref={formRef}
          className="flex flex-col gap-4"
          onSubmit={() => {}}
          options={{
            defaultValues: {
              item: item?.item || '',
              dateDelivered: item?.dateDelivered || '',
              qtyOrdered: parseFloat(item?.qtyOrdered).toFixed(3) || '0.00',
              qtyDelivered: parseFloat(item?.qtyDelivered).toFixed(3) || '0.00',
              qtyReturn: item?.qtyReturn
                ? parseFloat(item?.qtyReturn).toFixed(3)
                : '0.00',
              notes: item?.notes || '',
            },
          }}
        >
          {({ register, control }) => (
            <div className={cn('flex flex-col space-y-6')}>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div>Item</div>
                  <div className="bg-gray-200 p-2 rounded-md opacity-50 text-gray-600 cursor-not-allowed">
                    <span className="text-wrap ">{item?.item}</span>
                  </div>
                </div>

                <Input
                  label="Date Delivered"
                  {...register('dateDelivered')}
                  disabled={true}
                />
                <Input
                  label="Qty Ordered"
                  {...register('qtyOrdered')}
                  disabled={true}
                />
                <Input
                  label="Qty Delivered"
                  {...register('qtyDelivered')}
                  disabled={true}
                />
                <Input
                  label="Qty for Return"
                  {...register('qtyReturn')}
                  disabled={true}
                />
                <TextArea
                  label="Notes"
                  name="notes"
                  className="min-h-48  w-full"
                  control={control}
                  maxCharacters={500}
                  disabled={true}
                />
              </div>
            </div>
          )}
        </Form>
      </Modal>

      <CancelModal
        isOpen={isCancelReturnsModalOpen}
        header="Cancel Returns"
        onClose={() => setIsCancelReturnsModalOpen(false)}
        onContinue={handleCancelReturnsConfirm}
        message="You are about to cancel the returns. All changes will not be saved. Press continue if you want to proceed with this action."
      />

      <EditDetailsModal
        isOpen={isEditModalOpen}
        onClose={handleEditClose}
        item={item}
        onSave={handleSave}
      />
    </React.Fragment>
  );
};
