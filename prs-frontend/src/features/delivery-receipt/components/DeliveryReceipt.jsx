import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

// React Router imports
import { useParams, useNavigate } from 'react-router-dom';

// Store imports
import useDeliveryReceiptStore from '@src/store/deliveryReceiptStore';
import { useUserStore } from '@store';
import { useDownloadTemplatePdf } from '@src/hooks/useDownloadTemplatePdf';

// API hooks
import {
  useCreateDeliveryReceipt,
  useCreateDeliveryReceiptAttachment,
  useGetDeliveryReceipt,
  useUpdateDeliveryReceipt,
  useUpdateDeliveryReceiptItem,
  useCancelReturns,
  useGetPOsForDelivery,
  useGetPODetailsForDelivery,
  useGetDeliveryReceiptNotes,
  useCreateDeliveryReceiptNote,
  useMarkAsSeen,
  useGetDrAttachmentNotifications,
  useGetPurchaseOrderItems,
} from '../api';

import { useGetOFMItems } from '@src/features/ofm/api';

import {
  useUploadAttachments,
  useAssignAttachments,
  useGetAttachments,
  useDeleteAttachment,
} from '@features/attachments/api';

import { useCreateNotes } from '@features/notes/api';

import { useGetRequisitionSlip } from '@src/features/dashboard/api';

// Notification hooks
import { useNotification } from '@hooks/useNotification';

// UI components
import {
  ConfirmModal,
  CancelModal,
  ActionModal,
  Modal,
} from '@components/ui/Modal';
import {
  Form,
  Input,
  File,
  TextArea,
  Select,
  FileUpload,
} from '@src/components/ui/Form';
import { Button } from '@src/components/ui/Button';
import { SearchBar } from '@components/ui/SearchBar';
import { ActionButtons } from '@features/delivery-receipt/components/ActionButtons';
import { Tabs } from '@src/components/ui/Tabs';

// Feature components
import { ItemDetailsModal } from '@features/delivery-receipt/components/ItemDetailsModal';
import { Status } from '@src/features/dashboard/components/Status';
import { EditDetailsModal } from '@features/delivery-receipt/components/EditDetailsModal';
import DeliveryReceiptAttachmentModalContent from '@features/delivery-receipt/components/DeliveryReceiptAttachmentModalContent';

// Utilities
import { setDefaultDateValue } from '@src/utils/dateFormat';
import {
  steelbarsItemsExtractor,
  nonSteelbarsItemsExtractor,
  matchingAccountCodeExtractor,
} from '@src/utils/itemListSelector';

import { MODELS } from '@config/modelsConfig';
import { NOTES_TYPE } from '@config/notesConfig';

// Icons
import AddIcon from '@assets/icons/add.svg?react';
import XIcon from '@assets/icons/xicon.svg?react';
import CommentIcon from '@src/assets/icons/comment.svg?react';
import FileIcon from '@src/assets/icons/file.svg?react';
import FileClock from '@assets/icons/file-clock.svg?react';
import Download from '@assets/icons/download.svg?react';

// Zod Validation
import { drFormSchema } from '@src/schema/delivery-receipt.schema';
import { capitalizeFirstLetter } from '@src/utils/capitalizeFirstLetter';
import { Pill } from '@src/components/ui/Pill';
import { DeliveryReceiptItemsTable } from './DeliveryReceiptItemsTable';

const SAMPLE_PO_DATA = [
  { poNumber: 'PO-2024-001', supplier: 'ABC Construction Supply' },
  { poNumber: 'PO-2024-002', supplier: 'XYZ Hardware Corporation' },
  { poNumber: 'PO-2024-003', supplier: 'Metro Steel Manufacturing' },
  { poNumber: 'PO-2024-004', supplier: 'Pioneer Building Materials' },
  { poNumber: 'PO-2024-005', supplier: 'Global Cement Industries' },
  { poNumber: 'PO-2024-006', supplier: 'Supreme Paint Solutions' },
  { poNumber: 'PO-2024-007', supplier: 'United Tools and Equipment' },
  { poNumber: 'PO-2024-008', supplier: 'Pacific Lumber Company' },
  { poNumber: 'PO-2024-009', supplier: 'Eastern Electrical Supplies' },
  { poNumber: 'PO-2024-010', supplier: 'National Plumbing Systems' },
];

const SELECT_OPTIONS = SAMPLE_PO_DATA.map(item => ({
  key: item.poNumber,
  value: item.poNumber,
}));

export const DeliveryReceipt = ({ currentLimit, setPage, setLimit }) => {
  const { user } = useUserStore();
  // Store Utilities
  // URL and Navigation
  const { id, deliveryReceiptId } = useParams();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const { mode, updateMode } = useDeliveryReceiptStore();
  const noteRef = useRef(null);

  // Form State
  const INITIAL_FORM_STATE = {
    purchaseOrderNo: '',
    supplier: '',
    attachments: [],
    note: '',
    invoiceNo: '',
    invoiceDate: '',
    totalSales: '',
    vatAmount: '',
    vatExemptAmount: '',
    zeroRatedAmount: '',
    invoiceFile: [],
  };

  const fileRef = useRef(null);
  const mainFormRef = useRef(null);
  const invoiceFileRef = useRef(null);
  const saveAsDraft = useRef(false);
  const supplierRef = useRef(null);
  const attachmentRef = useRef(null);

  const [isChangeSelectionModalOpen, setIsChangeSelectionModalOpen] =
    useState(false);
  const [isCancelled, setIsCancelled] = useState(null);
  const [isConfirmed, setIsConfirmed] = useState(null);
  const [note, setNote] = useState('');
  const [showInvoice, setShowInvoice] = useState(false);
  const [isInvoiceAdded, setIsInvoiceAdded] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [hasAttachments, setHasAttachments] = useState(false);
  const [invoiceAttachment, setInvoiceAttachment] = useState({});
  const [hasInvoiceAttachment, setHasInvoiceAttachment] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploadingInvoice, setIsUploadingInvoice] = useState(false);
  const [drFormData, setDrFormData] = useState([]);
  const [poItemsList, setPoItemsList] = useState([]);
  const [selectedPo, setSelectedPo] = useState(null);
  const [purchaseOrderOptions, setPurchaseOrderOptions] = useState([]); // Options for delivery dropdown

  // Component State
  const [searchQuery, setSearchQuery] = useState({});
  const [searchFilename, setSearchFilename] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [isItemDetailsModalOpen, setIsItemDetailsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCancelReturnsModalOpen, setIsCancelReturnsModalOpen] =
    useState(false);
  const [selectedReturnItem, setSelectedReturnItem] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastSavedDate, setLastSavedDate] = useState('');
  const [lastSavedTime, setLastSavedTime] = useState('');
  const [deliveryItems, setDeliveryItems] = useState([]);
  const [isDraftMode, setIsDraftMode] = useState(mode === 'draft');
  const [viewMode, setViewMode] = useState(mode === 'view');
  const [activeTab, setActiveTab] = useState('items');

  // temporary states (Table)
  const [currentSort, setCurrentSort] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);

  // Modal State
  const [modalData, setModalData] = useState({
    isOpen: false,
    type: '',
  });

  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isDeleteAttachmentModalOpen, setIsDeleteAttachmentModalOpen] =
    useState(false);

  const [dateData, setDateData] = useState({
    dateFrom: '',
    dateTo: '',
  });

  // Queries and Mutations

  const { data: requisition, isLoading: isRequisitionLoading } =
    useGetRequisitionSlip(id);

  const isTypeOfm = ['ofm-tom', 'ofm'].includes(requisition?.type);

  const { data: poList } = useGetPOsForDelivery(id);

  const { mutate: downloadPdf, isPending: isPdfDownloading } =
    useDownloadTemplatePdf();

  const { data: OFMItems, isLoading: isFetchingOFMItems } = useGetOFMItems({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });

  const { data: fileAttachments } = useGetAttachments(
    {
      modelId: deliveryReceiptId,
      model: 'delivery_receipt',
      search: searchFilename,
    },
    {
      enabled: !!deliveryReceiptId,
    },
  );

  const { data: deliveryReceiptNotes, refetch: refetchDeliveryReceiptNotes } =
    useGetDeliveryReceiptNotes(
      {
        id: deliveryReceiptId,
        dateFrom: dateData.dateFrom,
        dateTo: dateData.dateTo,
      },
      { enabled: !!deliveryReceiptId },
    );

  const { data: poDetails } = useGetPODetailsForDelivery(selectedPo, {
    enabled: !!selectedPo,
  });

  const { data: poItems, refetch: refetchPurchaseOrderItems } =
    useGetPurchaseOrderItems(selectedPo, {
      enabled: !!selectedPo,
    });

  const { mutateAsync: createNotes, isPending: isSubmittingNotes } =
    useCreateNotes();

  const { mutateAsync: uploadAttachments, isLoading: isFileUploading } =
    useUploadAttachments();

  const { mutateAsync: deleteAttachment } = useDeleteAttachment();

  const { mutateAsync: assignAttachments, isLoading: isAssigning } =
    useAssignAttachments();

  const { data: deliveryReceipt, isLoading: isDeliveryReceiptLoading } =
    useGetDeliveryReceipt(
      { id: deliveryReceiptId, type: requisition?.type },
      { enabled: !!deliveryReceiptId },
    );

  const { mutateAsync: createAttachment, isPending: isCreatingDrPending } =
    useCreateDeliveryReceiptAttachment();

  const {
    data: hasNewNotification,
    refetch: refetchHasNewNotification,
    isFetching: isLoadingHasNewNotification,
  } = useGetDrAttachmentNotifications(deliveryReceiptId, {
    enabled: !!deliveryReceiptId,
  });

  const { mutateAsync: markAttachmentsAsSeen } = useMarkAsSeen();

  const { mutateAsync: createNote } = useCreateDeliveryReceiptNote({});

  const {
    mutateAsync: createDeliveryReceipt,
    isPending: isCreatingDeliveryReceipt,
  } = useCreateDeliveryReceipt({
    onSuccess: ({ id: deliveryID, status }) => {
      showNotification({
        type: 'success',
        message:
          status === 'Draft'
            ? 'Draft created successfully'
            : 'Receiving Report created successfully',
      });
      navigate(`/app/receiving-report/${deliveryID}/requisition-slip/${id}`);
    },
  });

  const { mutateAsync: updateDeliveryReceipt, isPending: isUpdating } =
    useUpdateDeliveryReceipt({
      onSuccess: () => {
        showNotification({
          type: 'success',
          message:
            modalData?.mode === 'draft'
              ? 'Draft created successfully'
              : 'Receiving Report created successfully',
        });

        if (modalData?.mode === 'draft') {
          setIsDraftMode(true);
          setViewMode(false);
          setModalData(prevData => ({
            type: '',
            isOpen: false,
          }));
        } else {
          setIsDraftMode(false);
          setViewMode(true);
        }
      },
    });

  const { mutateAsync: updateItem } = useUpdateDeliveryReceiptItem();

  const { mutateAsync: cancelReturns, isPending: isCancelingReturns } =
    useCancelReturns({
      onSuccess: () => {
        showNotification({
          type: 'success',
          message: 'Returns cancelled successfully',
        });
      },
      onError: error => {
        showNotification({
          type: 'error',
          message: error?.response?.data?.message || 'Failed to cancel returns',
        });
      },
    });

  //tabs for Steelbars
  const handleTabChange = key => {
    setActiveTab(key);
  };

  const tabs = [
    {
      key: 'items',
      value: 'Items',
      onClick: () => handleTabChange('items'),
    },
    {
      key: 'steel-bars',
      value: 'Steel Bars',
      onClick: () => handleTabChange('steel-bars'),
    },
  ];

  // Effects

  useEffect(() => {
    if (viewMode && !showInvoice) {
      setShowInvoice(true);
    }
  }, [viewMode, showInvoice, isInvoiceAdded]);

  useEffect(() => {
    if (mode) {
      setIsDraftMode(mode === 'draft');
      setViewMode(mode === 'view');
    }
  }, [mode]);

  useEffect(() => {
    if (isDeliveryReceiptLoading || isRequisitionLoading) return;

    // ensures that the mode persist after page refresh
    if (!mode) {
      if (deliveryReceipt?.isDraft == true) {
        updateMode('draft');
      } else if (deliveryReceipt?.isDraft == false) {
        updateMode('view');
      } else {
        updateMode(null);
      }
    }

    if (deliveryReceipt?.isDraft && deliveryReceipt?.invoice) {
      setShowInvoice(true);
      setIsInvoiceAdded(true);
    }

    if (deliveryReceipt) {
      setSelectedPo(deliveryReceipt?.poId);
      setInvoiceAttachment(deliveryReceipt?.invoice?.attachment);
      setAttachments(deliveryReceipt?.attachments);
    }

    if (deliveryReceiptId && deliveryReceipt?.items) {
      const mappedItems = deliveryReceipt.items.map(item => ({
        id: item.id,
        itemId: item.itemId,
        poItemId: item.poItemId,
        accountCode: item.accountCode,
        poId: selectedPo || deliveryReceipt?.poId,
        item: item.itemDes,
        qtyOrdered: item.qtyOrdered,
        qtyDelivered: item.qtyDelivered,
        qtyReturn: item.qtyReturned,
        unit: item.unit,
        dateDelivered: item.dateDelivered,
        deliveryStatus: item.deliveryStatus,
        notes: item.notes,
        hasReturns: item.hasReturns,
      }));
      setDeliveryItems(mappedItems);

      if (deliveryReceipt.draftSavedAt) {
        const savedDate = new Date(deliveryReceipt.draftSavedAt);
        setLastSavedDate(
          savedDate.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
          }),
        );
        setLastSavedTime(
          savedDate.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
          }),
        );
      }
    } else if (poItems) {
      //
      //setDeliveryItems(transformRequisitionData(poItems));
      setPoItemsList(transformRequisitionData(poItems));
    }
  }, [
    poItems,
    poDetails,
    deliveryReceiptId,
    deliveryReceipt,
    requisition,
    isDeliveryReceiptLoading,
    isRequisitionLoading,
  ]);

  useEffect(() => {
    if (poList && !deliveryReceiptId) {
      setSelectedPo(null);
      const options = poList?.map(po => {
        return {
          key: po?.poNumber,
          value: po?.id,
        };
      });
      setPurchaseOrderOptions(options);
    }
  }, [poList]);

  useEffect(() => {
    if (deliveryReceipt && poDetails) {
      setPurchaseOrderOptions([
        {
          key: poDetails?.poNumber,
          value: poDetails?.id,
        },
      ]);
    }
  }, [poDetails, requisition]);

  // Transform Data - Used for DR Creation
  const transformRequisitionData = poItems => {
    if (!poItems) return [];

    return poItems?.data.map(item => ({
      poItemId: item.id, //purchase order item id
      itemId: item.itemId,
      poId: selectedPo || deliveryReceipt?.poId,
      item: item.itemName,
      isSteelbars: item?.isSteelbar,
      accountCode: item?.accountCode,
      qtyOrdered: item.quantityPurchased,
      totalDelivered: item.totalDelivered,
      remainingQtyForDelivery: item.remainingQtyForDelivery,
      qtyDelivered: item.qtyDelivered || 0,
      returnQty: item.returnQty || 0,
      unit: item.unit,
      dateDelivered: item.dateDelivered || '',
      deliveryStatus: item.deliveryStatus || '',
      notes: item.notes || '',
    }));
  };

  // Form Event Handlers
  const handleChangeSelection = () => {
    setIsChangeSelectionModalOpen(true);
  };

  const handleCancelSelection = () => {
    setIsCancelled(true);
    setIsChangeSelectionModalOpen(false);
  };

  const handleContinueSelection = () => {
    setIsConfirmed(true);
    setIsChangeSelectionModalOpen(false);
  };

  // Event Handlers

  const handleDownload = () => {
    downloadPdf({
      type: 'delivery-receipt',
      id: deliveryReceiptId,
    });
  };

  const onSubmitNote = async () => {
    try {
      const noteReference = noteRef.current;

      const payload = {
        model: MODELS.DELIVERY_RECEIPT,
        modelId: deliveryReceiptId,
        note,
        userType: 'Requestor',
        commentType: NOTES_TYPE.NOTE,
      };

      await createNotes(payload, {
        onSuccess: () => {
          showNotification({
            type: 'success',
            message: 'Note added successfully',
          });
          noteReference.clear();
          setNote('');
          refetchDeliveryReceiptNotes();
        },
      });
    } catch (error) {
      let errorMessage = error?.response?.data?.message || 'Failed to add note';

      showNotification({
        type: 'error',
        message: errorMessage,
      });
      throw error;
    }
  };

  const submitAttachments = async () => {
    const file = attachmentRef.current;
    const attachmentIds = file?.attachmentIds;

    await assignAttachments(
      {
        attachmentIds,
        model: MODELS.DELIVERY_RECEIPT,
        modelId: parseInt(deliveryReceiptId),
      },
      {
        onSuccess: message => {
          showNotification({
            type: 'success',
            message: message?.message || 'Attachments uploaded successfully',
          });
          file.removeAllFiles();
          setHasAttachments(false);
        },
        onError: error => {
          const errorMessage = error?.response?.data?.message || error?.message;
          showNotification({
            type: 'error',
            message: errorMessage,
          });
        },
      },
    );
  };

  const seenAttachments = async () => {
    if (!modalData?.type) return;

    await markAttachmentsAsSeen(
      { id: deliveryReceiptId, type: modalData.type },
      {
        onError: () => {
          console.log(`Error updating view of ${modalData?.type}`);
        },
      },
    );

    if (modalData.type === 'attachments' && hasNewNotification) {
      refetchHasNewNotification();
    } else if (
      modalData.type === 'notes' &&
      deliveryReceiptNotes?.hasNewNotifications
    ) {
      refetchDeliveryReceiptNotes();
    }
  };

  const handleItemClick = item => {
    setSelectedItem(item);
    setIsItemDetailsModalOpen(true);
  };

  const handleSelectedPO = value => {
    setSelectedPo(value);
  };

  const handleEditClick = item => {
    setSelectedItem(item);
    setIsEditModalOpen(true);
  };

  const handleHistoryClick = item => {
    setSelectedItem(item);
    navigate(`history/${item.id}`, {
      state: { previousMode: isDraftMode ? 'draft' : viewMode ? 'view' : null },
    });
  };

  const handleSave = async values => {
    try {
      const found = deliveryItems.find(item => item.itemId === values.itemId);

      if (found) {
        setDeliveryItems(prevItems => {
          return prevItems.map(item => {
            if (item?.itemId === selectedItem?.itemId) {
              const qtyDelivered = parseFloat(values.qtyDelivered) || 0;
              const returnQty = parseFloat(values.qtyReturn) || 0;

              return {
                ...item,
                qtyDelivered,
                qtyReturn: returnQty,
                accountCode: item?.accountCode,
                dateDelivered: values.dateDelivered || '',
                notes: values.notes || '',
              };
            }
            return item;
          });
        });
      } else {
        const newDeliveryItems = [...deliveryItems, values];
        setDeliveryItems(newDeliveryItems);
      }
      const filteredData = poItemsList.filter(
        item => item.itemId !== values.itemId,
      );

      setPoItemsList([...filteredData]);

      setIsEditModalOpen(false);
    } catch (error) {
      showNotification({
        type: 'error',
        message: error?.response?.data?.message || 'Failed to update item',
      });
    }
  };

  const preventMinus = e => {
    if (e.code === 'Minus' || e.code === 'KeyE') {
      e.preventDefault();
    }
  };

  const onAssignAttachments = async attachmentIds => {
    try {
      const model = 'delivery_receipt';

      await assignAttachments(
        { attachmentIds, model, modelId: parseInt(deliveryReceiptId) },
        {
          onSuccess: message => {
            showNotification({
              type: 'success',
              message: 'attachment added successfully!',
            });
            setAttachments([]);
            attachmentRef.current.removeAllFiles();
          },
          onError: error => {
            const errorMessage =
              error?.response?.data?.message || error?.message;
            showNotification({
              type: 'error',
              message: errorMessage,
            });
          },
        },
      );
    } catch (e) {
      throw e;
    }
  };

  const checkIfHasMissingItemData = () => {
    const found = deliveryItems.find(item => {
      if (!item.qtyDelivered && !item.qtyReturn) {
        return true;
      }

      return false;
    });

    return found;
  };

  const handleSaveDR = async values => {
    let invoiceAttachmentId = null;
    let attachmentIds = [];
    if (values) {
      if (checkIfHasMissingItemData()) {
        showNotification({
          type: 'error',
          message:
            'Please fill up all date delivered and items delivered/returned in the Item/s Table',
        });
        return;
      }

      if (values.attachments) {
        try {
          // Filter out already existing attachments from deliveryReceipt
          const newAttachments = values.attachments.filter(
            newAttachment =>
              !deliveryReceipt?.attachments?.some(
                existing => existing.fileName === newAttachment.fileName,
              ),
          );

          // Proceed only if there are new attachments to upload
          if (newAttachments.length > 0) {
            const uploadedAttachments = await createAttachment({
              model: 'delivery_receipt',
              attachments: newAttachments,
            });

            attachmentIds = uploadedAttachments.map(
              attachment => attachment.id,
            );
          }
        } catch (error) {
          showNotification({
            type: 'error',
            message: error?.response?.data?.message || 'Failed to upload file',
          });
        }
      }

      const poId = SELECT_OPTIONS.findIndex(
        option => option.value === values.purchaseOrderNo,
      );

      try {
        setIsLoading(true);
        const deliveryReceiptData = {
          requisitionId: id,
          poId: selectedPo.toString() || deliveryReceipt?.poId.toString(),
          supplier: poDetails?.supplier?.name || deliveryReceipt?.supplier,
          isDraft: saveAsDraft.current.toString(),
          note: values.note ?? deliveryReceipt?.note ?? 'Note',

          invoiceNumber:
            values.invoiceNo || deliveryReceipt?.invoice?.invoiceNo,
          issuedDate:
            setDefaultDateValue(values?.invoiceDate) ||
            deliveryReceipt?.invoice?.issuedInvoiceDate,
          supplierDeliveryIssuedDate: setDefaultDateValue(
            values?.supplierDeliveryIssuedDate,
          ),
          items: deliveryItems.map(item => {
            const itemData = {
              id: item.id,
              itemId: item.itemId,
              accountCode: item?.accountCode,
              poItemId: item.poItemId,
              poId: selectedPo.toString() || deliveryReceipt?.poId.toString(),
              itemDes: item.item,
              qtyOrdered: parseFloat(item.qtyOrdered),
              qtyDelivered: parseFloat(item.qtyDelivered) || 0,
              qtyReturned: parseFloat(item.qtyReturn) || 0,
              dateDelivered: item.dateDelivered || '',
              unit: item.unit,
              notes: item.notes || '',
            };

            // Only include id if in draft mode
            if (isDraftMode && deliveryReceiptId) {
              itemData.id = item.id;
            }

            return itemData;
          }),

          attachmentIds:
            attachmentIds || deliveryReceipt?.attachments.map(file => file.id),
        };
        if ((isDraftMode || viewMode) && deliveryReceiptId) {
          await updateDeliveryReceipt({
            id: deliveryReceiptId,
            data: deliveryReceiptData,
          });
        } else {
          await createDeliveryReceipt(deliveryReceiptData);
        }
        //navigate(`/app/requisition-slip/${id}`);
        saveAsDraft.current = null;
        //supplier.current = null;
        updateMode(null);
      } catch (error) {
        console.log(error);
        showNotification({
          type: 'error',
          message:
            error?.response?.data?.message ||
            'Failed to submit Receiving Report.',
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCancel = () => {
    navigate(`/app/requisition-slip/${id}`);
    updateMode(null);
  };

  const handleCancelReturns = item => {
    setSelectedReturnItem(item);
    setIsCancelReturnsModalOpen(true);
  };

  const handleCancelReturnsConfirm = async () => {
    if (selectedReturnItem?.id) {
      try {
        await cancelReturns({ id: selectedReturnItem.id });

        // Update local state
        setDeliveryItems(prevItems =>
          prevItems.map(item =>
            item.id === selectedReturnItem.id
              ? { ...item, returnQty: 0 }
              : item,
          ),
        );
      } catch (error) {
        console.error('Error canceling returns:', error);
      } finally {
        setIsCancelReturnsModalOpen(false);
        setSelectedReturnItem(null);
      }
    } else {
      try {
        const items = deliveryItems;

        const index = items.findIndex(
          dItem => dItem.itemId === selectedReturnItem.itemId,
        );

        items[index].returnQty = 0;

        setDeliveryItems(items);

        showNotification({
          type: 'success',
          message: 'Returns cancelled successfully',
        });
      } catch (e) {
        showNotification({
          type: 'error',
          message: error?.response?.data?.message || 'Failed to cancel returns',
        });
      } finally {
        setIsCancelReturnsModalOpen(false);
        setSelectedReturnItem(null);
      }
    }
  };

  const handleSort = field => {
    setCurrentSort(prev => {
      const direction = prev[0]?.[1] === 'ASC' ? 'DESC' : 'ASC';

      if (prev[0]?.[1] === 'DESC') return [];

      return [[field, direction]];
    });
  };

  const handleToggleInvoice = () => {
    if (!viewMode || !showInvoice) {
      setShowInvoice(true);
      setIsInvoiceAdded(true);
    }
    if (showInvoice) {
      setShowInvoice(false);
      setIsInvoiceAdded(false);
    }
  };

  const handleRemoveAttachment = () => {};

  // Filtered Data
  const getFilteredData = (data, query) => {
    if (!query.item) return data;
    return data.filter(item =>
      item.item.toLowerCase().includes(query.item.toLowerCase()),
    );
  };

  const filteredData = getFilteredData(deliveryItems, searchQuery);

  //Variables for Steelbar Items
  const steelbarItemsList = steelbarsItemsExtractor(
    filteredData,
    OFMItems?.data,
    isTypeOfm,
  );

  const steelbarItems = matchingAccountCodeExtractor(
    steelbarItemsList,
    filteredData,
  );

  const nonSteelbarItems = nonSteelbarsItemsExtractor(
    steelbarItems,
    filteredData,
  );

  useEffect(() => {
    if (nonSteelbarItems?.length < 1 && steelbarItems?.length > 0) {
      setActiveTab('steel-bars');
    }
  }, [steelbarItems, nonSteelbarItems]);

  const searchItems = [
    {
      name: 'item',
      type: 'text',
      placeholder: 'Search Items',
      isParent: true,
      label: 'Search:',
    },
  ];

  // Modal State Handlers

  const onCheckAttachment = () => {
    setModalData(prevData => ({
      type: 'attachments',
      isOpen: !prevData?.isOpen,
    }));
  };

  const onCheckNote = () => {
    setModalData(prevData => ({
      type: 'notes',
      isOpen: !prevData?.isOpen,
    }));
  };

  const closeModal = () => {
    setSearchFilename('');
    setModalData(prevData => ({
      ...prevData,
      isOpen: !prevData?.isOpen,
    }));
  };

  const closeAttachmentAndNotesModal = () => {
    if (hasNewNotification && modalData.type === 'attachments') {
      seenAttachments();
    } else if (
      deliveryReceiptNotes?.hasNewNotifications &&
      modalData.type === 'notes'
    ) {
      seenAttachments();
    }

    setModalData(prevData => ({
      ...prevData,
      isOpen: !prevData?.isOpen,
    }));
  };

  const modalSetter = (isOpen, mode) => {
    setModalData({
      isOpen: isOpen,
      mode: mode,
    });
  };

  const removeAttachment = async id => {
    try {
      await deleteAttachment(
        { id },
        {
          onSuccess: async () => {
            showNotification({
              type: 'success',
              message: 'Attachment Removed successfully',
            });
            setAttachments(prevAttachments =>
              prevAttachments.filter(attachment => attachment.id !== id),
            );
            //await refetchAttachments();
          },
          onError: error => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message ||
                'Failed to remove attachments',
            });
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  const canViewDownload = () => {
    const allowableRoles = [
      'Purchasing Staff',
      'Purchasing Head',
      'Purchasing Admin',
    ];

    const found = allowableRoles.find(role => role === user.role?.name);
    return found ? true : false;
  };

  const DraftDetails = () => {
    return (
      <>
        <div className="flex flex-col sm:items-center gap-4 mb-4 absolute top-8 right-5">
          {isUserAllowed() && viewMode && (
            <Button
              variant="action"
              hover="action"
              className="color:bg-[#F0F1F1] text-[#420001] max-w-40"
              icon={Download}
              iconPosition="L"
              isLoading={isPdfDownloading}
              onClick={() => handleDownload()}
              disabled={isPdfDownloading}
            >
              Download
            </Button>
          )}
          {(isDraftMode || !viewMode) && (
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4">
              {lastSavedDate && lastSavedTime && (
                <div className="flex gap-2 text-sm whitespace-nowrap">
                  <span className="text-[#219653] font-bold">Draft Saved:</span>
                  <span>{lastSavedDate}</span>
                  <span className="hidden sm:inline">|</span>
                  <span>{lastSavedTime}</span>
                </div>
              )}
              <Button
                className="w-full xl:w-fit border-[#754445] text-[#754445]"
                variant="outline"
                hover="outline"
                onClick={() => modalSetter(true, 'draft')}
                isLoading={isLoading}
              >
                Save Draft
              </Button>
            </div>
          )}
        </div>
      </>
    );
  };

  const showMessageOnError = error => {
    if (error) {
      return <div className="text-red-600/100 text-[12px]">{error}</div>;
    }
  };

  const verifyIfFullyDelivered = () => {
    if (
      deliveryReceipt &&
      deliveryItems.length === deliveryReceipt?.items.length
    ) {
      const found = deliveryItems.find(item => {
        return (
          item?.deliveryStatus !== 'Fully Delivered' &&
          item?.deliveryStatus !== 'Fully Delivered with Returns'
        );
      });

      return !found;
    } else {
      return false;
    }
  };

  if (isDeliveryReceiptLoading) return <div>Loading...</div>;

  const isUserAllowed = () => {
    if (requisition) {
      return (
        requisition?.createdByUser?.id === user.id ||
        requisition?.assignedTo?.id === user.id
      );
    }
    return false;
  };

  let isDeliveryHasInvoice = !deliveryReceipt?.invoiceReport;

  return (
    <React.Fragment>
      {deliveryReceipt &&
        viewMode &&
        isUserAllowed() &&
        isDeliveryHasInvoice && (
          <div className="sticky top-28 z-[3] flex items-center justify-between rounded-lg border border-[#F2994A] bg-[#FFFAF5] p-4 shadow-sm">
            <div className="w-full flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center">
                <FileClock className="text-[#754445]" />
              </div>
              <div>
                <p className="text-sm font-bold">You have a pending action:</p>
                <p className="text-sm font-semibold text-gray-900">
                  Receive Invoice
                </p>
              </div>
              <div className="flex items-center gap-2 ml-auto">
                <Button
                  className="min-w-24 bg-green-600 text-white hover:bg-green-700"
                  onClick={() => {
                    navigate(`/app/requisition-slip/${id}/invoice/create`);
                  }}
                >
                  Receive Invoice
                </Button>
              </div>
            </div>
          </div>
        )}
      <div className="space-y-6">
        {/* Save Draft Header Button */}

        <DraftDetails />

        {/* Form Header */}

        <div className="space-y-2 mb-6">
          <div className="flex gap-2">
            {isDraftMode ? (
              <span className="text-lg font-bold">
                R.R. Number <span className="text-[#F2994A]">(draft)</span>:
              </span>
            ) : (
              <span className="text-lg font-bold">R.R. Number:</span>
            )}
            <span className="text-lg font-medium">
              {deliveryReceipt?.drNumber || '---'}
            </span>
            <div className="grow"></div>
            <div className="w-48">
              <span className="text-sm font-bold">Status</span>
            </div>
          </div>

          <div className="flex gap-2">
            <span className="text-sm font-bold">R.S. Number:</span>
            <span className="text-sm font-medium">{requisition?.rsNumber}</span>
            <div className="grow"></div>
            <div className="w-48">
              <Status
                status={
                  isDraftMode || !deliveryReceipt?.drNumber
                    ? 'dr_draft'
                    : 'Delivered'
                }
              />
            </div>
          </div>
        </div>

        {/* Receiving Report Form */}
        <Form
          onSubmit={handleSaveDR}
          schema={drFormSchema}
          hasErrorSpace={false}
          ref={mainFormRef}
          options={{
            //shouldUnregister: true,
            defaultValues: {
              existingAttachments: deliveryReceipt?.attachments,
              purchaseOrderNo: selectedPo || deliveryReceipt?.poId,
              supplier: poDetails?.supplier?.name ?? '--',
              invoiceNo: deliveryReceipt?.invoiceNumber ?? '',
              //deliveryReceipt?.invoice?.attachment
              invoiceDate:
                setDefaultDateValue(deliveryReceipt?.issuedDate) ?? '',
              supplierDeliveryIssuedDate:
                setDefaultDateValue(
                  deliveryReceipt?.supplierDeliveryIssuedDate,
                ) ?? '',
              totalSales: deliveryReceipt?.invoice?.totalSales ?? 0,
              vatAmount: deliveryReceipt?.invoice?.vatAmount ?? 0,
              vatExemptedAmount:
                deliveryReceipt?.invoice?.vatExemptedAmount ?? 0,
              zeroRatedAmount: deliveryReceipt?.invoice?.zeroRatedAmount ?? 0,
              note: viewMode ? '' : (deliveryReceipt?.note ?? ''),
            },
          }}
        >
          {({
            control,
            watch,
            register,
            setValue,
            reset,
            formState: { errors },
          }) => {
            const purchaseOrderNo = watch('purchaseOrderNo');
            const previousPO = useRef(null);
            const pendingPO = useRef(null);

            const updateValue = (fieldName, object) => {
              let value = object;

              if (value.length > 1 && value.charAt(0) === '0') {
                value = value.slice(1);
              }

              object === ''
                ? setValue(fieldName, 0)
                : setValue(fieldName, value);
            };

            const hasError = () => {
              return (
                !!errors?.invoiceAttachment ||
                !!errors?.invoiceDate ||
                !!errors?.invoiceNo
              );
            };

            useEffect(() => {
              if (attachments) {
                setValue('existingAttachments', attachments);
              }
            }, [attachments]);

            useEffect(() => {
              if (errors?.invoiceAttachment) {
                if (errors?.invoiceAttachment) {
                  showNotification({
                    type: 'error',
                    message: errors?.invoiceAttachment?.message,
                  });
                  //
                }
              }
            }, [errors?.invoiceAttachment]);

            useEffect(() => {
              if (!previousPO.current) {
                previousPO.current = purchaseOrderNo;
                return;
              }
              if (previousPO.current !== purchaseOrderNo) {
                pendingPO.current = purchaseOrderNo;
                handleChangeSelection();
                setValue('purchaseOrderNo', previousPO.current);
              }

              if (isConfirmed) {
                // Store current values before reset
                const currentPO = pendingPO.current;
                const currentSupplier = supplierRef.current;

                // Reset entire form
                reset();

                // Restore preserved values
                setValue('purchaseOrderNo', currentPO);
                setValue('supplier', currentSupplier);

                // Update refs
                previousPO.current = currentPO;
                setIsConfirmed(false);
              }

              if (isCancelled) {
                setValue('purchaseOrderNo', previousPO.current);
                setIsCancelled(false);
              }
            }, [purchaseOrderNo, isConfirmed, isCancelled, reset, setValue]);

            const selectedPO = SAMPLE_PO_DATA.find(
              po => po.poNumber === purchaseOrderNo,
            );
            const supplier = selectedPO ? selectedPO.supplier : '';

            supplierRef.current = supplier;

            return (
              <div className="space-y-6 bg-white border rounded-lg p-6">
                <div className="grid grid-cols-2 gap-6 pb-2">
                  <div>
                    <Select
                      label="Purchase Order No."
                      name="purchaseOrderNo"
                      disabled={viewMode || deliveryReceipt?.isDraft}
                      error={errors?.purchaseOrderNo?.message}
                      control={control}
                      handleChange={handleSelectedPO}
                      options={purchaseOrderOptions}
                      {...register('purchaseOrderNo')}
                      searchable
                    />
                    {showMessageOnError(errors?.purchaseOrderNo?.message)}
                  </div>

                  <div>
                    <Input
                      control={control}
                      name="invoiceDate"
                      label="RR Issued Date"
                      type="date"
                      disabled={viewMode}
                      error={errors?.invoiceDate?.message}
                      {...register('invoiceDate')}
                      maxDate={setDefaultDateValue(new Date())}
                    />
                    {showMessageOnError(errors?.invoiceDate?.message)}
                  </div>

                  <div>
                    <Input
                      control={control}
                      name="invoiceNo"
                      label="Supplier Delivery Invoice No."
                      maxLength={50}
                      disabled={viewMode}
                      error={errors?.invoiceNo?.message}
                      {...register('invoiceNo')}
                    />
                    {showMessageOnError(errors?.invoiceNo?.message)}
                  </div>
                  <div>
                    <Input
                      control={control}
                      name="supplierDeliveryIssuedDate"
                      label="Supplier Delivery Issued Date"
                      type="date"
                      disabled={viewMode}
                      error={errors?.supplierDeliveryIssuedDate?.message}
                      {...register('supplierDeliveryIssuedDate')}
                      maxDate={setDefaultDateValue(new Date())}
                    />
                    {showMessageOnError(
                      errors?.supplierDeliveryIssuedDate?.message,
                    )}
                  </div>
                </div>
                {viewMode && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button
                        type="button"
                        onClick={onCheckAttachment}
                        icon={FileIcon}
                        iconSize="h-5 w-auto"
                        variant="outline"
                        hover="outline"
                        label={
                          <div className="flex justify-between align-middle">
                            <p>Attachments</p>
                            {hasNewNotification && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8]">
                                New Attachment/s
                              </Pill>
                            )}
                          </div>
                        }
                      >
                        Check Attachments
                      </Button>
                      <Button
                        type="button"
                        onClick={onCheckNote}
                        icon={CommentIcon}
                        iconSize="h-5 w-auto"
                        variant="outline"
                        hover="outline"
                        label={
                          <div className="flex justify-between align-middle">
                            <p>Notes</p>
                            {deliveryReceiptNotes?.hasNewNotifications && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8]">
                                New Note/s
                              </Pill>
                            )}
                          </div>
                        }
                      >
                        Check Notes
                      </Button>
                      <div className="flex flex-col">
                        <FileUpload
                          isUploading={isUploading}
                          ref={attachmentRef}
                          control={control}
                          modelType={MODELS.DELIVERY_RECEIPT}
                          name={'fileAttachments'}
                          attachments={attachments}
                          onAttachmentChange={val =>
                            setHasAttachments(!!val?.length)
                          }
                          error={errors?.fileAttachments?.message}
                          fileTypeRestrictions=".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.csv,.docx"
                        />

                        <div className="flex justify-between items-center mt-1">
                          <div className="text-[#4F4F4F] text-[12px]"></div>
                          <Button
                            variant="outline"
                            type="button"
                            onClick={submitAttachments}
                            disabled={!hasAttachments === true ? true : null}
                            hover="outline"
                            className="w-32 h-8 text-sm text-[#420001] border-[#420001] ml-4"
                          >
                            Submit
                          </Button>
                        </div>
                      </div>

                      <div className="col-span-1">
                        <div className="flex flex-col">
                          <TextArea
                            label="Add Notes"
                            name={'notes'}
                            registration={{ ...control.register('notes') }}
                            control={control}
                            value={note}
                            placeholder="Input notes"
                            onInput={e => setNote(e.target.value)}
                            maxCharacters={100}
                            ref={noteRef}
                            className="mb-0"
                          />
                          <div className="flex justify-between items-center mt-1">
                            <div className="text-[#4F4F4F] text-[12px]"></div>
                            <Button
                              variant="outline"
                              type="button"
                              onClick={onSubmitNote}
                              disabled={!note?.length ? true : null}
                              hover="outline"
                              className="w-32 h-8 text-sm text-[#420001] border-[#420001] ml-4"
                            >
                              Submit
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                <div
                  className={
                    !viewMode
                      ? 'grid grid-cols-2 gap-6'
                      : 'hidden grid grid-cols-2 gap-6'
                  }
                >
                  <div className="flex flex-col">
                    <File
                      isUploading={isUploading}
                      ref={fileRef}
                      control={control}
                      name="attachments"
                      attachments={deliveryReceipt?.attachments}
                      hasAttachments={hasAttachments}
                      error={errors['attachments']}
                      hasError={errors['attachments'] ? true : false}
                    />

                    {isUploading && (
                      <div className="flex items-center">
                        <div className="text-sm mr-2 font-semibold">
                          {uploadProgress}%
                        </div>
                        <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
                          <div
                            className="bg-blue-500 h-full transition-all duration-200 ease-out"
                            style={{ width: `${uploadProgress}%` }}
                          />
                        </div>
                      </div>
                    )}
                    <div className="flex flex-col gap-2 bg-gray-50 mb-2 rounded-lg shadow-sm max-h-36 overflow-y-auto">
                      {(!viewMode || isDraftMode) &&
                        attachments?.map(({ id, modelId, fileName }, index) => (
                          <div key={modelId} className="flex w-full">
                            <div className="w-full">
                              <Input
                                id={id}
                                name={`${id}`}
                                type="text"
                                value={fileName}
                                readOnly
                                disabled
                                className="w-full relative disabled:bg-gray-50 disabled:opacity-80 disabled:shadow-none text-black font-normal border-0"
                                hasIcon={!viewMode}
                                renderIcon={XIcon}
                                iconHandler={() =>
                                  !viewMode && removeAttachment(id)
                                }
                              />
                            </div>
                          </div>
                        ))}
                      <Input
                        className="hidden"
                        control={control}
                        name="existingAttachments"
                        label=""
                        type="text"
                        disabled={viewMode}
                        {...register('existingAttachments')}
                      />
                    </div>
                    <p className="text-[#4F4F4F] text-[12px] mt-[0px] pl-1">
                      The maximum size for each file is 25 MB. File formats -
                      PNG, JPG, JPEG, PDF, Excel, CSV.
                    </p>
                    {showMessageOnError(errors['attachments']?.message)}
                  </div>
                  <div>
                    <TextArea
                      control={control}
                      placeholder="Input Note"
                      name="note"
                      error={errors['note']}
                      label="Add Note"
                      maxLength={100}
                    />
                  </div>
                </div>
              </div>
            );
          }}
        </Form>

        {/* Table */}

        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-y-2">
            <div className="flex">
              <div className="w-64 flex-none">
                <h2 className="text-xl font-bold">Item/s</h2>
              </div>
              <div className="size-14 grow"></div>
              <div className="w-32 flex-none">
                {' '}
                <div className="flex justify-end">
                  {(!deliveryReceipt || deliveryReceipt?.isDraft) && (
                    <Button
                      iconPosition="L"
                      icon={AddIcon}
                      disabled={!selectedPo ? true : null}
                      className="w-full xl:w-fit py-3 px-5 mt-2 xl:mt-0 float-right mb-5"
                      onClick={() => {
                        setSelectedItem(null);
                        refetchPurchaseOrderItems();
                        setIsEditModalOpen(true);
                      }}
                    >
                      Add Item/s
                    </Button>
                  )}
                </div>
              </div>
            </div>

            <SearchBar
              searchItems={searchItems}
              onSearch={setSearchQuery}
              className="p-0 bg-transparent hidden"
            />
            {steelbarItems?.length && nonSteelbarItems?.length ? (
              <div className="my-6">
                <Tabs activeTab={activeTab} tabs={tabs} />
              </div>
            ) : null}
            <DeliveryReceiptItemsTable
              tableData={
                activeTab === 'items' ? nonSteelbarItems : steelbarItems
              }
              isLoading={
                isLoading || isRequisitionLoading || isDeliveryReceiptLoading
              }
              setPage={setPage}
              setLimit={setLimit}
              currentPage={currentPage}
              hasInvoice={
                deliveryReceipt?.invoiceReport || isUserAllowed() === false
                  ? true
                  : false
              }
              limit={currentLimit}
              onSort={handleSort}
              currentSort={currentSort}
              hasSelectedPO={selectedPo ? true : false}
              handleItemClick={handleItemClick}
              handleEditClick={handleEditClick}
              handleHistoryClick={handleHistoryClick}
              handleCancelReturns={handleCancelReturns}
              onAddData={() => {
                refetchPurchaseOrderItems();
                setIsEditModalOpen(true);
              }}
              viewMode={viewMode || isDeliveryHasInvoice}
              draftMode={isDraftMode}
            />
          </div>

          {/* Modals */}

          <ActionModal
            header={capitalizeFirstLetter(modalData?.type)}
            onClose={closeAttachmentAndNotesModal}
            isOpen={
              modalData?.isOpen &&
              (modalData?.type === 'attachments' || modalData?.type === 'notes')
            }
          >
            <DeliveryReceiptAttachmentModalContent
              hasNewAttachment={false}
              onSearch={searchKey => setSearchFilename(searchKey)}
              onDateFilter={dateValues => {
                setDateData({
                  dateFrom: dateValues?.dateFrom,
                  dateTo: dateValues?.dateTo,
                });
              }}
              onRemoveAttachment={data => {
                setAttachments(data);
                setIsDeleteAttachmentModalOpen(true);
              }}
              hasNewNotification={
                modalData?.type === 'attachments'
                  ? hasNewNotification
                  : deliveryReceiptNotes?.hasNewNotification
              }
              modalType={modalData?.type}
              onClose={closeAttachmentAndNotesModal}
              data={
                modalData?.type === 'attachments'
                  ? attachments
                    ? {
                        data: fileAttachments?.data,
                      }
                    : null
                  : deliveryReceiptNotes
              }
            />
          </ActionModal>

          <ConfirmModal
            isOpen={isDeleteAttachmentModalOpen}
            onClose={() => setIsDeleteAttachmentModalOpen(false)}
            onConfirm={handleRemoveAttachment}
            header="Delete Attachment"
            message="You are about to delete this attachment. This action is irreversible. Press continue if you want to proceed with this action."
          />

          <ConfirmModal
            isOpen={isChangeSelectionModalOpen}
            header="Change Purchase Order"
            onClose={() => handleCancelSelection()}
            onConfirm={() => handleContinueSelection()}
            message="Changing the purchase order will remove the filled information below. Press continue if you want to proceed with this action."
          />
          <ConfirmModal
            isOpen={modalData?.isOpen && modalData?.mode === 'draft'}
            onClose={closeModal}
            onConfirm={() => {
              saveAsDraft.current = true;
              mainFormRef.current.requestSubmit();
            }}
            header="Save Draft"
            message="You are about to save this request as a draft. Make sure all items are correct. Press save if you want to proceed with this action."
            confimrButtonText="Save"
            isLoading={isCreatingDrPending}
          />

          <ItemDetailsModal
            isOpen={isItemDetailsModalOpen}
            onClose={() => setIsItemDetailsModalOpen(false)}
            onCancel={item => handleCancelReturns(item)}
            item={selectedItem}
            mode={mode}
            onSave={handleSave}
          />

          <EditDetailsModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            item={selectedItem}
            onSave={handleSave}
            itemList={poItemsList}
          />
          {/* in View mode - hide when all are fully delivered */}
          {isDeliveryHasInvoice && isUserAllowed() && (
            <ActionButtons
              onSaveDraft={() => modalSetter(true, 'draft')}
              onCancel={handleCancel}
              onSubmit={handleSaveDR}
              isViewMode={viewMode}
              lastSavedDate={lastSavedDate}
              lastSavedTime={lastSavedTime}
              isLoading={isCreatingDeliveryReceipt}
              formRef={mainFormRef}
              isDraft={saveAsDraft}
            />
          )}

          <CancelModal
            isOpen={isCancelReturnsModalOpen}
            header="Cancel Returns"
            onClose={() => setIsCancelReturnsModalOpen(false)}
            onContinue={handleCancelReturnsConfirm}
            message="You are about to cancel the returns. All changes will not be saved. Press continue if you want to proceed with this action."
            isLoading={isCancelingReturns}
          />
        </div>
      </div>
    </React.Fragment>
  );
};

DeliveryReceipt.propTypes = {
  currentPage: PropTypes.number.isRequired,
  currentLimit: PropTypes.number.isRequired,
  setPage: PropTypes.func.isRequired,
  setLimit: PropTypes.func.isRequired,
  setSort: PropTypes.func.isRequired,
  currentSort: PropTypes.array,
  sortBy: PropTypes.object,
  permissions: PropTypes.object,
  mode: PropTypes.string,
};
