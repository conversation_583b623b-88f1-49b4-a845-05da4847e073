import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@src/components/ui/Button';
import { Form, Input } from '@src/components/ui/Form';
import { Modal } from '@src/components/ui/Modal';
import { CancelModal } from '@src/components/ui/Modal';
import { PurchaseOrderItemsTable } from './PurchaseOrderItemsTable';
import { SupplierDetailsModal } from './SupplierDetailsModal';
import { ProjectDetailsModal } from './ProjectDetailsModal';
import { CompanyDetailsModal } from './CompanyDetailsModal';
import { ApproverItemsTable } from './ApproverItemsTable';
import { AssignedStaffSection } from './AssignedStaffSection';
import { ApproversSection } from './ApproversSection';
import FileClock from '@assets/icons/file-clock.svg?react';
import PercentIcon from '@assets/icons/percent.svg?react';
import Download from '@assets/icons/download.svg?react';
import PesoIcon from '@assets/icons/peso.svg?react';
import { RadioButtonGroup } from '@components/ui/RadioButtonGroup';
import { PendingAction } from './PendingAction';
import { Tabs } from '@src/components/ui/Tabs';
import { useGetOFMItems } from '@src/features/ofm/api';
import { AttachmentsAndNotes } from './AttachmentsAndNotes';
import { setDefaultDateValue } from '@utils/dateFormat';
import PropTypes from 'prop-types';
import { useGetRequisitionSlip } from '@features/dashboard/api';
import { StatusSection } from '@src/features/dashboard/components/StatusSection';
import AdditionalInfoModal from '@src/components/ui/Modal/AdditionalInfoModal';
import {
  useGetPurchaseOrder,
  useGetPurchaseOrderAssignee,
  useGetPurchaseOrderApprovers,
  useCancelPO,
  useGetRequestor,
  useGetItems,
} from '../api';
import { useDownloadTemplatePdf } from '@src/hooks/useDownloadTemplatePdf';
import { useGetSupplierDetails } from '@src/features/supplier/api';
import { useUserStore } from '@store';
import { useGetUsersForAdhoc } from '../api';
import { useNotification } from '@src/hooks/useNotification';
import useRSTabStore from '@src/store/rsTabStore';
import VisitMainRSButton from '@src/components/ui/Button/VisitMainRSButton';
import useTableTabs from '@src/hooks/useTableTabs';
export const PurchaseOrderDetail = ({
  currentPage,
  currentLimit,
  setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
}) => {
  const { user } = useUserStore();
  const { resetTabs } = useRSTabStore();
  const [isCancelReqModalOpen, setIsCancelReqModalOpen] = useState(false);
  const [newApprover, setNewApprover] = useState(null);
  const [sortedApprovers, setSortedApprovers] = useState([]);
  const [userList, setUserList] = useState([]);
  const [currentPos, setCurrentPos] = useState(0);
  const { id, rsId } = useParams();
  const [searchQuery, setSearchQuery] = useState({});
  const { showNotification } = useNotification();
  const {
    data: purchaseOrderDetail,
    isLoading,
    error,
  } = useGetPurchaseOrder(id, {
    enabled: !!id,
  });
  const { data: requisition, isLoading: isLoadingRequisition } =
    useGetRequisitionSlip(rsId);
  const { data: items, isLoading: isuseGetItemsLoading } = useGetItems({
    id: id,
    search: searchQuery.itemName,
    sortBy,
    paginate: false,
  });

  const { data: rsRequestor } = useGetRequestor(rsId, {
    enabled: !!rsId,
  });

  const { mutateAsync: cancelPO } = useCancelPO();

  const { mutate: downloadPdf, isPending: isPdfDownloading } =
    useDownloadTemplatePdf();

  const { data: OFMItems } = useGetOFMItems({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });
  const { data: users } = useGetUsersForAdhoc();
  const REJECTED_PO = 'reject_po';
  const CANCELLED_PO = 'cancelled_po';
  const FOR_DELIVERY = 'for_delivery';
  // const FOR_PO_REVIEW = 'for_po_review';

  const { data: approversResponse, isLoading: isApproversResponse } =
    useGetPurchaseOrderApprovers(id, { enabled: !!id });

  const isUserAnApprover = () => {
    return approversResponse?.data?.some(approver => {
      return approver?.approver?.id === user.id;
    });
  };

  const userTypeForNote = isUserAnApprover() ? 'Approver' : null;

  const isUserACurrentApprover = useMemo(() => {
    const currentApprover = sortedApprovers?.find(
      approver => approver.status === 'pending',
    );

    if (!currentApprover || !currentApprover?.approver) {
      return false;
    }

    const dateRequired = new Date(
      setDefaultDateValue(purchaseOrderDetail?.requisition?.dateRequired),
    ).setHours(0, 0, 0, 0);

    // Ensure userLeaves is an array before accessing length
    const userLeaves = currentApprover?.approver?.userLeaves || [];

    if (userLeaves.length > 0) {
      const found = userLeaves.find(leave => {
        const startDate = new Date(leave.startDate).setHours(0, 0, 0, 0);
        const endDate = new Date(leave.endDate).setHours(0, 0, 0, 0);
        return startDate <= dateRequired && dateRequired <= endDate;
      });
      if (!found && currentApprover?.approver?.id === user.id) {
        return true;
      } else if (!found && currentApprover?.approverId !== user.id) {
        return false;
      }
      if (
        found &&
        currentApprover?.altApproverId &&
        currentApprover?.altApproverId !== user.id
      ) {
        return false;
      } else if (
        found &&
        !currentApprover?.altApprover &&
        currentApprover?.approver?.id === user.id
      ) {
        return true;
      } else if (found && currentApprover?.altApproverId === user.id) {
        return true;
      }

      return false;
    }

    return currentApprover?.approver?.id === user.id;
  }, [sortedApprovers, purchaseOrderDetail, user]);

  const hasAttachmentBadge = false;
  const hasCommentBadge = false;
  const navigate = useNavigate();
  // const rsId = location?.state?.rsId;

  const supplierId = purchaseOrderDetail?.supplier?.id;

  const { data: supplierDetails, isLoading: isLoadingSupplier } =
    useGetSupplierDetails({
      id: supplierId,
      config: {
        enabled:
          purchaseOrderDetail?.supplierType === 'supplier' && !!supplierId,
      },
    });

  const { data: assigneeResponse, isLoading: isAssigneeResponse } =
    useGetPurchaseOrderAssignee(id, {
      enabled: !!id,
    });

  const isAssignee = assigneeResponse?.data?.approver?.id === user.id;

  const [modalData, setModalData] = useState({
    isOpen: false,
    attachments: [],
    isUploading: false,
    supplier: null,
  });

  const { setActiveTab, activeTab, tabs } = useTableTabs(setPage, currentPage);

  useEffect(() => {
    if (users && approversResponse) {
      const filteredUsers = users?.data?.filter(
        user =>
          !approversResponse?.data?.some(
            userToRemove => user.id === userToRemove?.approver?.id,
          ),
      );
      setUserList(filteredUsers);
    }
  }, [users, approversResponse]);

  useEffect(() => {
    if (approversResponse) {
      const sortedApprovers = approversResponse?.data?.sort((user1, user2) => {
        if (user1.level > user2.level) return 1;
        if (user1.level < user2.level) return -1;

        if (user1.isAdhoc === true) return 1;
        if (user1.isAdhoc === false) return -1;
      });
      setSortedApprovers(sortedApprovers);
    }
  }, [approversResponse]);

  const orderData = {
    poNumber: 'RS-12AA0000000001',
    rsNumber: 'RS-12AA0000000001',
    status: 'po_approval',
    userRole: 'USER ROLE',
    assignedTo: { id: 1, name: 'Maria Josefina Maria Locsin' },
    approvers: [
      // mirroring most of the data from RequisitionSlip.js
      {
        approver: {
          id: 1,
          fullName: 'Maria Josefina Maria Locsin',
          userLeaves: [],
        },
        approverId: 11,
        isAltApprover: false,
        level: 1,
        status: 'approved',
      },
      {
        approver: {
          id: 2,
          fullName: 'John Doe Long Name Text',
          userLeaves: [],
        },
        approverId: 22,
        isAltApprover: false,
        level: 2,
        status: 'rejected',
      },
      {
        approver: {
          id: 3,
          fullName: 'Approver Name 3',
          userLeaves: [],
        },
        approverId: 33,
        isAltApprover: false,
        level: 3,
        status: 'pending',
      },
    ],
    supplier: {
      name: 'Sample Supplier',
      deliveryAddress: 'SAMPLE ADDRESS',
      terms: 'COD',
      deposit: '30%',
      warranty: 'Sample Warranty',
    },
    attachments: {
      data: [
        {
          id: 9999,
          fileName: 'Asset_Accountability_Policy.pdf',
          path: '/purchase-orders/Asset_Accountability_Policy.pdf',
          model: 'purchase-order',
          modelId: 777,
          createdAt: '2025-01-15T06:33:52.366Z ',
          updateAt: '2025-01-15T06:33:52.366Z',
          userAttachment: { id: 2, firstName: 'purchasing', lastName: 'staff' },
        },
        {
          id: 10001,
          fileName: 'Vendor_Contract_Agreement.pdf',
          path: '/purchase-orders/Vendor_Contract_Agreement.pdf',
          model: 'purchase-order',
          modelId: 778,
          createdAt: '2025-01-15T06:45:30.366Z',
          updatedAt: '2025-01-15T06:45:30.366Z',
          userAttachment: { id: 3, firstName: 'finance', lastName: 'manager' },
        },
        {
          id: 10002,
          fileName: 'Invoice_12345.pdf',
          path: '/purchase-orders/Invoice_12345.pdf',
          model: 'purchase-order',
          modelId: 779,
          createdAt: '2025-01-15T07:01:15.366Z',
          updatedAt: '2025-01-15T07:01:15.366Z',
          userAttachment: { id: 4, firstName: 'admin', lastName: 'clerk' },
        },
        {
          id: 10003,
          fileName: 'Supplier_Onboarding_Documents.pdf',
          path: '/purchase-orders/Supplier_Onboarding_Documents.pdf',
          model: 'purchase-order',
          modelId: 780,
          createdAt: '2025-01-15T07:15:00.366Z',
          updatedAt: '2025-01-15T07:15:00.366Z',
          userAttachment: {
            id: 5,
            firstName: 'logistics',
            lastName: 'officer',
          },
        },
        {
          id: 10004,
          fileName: 'Budget_Approval_Request.pdf',
          path: '/purchase-orders/Budget_Approval_Request.pdf',
          model: 'purchase-order',
          modelId: 781,
          createdAt: '2025-01-15T07:30:45.366Z',
          updatedAt: '2025-01-15T07:30:45.366Z',
          userAttachment: { id: 6, firstName: 'budget', lastName: 'analyst' },
        },
        {
          id: 10005,
          fileName: 'Payment_Terms.pdf',
          path: '/purchase-orders/Payment_Terms.pdf',
          model: 'purchase-order',
          modelId: 782,
          createdAt: '2025-01-15T07:45:20.366Z',
          updatedAt: '2025-01-15T07:45:20.366Z',
          userAttachment: { id: 7, firstName: 'accounting', lastName: 'staff' },
        },
        {
          id: 10006,
          fileName: 'Equipment_Maintenance_Schedule.pdf',
          path: '/purchase-orders/Equipment_Maintenance_Schedule.pdf',
          model: 'purchase-order',
          modelId: 783,
          createdAt: '2025-01-15T08:00:05.366Z',
          updatedAt: '2025-01-15T08:00:05.366Z',
          userAttachment: {
            id: 8,
            firstName: 'maintenance',
            lastName: 'manager',
          },
        },
        {
          id: 10007,
          fileName: 'Annual_Procurement_Plan.pdf',
          path: '/purchase-orders/Annual_Procurement_Plan.pdf',
          model: 'purchase-order',
          modelId: 784,
          createdAt: '2025-01-15T08:15:50.366Z',
          updatedAt: '2025-01-15T08:15:50.366Z',
          userAttachment: { id: 9, firstName: 'project', lastName: 'manager' },
        },
        {
          id: 10008,
          fileName: 'Shipping_Details.pdf',
          path: '/purchase-orders/Shipping_Details.pdf',
          model: 'purchase-order',
          modelId: 785,
          createdAt: '2025-01-15T08:30:35.366Z',
          updatedAt: '2025-01-15T08:30:35.366Z',
          userAttachment: {
            id: 10,
            firstName: 'warehouse',
            lastName: 'supervisor',
          },
        },
        {
          id: 10009,
          fileName: 'Supplier_Payment_Receipt.pdf',
          path: '/purchase-orders/Supplier_Payment_Receipt.pdf',
          model: 'purchase-order',
          modelId: 786,
          createdAt: '2025-01-15T08:45:20.366Z',
          updatedAt: '2025-01-15T08:45:20.366Z',
          userAttachment: {
            id: 11,
            firstName: 'accounts',
            lastName: 'payable',
          },
        },
        {
          id: 10010,
          fileName: 'Purchase_Order_Confirmation.pdf',
          path: '/purchase-orders/Purchase_Order_Confirmation.pdf',
          model: 'purchase-order',
          modelId: 787,
          createdAt: '2025-01-15T09:00:05.366Z',
          updatedAt: '2025-01-15T09:00:05.366Z',
          userAttachment: {
            id: 12,
            firstName: 'purchasing',
            lastName: 'officer',
          },
        },
      ],
      hasNewNotification: true,
      total: 11,
    },
    notes: {
      lastUpdatedAt: '23 July 2024',
      users: [
        { id: 99, name: 'John Doe', role: 'Requestor' },
        { id: 9, name: 'Jane Doe', role: 'Approver' },
        { id: 24, name: 'Lysa Dane', role: 'Approver' },
        { id: 42, name: 'Lani Plata', role: 'Approver' },
      ],
      data: [
        {
          date: '23 July 2024',
          history: [
            // sorted from newest to oldest
            {
              id: 120,
              userId: 99,
              notes:
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. ',
              status: 'notes',
            },
            {
              id: 119,
              userId: 9,
              notes: 'OK',
              status: 'notes',
            },
          ],
        },
        {
          date: '18 July 2024',
          history: [
            {
              id: 118,
              userId: 24,
              notes: 'No! No! No!',
              status: 'disapproval',
            },
            {
              id: 117,
              userId: 42,
              notes: 'Will hand over to Lysa',
              status: 'notes',
            },
          ],
        },
        {
          date: '10 July 2024',
          history: [
            {
              id: 116,
              userId: 99,
              notes: 'Why? Why? Why?',
              status: 'disapproval',
            },
          ],
        },
      ],
      hasNewNotification: false,
    },
  };

  const handleSupplierDetailsClick = () => {
    setModalData({
      isOpen: true,
      type: 'supplier',
      supplier: {
        name: supplierDetails?.name,
        address: supplierDetails?.address,
        citizenshipCode: supplierDetails?.citizenshipCode,
        natureOfIncome: supplierDetails?.natureOfIncome,
        tin: supplierDetails?.tin,
        iccode: supplierDetails?.iccode,
        contactPerson: supplierDetails?.contactPerson,
        contactNumber: supplierDetails?.contactNumber,
        lineOfBusiness: supplierDetails?.lineOfBusiness,
        status: supplierDetails?.status,
        updatedAt: supplierDetails?.updatedAt,
      },
    });
  };

  const closeModal = () => {
    setModalData(prevData => ({
      ...prevData,
      isOpen: !prevData?.isOpen,
    }));
  };

  const handleDownload = () => {
    downloadPdf({
      type: 'purchase-order',
      id: id,
    });
  };

  const canViewDownload = () => {
    const allowableRoles = [
      'Purchasing Staff',
      'Purchasing Head',
      'Purchasing Admin',
    ];

    const found = allowableRoles.find(role => role === user.role?.name);
    return found || requisition?.createdByUser?.id === user.id ? true : false;
  };

  const onCloseReqModal = async () => {
    try {
      await cancelPO(id);
      showNotification({
        type: 'success',
        message: 'Request has been cancelled',
      });
      setIsCancelReqModalOpen(false);
      // navigate(-1);
    } catch (error) {
      showNotification({
        type: 'error',
        message: 'Failed to cancel request',
      });
    }
  };

  const handleAddAdhoc = newApprover => {
    setNewApprover(newApprover);

    const pos = sortedApprovers?.findIndex(approver => {
      return approver.status === 'pending' && approver?.isAdhoc === false;
    });
    setCurrentPos(pos);
    const newApproverArr = sortedApprovers;

    const approverItem = {
      id: newApprover?.value,
      level: sortedApprovers[pos].level,
      status: 'pending',
      isAdhoc: true,
      isNew: true,
      approver: {
        fullName: newApprover?.key,
      },
    };
    newApproverArr.splice(pos + 1, 0, approverItem);
  };

  const onDeleteAdhoc = () => {
    const newApproverArr = sortedApprovers;
    newApproverArr.splice(currentPos + 1, 1);
    setCurrentPos(0);
    setNewApprover(null);
  };

  const onEditAdhoc = newApprover => {
    setNewApprover(newApprover);

    const newApproverArr = sortedApprovers;

    if (newApproverArr) {
      newApproverArr[currentPos + 1] = {
        id: newApprover?.value,
        level: newApproverArr[currentPos].level,
        status: 'pending',
        isAdhoc: true,
        isNew: true,
        approver: {
          fullName: newApprover?.key,
        },
      };
    }
  };

  const getModalButtonName = () => {
    switch (purchaseOrderDetail?.supplierType) {
      case 'supplier':
        return 'Supplier';
      case 'company':
        return 'Company';
      case 'project':
        return 'Project';
    }
  };

  if (isLoading || isLoadingRequisition) return <div>...Loading </div>;

  const hasSteelbars = items?.data?.some(item => item?.isSteelbars === true);
  const onlySteelbars = items?.data?.every(item => item?.isSteelbars === true);

  return (
    <>
      {requisition?.actions?.hasForDelivery &&
        purchaseOrderDetail.status === FOR_DELIVERY &&
        (requisition?.createdByUser?.id === user.id ||
          requisition?.assignedTo?.id === user.id) && (
          <div className="sticky top-28 z-[3] flex items-center justify-between rounded-lg border border-[#F2994A] bg-[#FFFAF5] p-4 shadow-sm">
            <div className="w-full flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center">
                <FileClock className="text-[#754445]" />
              </div>
              <div>
                <p className="text-sm font-bold">You have a pending action:</p>
                <p className="text-sm font-semibold text-gray-900">
                  Receiving Report
                </p>
              </div>
              <div className="flex items-center gap-2 ml-auto">
                <Button
                  className="min-w-24 bg-green-600 text-white hover:bg-green-700"
                  onClick={() => {
                    navigate(`/app/receiving-report/create/${rsId}`);
                  }}
                >
                  Confirm Delivery
                </Button>
              </div>
            </div>
          </div>
        )}

      <div className="flex flex-col sm:items-center gap-4 mb-4 absolute top-8 right-5">
        {canViewDownload() && (
          <Button
            variant="action"
            hover="action"
            className="color:bg-[#F0F1F1] text-[#420001] max-w-40"
            icon={Download}
            iconPosition="L"
            isLoading={isPdfDownloading}
            onClick={() => handleDownload()}
            disabled={isPdfDownloading}
          >
            Download
          </Button>
        )}
      </div>

      <div className="p-6">
        {/* Note: show this to Approvers only */}
        {isUserACurrentApprover &&
          purchaseOrderDetail?.status !== 'for_po_review' &&
          purchaseOrderDetail?.status !== REJECTED_PO &&
          purchaseOrderDetail?.status !== CANCELLED_PO && (
            <PendingAction
              id={purchaseOrderDetail?.id}
              newApprover={newApprover}
              approvers={sortedApprovers}
              userList={userList}
              onAddAdhoc={newApprover => handleAddAdhoc(newApprover)}
            />
          )}

        {/* Header with PO Numbers and Actions */}
        <div className="flex justify-between items-center border-t pt-5 border-b pb-5 mb-6">
          <div>
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold">P.O. Number:</span>
              <span className="text-lg font-semibold">
                {purchaseOrderDetail?.poNumber}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-bold">R.S. Number:</span>
              <span className="font-semibold">{requisition?.rsNumber}</span>
            </div>
          </div>
          <div className="flex gap-10">
            <VisitMainRSButton rsId={rsId} />
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Left Column - Form */}
          <div className="flex-1 bg-white p-6 rounded-lg shadow-sm">
            <Form
              hasErrorSpace={false}
              options={{
                shouldUnregister: true,
                defaultValues: {
                  supplier: (() => {
                    if (purchaseOrderDetail?.supplierType === 'supplier') {
                      return purchaseOrderDetail?.supplierNameLocked
                        ? purchaseOrderDetail?.supplierName
                        : purchaseOrderDetail?.supplier?.name;
                    } else if (
                      purchaseOrderDetail?.supplierType === 'project'
                    ) {
                      return requisition?.project?.name;
                    } else if (
                      purchaseOrderDetail?.supplierType === 'company'
                    ) {
                      return requisition?.company?.name;
                    }
                    return 'N/A';
                  })(),
                  deliveryAddress: purchaseOrderDetail?.newDeliveryAddress
                    ? purchaseOrderDetail?.newDeliveryAddress
                    : purchaseOrderDetail?.requisition?.deliveryAddress ||
                      'N/A',
                  terms: purchaseOrderDetail?.terms,
                  deposit: purchaseOrderDetail?.depositPercent || 'N/A',
                  warranty: purchaseOrderDetail?.warranty?.name || '---',
                },
              }}
            >
              {({ control }) => (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid grid-cols-12 gap-3">
                      <div className="col-span-9">
                        <Input
                          label="Name"
                          registration={{ ...control.register('supplier') }}
                          control={control}
                          disabled={true}
                        />
                      </div>
                      <div className="col-span-3">
                        <Button
                          variant="outline"
                          hover="outline"
                          className="mt-6 px-2 whitespace-nowrap text-sm h-10"
                          onClick={handleSupplierDetailsClick}
                        >
                          {getModalButtonName()} Details
                        </Button>
                      </div>
                    </div>
                    <div className="relative group">
                      <div
                        className="absolute z-10 rounded-xl bg-white shadow-default p-1 px-2 border border-gray-200 text-gray-700 text-xs font-normal hidden group-hover:block"
                        id="custom_tooltip"
                      >
                        {purchaseOrderDetail?.newDeliveryAddress
                          ? purchaseOrderDetail?.newDeliveryAddress
                          : purchaseOrderDetail?.requisition?.deliveryAddress ||
                            'N/A'}
                      </div>
                      <Input
                        className="group"
                        data-tooltip="#custom_tooltip"
                        label="Delivery Address"
                        registration={{
                          ...control.register('deliveryAddress'),
                        }}
                        control={control}
                        disabled
                      />
                    </div>

                    <Input
                      label="Terms"
                      registration={{ ...control.register('terms') }}
                      control={control}
                      disabled
                    />
                    <Input
                      label="Deposit %"
                      registration={{ ...control.register('deposit') }}
                      control={control}
                      disabled
                    />
                    <Input
                      label="Warranty"
                      registration={{ ...control.register('warranty') }}
                      control={control}
                      disabled
                    />

                    <div className="flex">
                      <div className="w-64">
                        <RadioButtonGroup
                          name={`discountType`}
                          disabled
                          label="Discount (in price or percentage):"
                          options={[
                            { key: 'Fixed Amount', value: 'fixed' },
                            { key: 'Percentage', value: 'percent' },
                          ]}
                          defaultValue={
                            purchaseOrderDetail?.isAddedDiscountFixedAmount
                              ? 'fixed'
                              : 'percent'
                          }
                        />
                      </div>
                      <div className="flex-1">
                        <Input
                          type="number"
                          disabled
                          min="0"
                          step=".01"
                          value={purchaseOrderDetail?.addedDiscount}
                          label={<span>&nbsp;</span>}
                          placeholder="Input Discount"
                          beforeIcon={
                            purchaseOrderDetail?.isAddedDiscountFixedAmount
                              ? PesoIcon
                              : PercentIcon
                          }
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </Form>

            <AttachmentsAndNotes userType={userTypeForNote} />
          </div>

          {/* Right Column - Status Section */}
          {purchaseOrderDetail?.status !== 'for_po_review' && (
            <div className="w-full lg:w-[300px]">
              <StatusSection
                headerCn={'lg:border-b w-full p-1'}
                status={purchaseOrderDetail?.status}
              />

              <AssignedStaffSection
                poStatus={purchaseOrderDetail?.status}
                headerCn={'lg:border-b w-full p-1'}
                assigneeResponse={assigneeResponse}
                isAssigneeResponse={isAssigneeResponse}
              />

              <ApproversSection
                userList={userList}
                sortedApprovers={sortedApprovers}
                dateRequired={purchaseOrderDetail?.requisition?.dateRequired}
                onAddAdhoc={newApprover => handleAddAdhoc(newApprover)}
                onDeleteAdhoc={() => onDeleteAdhoc()}
                onEditAdhoc={values => onEditAdhoc(values)}
                poID={purchaseOrderDetail?.id}
                status={purchaseOrderDetail?.status}
              />
            </div>
          )}
        </div>

        {/* Modals */}
        <Modal
          size="small"
          header={
            modalData.type === 'supplier'
              ? `${getModalButtonName()} Details`
              : modalData.type === 'attachment'
                ? 'Attachments'
                : 'Notes'
          }
          onClose={closeModal}
          isOpen={modalData?.isOpen}
        >
          <AdditionalInfoModal
            data={
              modalData.type === 'attachment'
                ? orderData?.attachments
                : orderData?.notes
            }
            mode={modalData?.type}
            onClose={closeModal}
          />
        </Modal>
        <div className="mt-6">
          <h2 className="text-xl font-bold">Items</h2>

          {hasSteelbars && !onlySteelbars ? (
            <div className="my-6">
              <Tabs activeTab={activeTab} tabs={tabs} />
            </div>
          ) : null}

          <div className="mt-4">
            {isUserAnApprover() ? (
              <ApproverItemsTable
                data={items}
                onSearch={query => {
                  setSearchQuery(query);
                }}
                itemGroup={activeTab}
                ofmItems={OFMItems?.data}
                currentPage={currentPage}
                currentLimit={currentLimit}
                setPage={setPage}
                setLimit={setLimit}
                setSort={setSort}
                currentSort={currentSort}
                sortBy={sortBy}
                id={purchaseOrderDetail?.id}
                isLoading={isuseGetItemsLoading}
              />
            ) : (
              <PurchaseOrderItemsTable
                onSearch={query => {
                  setSearchQuery(query);
                }}
                activeTab={activeTab}
                isLoading={isuseGetItemsLoading}
                data={items}
                ofmItems={OFMItems?.data}
                currentPage={currentPage}
                currentLimit={currentLimit}
                setPage={setPage}
                setLimit={setLimit}
                setSort={setSort}
                currentSort={currentSort}
                sortBy={sortBy}
                type={requisition?.type}
              />
            )}
          </div>
          <hr className="my-2 col-span-2" />

          {isAssignee &&
            purchaseOrderDetail.status !== CANCELLED_PO &&
            purchaseOrderDetail.status !== FOR_DELIVERY && (
              <div className="flex flex-col sm:flex-row items-center sm:space-x-2 space-y-2 sm:space-y-0">
                <Button
                  variant="outline"
                  hover="outline"
                  className="w-fit text-[#EB5757] border-[#EB5757]"
                  onClick={() => setIsCancelReqModalOpen(true)}
                  // disabled
                >
                  Cancel Purchase Request
                </Button>
                <span className="font-bold text-sm sm:text-base">Warning:</span>
                <span className="text-sm sm:text-base">
                  This action is irreversible. All progress will be lost once
                  request is cancelled.
                </span>
              </div>
            )}
        </div>

        {purchaseOrderDetail?.supplierType === 'supplier' && (
          <SupplierDetailsModal
            isOpen={modalData.type === 'supplier' && modalData.isOpen}
            onClose={closeModal}
            supplierId={supplierId}
            supplier={modalData.supplier}
            isLoading={isLoadingSupplier}
          />
        )}

        {purchaseOrderDetail?.supplierType === 'project' && requisition && (
          <ProjectDetailsModal
            isOpen={modalData.type === 'supplier' && modalData.isOpen}
            onClose={closeModal}
            project={requisition?.project}
          />
        )}

        {purchaseOrderDetail?.supplierType === 'company' && requisition && (
          <CompanyDetailsModal
            isOpen={modalData.type === 'supplier' && modalData.isOpen}
            onClose={closeModal}
            company={requisition?.company}
          />
        )}
      </div>
      <CancelModal
        isOpen={isCancelReqModalOpen}
        onClose={() => setIsCancelReqModalOpen(false)}
        onContinue={onCloseReqModal}
        size="small"
        header="Cancel Request"
        message="You are about to cancel this request. This action is irreversible and all progress will be deleted. Press continue if you want to proceed with this action."
      />
    </>
  );
};

PurchaseOrderDetail.propTypes = {
  id: PropTypes.number.isRequired,
};
