import React, { useState, useRef, useMemo, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import PropTypes from 'prop-types';

import {
  Form,
  Input,
  Select,
  File,
  TextArea,
  FileUpload,
} from '@src/components/ui/Form';
import { Modal, ConfirmModal } from '@src/components/ui/Modal';
import { Pill } from '@components/ui/Pill';
import { Button } from '@src/components/ui/Button';
import XIcon from '@assets/icons/xicon.svg?react';
import FileIcon from '@src/assets/icons/file.svg?react';
import CommentIcon from '@src/assets/icons/comment.svg?react';
import AddIcon from '@assets/icons/add.svg?react';
import PesoIcon from '@assets/icons/peso.svg?react';
import FileClock from '@assets/icons/file-clock.svg?react';
import Download from '@assets/icons/download.svg?react';

import DeliveryReportsTable from './DeliveryReportsTable';
import { DeliveryReportsItemTable } from './DeliveryReportsItemTable';
import { AttachmentAndNotesModal } from '@features/attachments/components';
import { StatusSection } from '@src/features/dashboard/components/StatusSection';

import { useGetRequisitionSlip } from '@features/dashboard/api';
import { useDownloadTemplatePdf } from '@src/hooks/useDownloadTemplatePdf';
import { useGetDeliveryReceipt } from '@src/features/delivery-receipt/api';
import {
  useGetPurchaseOrdersForInvoice,
  useGetDeliveryReceipts,
  useCreateInvoice,
  useGetInvoice,
  useGetInvoiceDR,
  useUpdateInvoice,
  useGetDeliveryReceiptItems,
} from '../api';
import {
  useUploadAttachments,
  useGetAttachments,
  useAssignAttachments,
  useDeleteAttachment,
} from '@features/attachments/api';
import { useGetNotes, useCreateNotes } from '@features/notes/api';
import { useGetPurchaseOrder } from '@src/features/purchase-order/api';
import { useUserStore } from '@store';

import { invoiceSchema } from '@src/schema/invoice.schema';
import { MODELS } from '@config/modelsConfig';
import { NOTES_TYPE } from '@config/notesConfig';

import {
  addDaysToDate,
  setDefaultDateValue,
  formatDateToDMY,
} from '@utils/dateFormat';

import { useNotification } from '@hooks/useNotification';

const MODAL_TYPES = {
  ADD_DELIVERY_REPORT: 'ADD_DELIVERY_REPORT',
  VIEW_DR_ITEMS: 'VIEW_DR_ITEMS',
  CONFIRM_CHANGES: 'CONFIRM_CHANGES',
  SUBMIT: 'SUBMIT',
  CANCEL: 'CANCEL',
  DRAFT: 'DRAFT',
  NONE: 'NONE',
  NOTES: 'notes',
  ATTACHMENT: 'attachments',
};

const AccordionItem = ({ item, selectedItems, handleSelectItems }) => {
  return (
    <div className="flex flex-col cursor-pointer ">
      <div key={item?.value} className="flex flex-row space-x-2 ml-3 pb-1 pt-1">
        <input
          type="checkbox"
          className="cursor-pointer"
          value={item?.value}
          checked={selectedItems.includes(item?.value)}
          onChange={handleSelectItems}
        />
        <span className="text-sm">{item?.key}</span>
      </div>
    </div>
  );
};

export const InvoiceCreate = ({
  currentPage,
  currentLimit,
  setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [hasAttachments, setHasAttachments] = useState(false);
  const [attachmentsArr, setAttachments] = useState([]);
  const [note, setNote] = useState('');

  const [filteredItems, setFilteredItems] = useState([]);
  const [itemsList, setItemsList] = useState([]); // for selected item/s container

  const [defaultSelectedItems, setDefaultSelectedItems] = useState([]);
  const [defaultItemsList, setDefaultItemsList] = useState([]); // for selected item/s container

  const [selectedItems, setSelectedItems] = useState([]);

  const [tableList, setTableList] = useState([]);
  const [deliveryItemsList, setDeliveryItemsList] = useState([]);
  const [selectedDR, setSelectedDR] = useState(null);

  const [deliveryReportList, setDeliveryReportList] = useState([]);
  const [deliveryReportItemsList, setDeliveryReportItemsList] = useState([]);
  const [selectedPo, setSelectedPo] = useState(null);
  const [deliveryReceiptId, setDeliveryReceiptId] = useState(null);
  const [purchaseOrderOptions, setPurchaseOrderOptions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchFilename, setSearchFilename] = useState('');
  const [modalData, setModalData] = useState({
    isOpen: false,
    type: '',
    attachments: [],
    isUploading: false,
  });

  const { user } = useUserStore();

  const mainFormRef = useRef(null);
  const poRef = useRef(null);
  const fileRef = useRef(null);
  const searchRef = useRef(null);
  const noteRef = useRef(null);

  const { id: rsID } = useParams();
  const { invoiceID } = useParams();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  const { data: requisition } = useGetRequisitionSlip(rsID, {
    enabled: !!rsID,
  });
  const { data: poList, refetch: refetchPurchaseOrderforInvoice } =
    useGetPurchaseOrdersForInvoice(requisition?.id, {
      enabled: !!requisition?.id,
    });
  const { data: deliveryReceipts, refetch: refetchGetDeliveryReceipts } =
    useGetDeliveryReceipts(selectedPo, {
      enabled: !!selectedPo,
    });
  const { mutate: downloadPdf, isPending: isPdfDownloading } =
    useDownloadTemplatePdf();
  const { data: invoiceData } = useGetInvoice(invoiceID, {
    enabled: !!invoiceID,
  });
  const { data: invoiceDRData } = useGetInvoiceDR(invoiceID, {
    enabled: !!invoiceID,
  });
  const {
    data: invoiceDRItemsData,
    refetch: refetchInvoiceDRItemsData,
    isLoading: isLoadingDRItems,
  } = useGetDeliveryReceiptItems(selectedDR, {
    enabled: !!selectedDR,
  });
  const { data: purchaseOrder } = useGetPurchaseOrder(
    invoiceData?.purchaseOrderId,
    {
      enabled: !!invoiceData?.purchaseOrderId,
    },
  );
  const { data: deliveryReceipt, refetch: refetchDR } = useGetDeliveryReceipt(
    { id: deliveryReceiptId, type: requisition?.type },
    { enabled: !!deliveryReceiptId },
  );
  const { data: fileAttachments, refetch: refetchAttacments } =
    useGetAttachments(
      {
        modelId: invoiceID,
        model: MODELS.INVOICE,
        search: searchFilename,
      },
      {
        enabled: !!invoiceID,
      },
    );
  const { data: notesData } = useGetNotes(
    {
      model: MODELS.INVOICE,
      modelId: invoiceID,
      dateFrom: '',
      dateTo: '',
    },
    {
      enabled: !!invoiceID,
    },
  );

  // Mutations
  const { mutateAsync: createInvoice, isPending: isCreatingDeliveryReceipt } =
    useCreateInvoice();
  const { mutateAsync: updateInvoice } = useUpdateInvoice();
  const { mutateAsync: uploadAttachments, isLoading: isFileUploading } =
    useUploadAttachments();
  const { mutateAsync: deleteAttachment } = useDeleteAttachment();
  const { mutateAsync: createNotes, isPending: isSubmittingNotes } =
    useCreateNotes();
  const { mutateAsync: assignAttachments, isPending: isSubmittingAttachments } =
    useAssignAttachments();

  useEffect(() => {
    if (invoiceData) {
      setSelectedPo(invoiceData?.purchaseOrderId);
    }
  }, [invoiceData]);

  useEffect(() => {
    if (invoiceDRData) {
      const arr = invoiceDRData?.data.map(item => {
        return {
          id: item?.id,
          drNumber: item?.drNumber,
          latestDeliveryStatus: 'Delivered',
          latestDeliveryDate: formatDateToDMY(item?.latestDeliveryDate),
        };
      });
      setTableList(arr);
      setDefaultItemsList(arr);

      const ids = invoiceDRData?.data.map(item => {
        return item?.id;
      });
      setSelectedItems(ids);
      setDefaultSelectedItems(ids);

      const options = invoiceDRData?.data.map(dr => {
        return {
          key: dr?.drNumber,
          value: dr?.id,
        };
      });

      setItemsList(options);
    }
  }, [invoiceDRData]);

  useEffect(() => {
    if (invoiceDRItemsData) {
      setDeliveryItemsList(invoiceDRItemsData?.data);
    }
  }, [invoiceDRItemsData]);

  useEffect(() => {
    if (requisition) refetchPurchaseOrderforInvoice();
  }, [requisition]);

  useEffect(() => {
    if (poList?.data?.length > 0) {
      setPurchaseOrderOptions([]);
      const options = poList?.data.map(po => {
        const poValue = po?.poNumber;
        return {
          key: poValue,
          value: po?.id,
        };
      });

      setPurchaseOrderOptions(options);
    }
  }, [poList]);

  useEffect(() => {
    if (purchaseOrder) {
      setPurchaseOrderOptions([
        {
          key: purchaseOrder?.poNumber,
          value: purchaseOrder?.id,
        },
      ]);
    }
  }, [purchaseOrder]);

  useEffect(() => {
    if (fileAttachments) {
      setAttachments(fileAttachments?.data);
    }
  }, [fileAttachments]);

  useEffect(() => {
    if (deliveryReceipt) {
      const foundItem = filteredItems.find(
        dr => dr.drNumber === deliveryReceipt?.drNumber,
      );

      if (foundItem) {
        setDeliveryReportItemsList(prevItems =>
          prevItems.filter(item => item.drNumber !== deliveryReceipt?.drNumber),
        );
        setDeliveryReportList(prevItems =>
          prevItems.filter(item => item.drNumber !== deliveryReceipt?.drNumber),
        );
      } else {
        let latestDeliveryDate = new Date(
          Math.max.apply(
            null,
            deliveryReceipt?.items.map(function (e) {
              return new Date(setDefaultDateValue(e.dateDelivered));
            }),
          ),
        );

        const formattedDeliveryReport = deliveryReceipt?.items.map(item => {
          return {
            id: item?.id,
            drNumber: deliveryReceipt?.drNumber,
            itemDes: item?.itemDes,
            qtyOrdered: item?.qtyOrdered,
            qtyDelivered: item?.qtyDelivered,
            qtyReturned: item?.qtyReturned,
            unit: item?.unit,
            latestDeliveryDate: item?.dateDelivered,
          };
        });

        setDeliveryReportItemsList(formattedDeliveryReport);
        const arr = deliveryReportList;

        const foundItem = arr.find(
          dr => dr.drNumber === deliveryReceipt?.drNumber,
        );

        if (!foundItem) {
          arr.push({
            id: deliveryReceipt?.id,
            drNumber: deliveryReceipt?.drNumber,
            latestDeliveryStatus: 'Delivered',
            latestDeliveryDate: formatDateToDMY(latestDeliveryDate),
          });
          setDeliveryReportList(arr);
        }
      }
    }
  }, [deliveryReceipt]);

  useEffect(() => {
    if (deliveryReceipts) {
      const options = deliveryReceipts?.data.map(dr => {
        return {
          key: dr?.drNumber,
          value: dr?.id,
        };
      });
      setFilteredItems(options);
    }
  }, [deliveryReceipts]);

  const handleSubmit = async values => {
    let attachmentIds = [];

    // Check if invoice has at least 1 DR
    if (selectedItems.length) {
      try {
        let fileArr = [];
        values?.attachments?.forEach(attachment => {
          fileArr.push(attachment.file);
        });

        const formData = {
          requisitionId: requisition?.id.toString(),
          purchaseOrderId: selectedPo.toString(),
          isDraft: modalData.type === MODAL_TYPES.DRAFT ? 'true' : 'false',
          supplierInvoice: {
            number: values?.number,
            invoiceDate: values?.invoiceDate,
            amount: parseFloat(values?.amount).toFixed(2).toString(),
          },
          deliveryReceiptIds: selectedItems,
          note: values?.comment || '',
        };

        if (fileArr.length) {
          const uploadedAttachments = await uploadAttachments({
            model: 'invoice',
            attachments: fileArr,
          });

          attachmentIds = uploadedAttachments.map(attachment => attachment.id);
          formData['attachmentIds'] = attachmentIds;
        } else {
          attachmentIds = fileAttachments?.data.map(
            attachment => attachment.id,
          );
          formData['attachmentIds'] = attachmentIds;
        }

        if (invoiceID) {
          await updateInvoice(
            { id: invoiceID, data: formData },
            {
              onSuccess: result => {
                //closeModal();
                showNotification({
                  type: 'success',
                  message:
                    modalData.type === MODAL_TYPES.SUBMIT
                      ? 'Invoice submitted successfully'
                      : 'Draft created successfully',
                });

                modalData.type === MODAL_TYPES.DRAFT
                  ? navigate(
                      `/app/requisition-slip/${rsID}/invoice/create/${result?.id}`,
                    )
                  : navigate(
                      `/app/requisition-slip/${rsID}/invoice/${result?.id}`,
                    );

                closeModal();
              },
            },
          );
        } else {
          await createInvoice(formData, {
            onSuccess: result => {
              showNotification({
                type: 'success',
                message:
                  modalData.type === MODAL_TYPES.SUBMIT
                    ? 'Invoice submitted successfully'
                    : 'Draft created successfully',
              });

              modalData.type === MODAL_TYPES.DRAFT
                ? navigate(
                    `/app/requisition-slip/${rsID}/invoice/create/${result?.id}`,
                  )
                : navigate(
                    `/app/requisition-slip/${rsID}/invoice/${result?.id}`,
                  );

              closeModal();
            },
          });
        }
      } catch (error) {
        closeModal();
        const errorMessage = error?.response?.data?.message || error?.message;
        showNotification({
          type: 'error',
          message: errorMessage,
        });
      }
    } else {
      showNotification({
        type: 'error',
        message:
          // error?.response?.data?.message ||
          'A Receiving Report is required to be able to submit an invoice',
      });
    }
  };

  const handleSearch = () => {
    if (searchTerm === '' || searchTerm.trim() === '') {
    }
    setSearchTerm(searchRef.current.value);
  };

  const handleClear = () => {
    searchRef.current.value = '';
    setSearchTerm('');
    return true;
  };

  const onSelectedPO = value => {
    setSelectedPo(value);
  };

  const onCheckAttachment = () => {
    setModalData(prevData => ({
      type: MODAL_TYPES.ATTACHMENT,
      isOpen: !prevData?.isOpen,
      data: fileAttachments,
    }));
  };

  const handleDownload = () => {
    downloadPdf({
      type: 'invoice-report',
      id: invoiceID,
    });
  };

  const canViewDownload = () => {
    const allowableRoles = [
      'Purchasing Staff',
      'Purchasing Head',
      'Purchasing Admin',
    ];

    const found = allowableRoles.find(role => role === user.role?.name);
    return (found ||  requisition?.createdByUser?.id === user.id) ? true : false;
  };

  const onCheckNote = () => {
    setModalData(prevData => ({
      type: MODAL_TYPES.NOTES,
      isOpen: !prevData?.isOpen,
      data: notesData,
    }));
  };

  const onSubmitNote = async () => {
    try {
      const noteReference = noteRef.current;

      const payload = {
        model: MODELS.INVOICE,
        modelId: invoiceID,
        note,
        commentType: NOTES_TYPE.NOTE,
      };

      await createNotes(payload, {
        onSuccess: () => {
          showNotification({
            type: 'success',
            message: 'Note added successfully',
          });
          noteReference.clear();
          setNote('');
          closeModal();
        },
      });
    } catch (error) {
      let errorMessage = error?.response?.data?.message || 'Emojis are not allowed';

      showNotification({
        type: 'error',
        message: errorMessage,
      });
      throw error;
    }
  };

  const removeAttachment = async id => {
    try {
      await deleteAttachment(
        { id },
        {
          onSuccess: async () => {
            showNotification({
              type: 'success',
              message: 'Attachment Removed successfully',
            });
            setAttachments(prevAttachments =>
              prevAttachments.filter(attachment => attachment.id !== id),
            );
            //await refetchAttachments();
          },
          onError: error => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message ||
                'Failed to remove attachments',
            });
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  const submitAttachments = async () => {
    const file = fileRef.current;
    const attachmentIds = file?.attachmentIds;

    await assignAttachments(
      { attachmentIds, model: MODELS.INVOICE, modelId: parseInt(invoiceID) },
      {
        onSuccess: message => {
          showNotification({
            type: 'success',
            message: message?.message || 'Attachments uploaded successfully',
          });
          file.removeAllFiles();
          setHasAttachments(false);
          closeModal();
        },
        onError: error => {
          const errorMessage = error?.response?.data?.message || error?.message;
          showNotification({
            type: 'error',
            message: errorMessage,
          });
        },
      },
    );
  };

  const handleSelectItems = e => {
    const value = Number(e.target.value);

    if (e.target.checked) {
      setSelectedItems(prev => [...prev, value]);
      setDeliveryReceiptId(value);
      const foundItem = filteredItems.find(item => item.value === value);

      if (foundItem) {
        setItemsList(prev => [...prev, foundItem]);
        if (deliveryReceiptId) {
          refetchDR();
        }
      }
    } else {
      setSelectedItems(prev => prev.filter(id => id !== value));
      setItemsList(prev => prev.filter(item => item.value !== value));
    }
  };

  const closeModal = () => {
    setModalData(prevData => ({
      ...prevData,
      isOpen: !prevData?.isOpen,
      type: MODAL_TYPES.NONE,
    }));
  };

  const showMessageOnError = error => {
    if (error) {
      return <div className="text-red-600/100 text-sm">{error}</div>;
    }
  };

  return (
    <>
      {/** Floating Banner */}
      {invoiceData && !invoiceData?.isDraft && (
        <div className="sticky top-28 z-[3] flex items-center justify-between rounded-lg border border-[#F2994A] bg-[#FFFAF5] p-4 shadow-sm">
          <div className="w-full flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center">
              <FileClock className="text-[#754445]" />
            </div>
            <div>
              <p className="text-sm font-bold">You have a pending action:</p>
              <p className="text-sm font-semibold text-gray-900">
                Create Payment Request
              </p>
            </div>
            <div className="flex items-center gap-2 ml-auto">
              <Button
                className="min-w-24 bg-green-600 text-white hover:bg-green-700"
                onClick={() => {
                  navigate(`/app/requisition-slip/${requisition?.id}/payment-request/create`);
                }}
              >
                Create Payment Request
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="p-6 flex flex-col gap-4">
        <div className="flex flex-col md:flex-row md:justify-end md:items-center gap-4 mb-4 md:absolute md:top-8 md:right-8 text-sm">
          <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
            {canViewDownload() && invoiceID && !invoiceData?.isDraft && (
              <Button
                variant="action"
                hover="action"
                className="color:bg-[#F0F1F1] text-[#420001] max-w-40"
                icon={Download}
                iconPosition="L"
                isLoading={isPdfDownloading}
                onClick={() => handleDownload()}
                disabled={isPdfDownloading}
              >
                Download
              </Button>
            )}

            {(!invoiceData || invoiceData?.isDraft) && (
              <Button
                className="w-full xl:w-fit mt-2 xl:mt-0"
                variant="outline"
                hover="outline"
                onClick={() =>
                  setModalData({
                    isOpen: true,
                    type: MODAL_TYPES.DRAFT,
                  })
                }
              >
                Save Draft
              </Button>
            )}
          </div>
        </div>

        {/* Page Header */}
        <div className="flex justify-between items-center border-t pt-5 border-b pb-5 mb-6">
          <div>
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold">Invoice Report Number:</span>
              <span className="text-lg font-semibold">
                {!invoiceData
                  ? '---'
                  : invoiceData?.isDraft
                    ? invoiceData?.irDraftNumber
                    : !invoiceData?.isDraft
                      ? invoiceData?.irNumber
                      : ''}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-bold">R.S. Number:</span>
              <span className="font-semibold">
                {requisition?.rsNumber ? requisition?.rsNumber : '--'}
              </span>
            </div>
          </div>
        </div>

        {/* Form */}
        <div
          className={
            invoiceData && 'flex flex-col lg:flex-row gap-6 lg:gap-8 mb-10'
          }
        >
          <div className="flex-1 bg-white p-6 rounded-lg shadow-sm">
            <Form
              ref={mainFormRef}
              onSubmit={handleSubmit}
              hasErrorSpace={false}
              schema={invoiceSchema}
              options={{
                values: {
                  purchaseOrderId: selectedPo,
                  number: invoiceData?.supplierInvoiceNo,
                  invoiceDate: setDefaultDateValue(
                    invoiceData?.issuedInvoiceDate,
                  ),
                  amount: invoiceData?.invoiceAmount,
                  existingAttachments: fileAttachments?.data,
                },
              }}
            >
              {({ control, register, watch, setValue, formState, reset }) => {
                const previousPO = useRef(null);

                useEffect(() => {
                  if (selectedPo && previousPO.current) {
                    if (previousPO.current !== selectedPo) {
                      setAttachments([]);
                      setSelectedDR();
                      setTableList([]);
                      setItemsList([]);
                      setSelectedItems([]);
                      previousPO.current = selectedPo;
                    }
                  } else {
                    previousPO.current = selectedPo;
                  }
                }, [selectedPo]);

                return (
                  <>
                    <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 mb-">
                      <div className="flex-1 bg-white p-6 rounded-lg shadow-sm  mb-5 ">
                        <div className="grid grid-cols-2 pb-3 gap-4">
                          <div className={invoiceData ? 'hidden' : ''}>
                            <Select
                              searchable
                              label="Purchase Order No."
                              control={control}
                              ref={poRef}
                              error={formState.errors['purchaseOrderId']}
                              handleChange={val => {
                                reset();
                                fileRef.current.removeAllFiles();
                                onSelectedPO(val);
                              }}
                              {...register('purchaseOrderId')}
                              options={purchaseOrderOptions}
                            />

                            {showMessageOnError(
                              formState.errors['purchaseOrderId']?.message,
                            )}
                          </div>
                          <div className={invoiceData ? '' : 'hidden'}>
                            <Input
                              disabled
                              label="Purchase Order No."
                              value={purchaseOrder?.poNumber}
                            />
                          </div>
                        </div>

                        <hr className="my-4" />

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            {' '}
                            <Input
                              label="Supplier Invoice No."
                              maxLength="100"
                              disabled={invoiceData && !invoiceData?.isDraft}
                              error={formState.errors['number']}
                              registration={{
                                ...control.register('number'),
                              }}
                              control={control}
                            />
                            {showMessageOnError(
                              formState.errors['number']?.message,
                            )}
                          </div>
                          <div>
                            <Input
                              label="Supplier Invoice Issued Date"
                              type="date"
                              error={formState.errors['invoiceDate']}
                              disabled={invoiceData && !invoiceData?.isDraft}
                              registration={{
                                ...control.register('invoiceDate'),
                              }}
                              control={control}
                              maxDate={addDaysToDate(0, new Date())}
                            />
                            {showMessageOnError(
                              formState.errors['invoiceDate']?.message,
                            )}
                          </div>
                          <div>
                            <Input
                              label="Supplier Invoice Amount"
                              registration={{
                                ...control.register('amount', {
                                  valueAsNumber: true,
                                }),
                              }}
                              error={formState.errors['amount']}
                              disabled={invoiceData && !invoiceData?.isDraft}
                              step="any"
                              beforeIcon={PesoIcon}
                              type="number"
                              control={control}
                            />
                            {showMessageOnError(
                              formState.errors['amount']?.message,
                            )}
                          </div>
                        </div>

                        {invoiceID && !invoiceData?.isDraft && (
                          <>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Button
                                  onClick={onCheckAttachment}
                                  icon={FileIcon}
                                  iconSize="h-5 w-auto"
                                  type="button"
                                  variant="outline"
                                  hover="outline"
                                  label={
                                    <div className="flex justify-between align-middle">
                                      <p className="pb-2">Attachments</p>
                                      {fileAttachments?.hasNewNotifications && (
                                        <Pill className="text-[#EB5757] bg-[#FCE9D8] align-middle">
                                          New Attachment/s
                                        </Pill>
                                      )}
                                    </div>
                                  }
                                >
                                  Check Attachments
                                </Button>
                              </div>

                              <div className="flex flex-col ">
                                <Button
                                  onClick={onCheckNote}
                                  icon={CommentIcon}
                                  type="button"
                                  iconSize="h-5 w-auto"
                                  variant="outline"
                                  hover="outline"
                                  label={
                                    <div className="flex justify-between align-middle">
                                      <p className="pb-2">Notes</p>
                                      {notesData?.hasNewNotifications && (
                                        <Pill className="text-[#EB5757] bg-[#FCE9D8]">
                                          New Comment/s
                                        </Pill>
                                      )}
                                    </div>
                                  }
                                >
                                  Check Notes
                                </Button>
                              </div>

                              <div className="flex flex-col">
                                <FileUpload
                                  isUploading={isUploading}
                                  ref={fileRef}
                                  modelType={MODELS.INVOICE}
                                  control={control}
                                  name={'fileAttachments'}
                                  attachments={attachmentsArr}
                                  onAttachmentChange={val =>
                                    setHasAttachments(!!val.length)
                                  }
                                  fileTypeRestrictions=".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.csv,.docx"
                                />
                                <div className="flex justify-between items-center mt-1">
                                  <div className="text-[#4F4F4F] text-[12px]"></div>
                                  <Button
                                    variant="outline"
                                    type="button"
                                    onClick={submitAttachments}
                                    disabled={
                                      !hasAttachments === true ? true : null
                                    }
                                    hover="outline"
                                    className="w-32 h-8 text-sm text-[#420001] border-[#420001] ml-4"
                                  >
                                    Submit
                                  </Button>
                                </div>
                              </div>
                              <div className="col-span-1">
                                <div className="flex flex-col">
                                  <TextArea
                                    label="Add Notes"
                                    name={'notes'}
                                    registration={{
                                      ...control.register('notes'),
                                    }}
                                    control={control}
                                    value={note}
                                    placeholder="Input notes"
                                    onInput={e => setNote(e.target.value)}
                                    maxCharacters={100}
                                    ref={noteRef}
                                    className="mb-0"
                                  />
                                  <div className="flex justify-end items-center">
                                    <div className="ml-4">
                                      <Button
                                        variant="outline"
                                        disabled={note !== '' ? null : true}
                                        hover="outline"
                                        type="button"
                                        onClick={onSubmitNote}
                                        className="!w-32 h-8 text-sm"
                                      >
                                        Submit
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </>
                        )}

                        {(!invoiceID || invoiceData?.isDraft) && (
                          <div className="grid grid-cols-2 gap-4 pt-4">
                            <div className="flex flex-col">
                              <File
                                isUploading={isUploading}
                                ref={fileRef}
                                error={formState.errors['attachments']}
                                control={control}
                                name="attachments"
                                attachments={{}}
                                hasError={
                                  formState.errors['attachments'] ? true : false
                                }
                                hasAttachments={setHasAttachments}
                              />

                              {isUploading && (
                                <div className="flex items-center">
                                  <div className="text-sm mr-2 font-semibold">
                                    {uploadProgress}%
                                  </div>
                                  <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
                                    <div
                                      className="bg-blue-500 h-full transition-all duration-200 ease-out"
                                      style={{ width: `${uploadProgress}%` }}
                                    />
                                  </div>
                                </div>
                              )}
                              <div className="flex flex-col gap-2 bg-gray-50 mb-2 rounded-lg shadow-sm max-h-36 overflow-y-auto">
                                {attachmentsArr &&
                                  attachmentsArr?.map(
                                    ({ id, modelId, fileName }, index) => (
                                      <div
                                        key={modelId}
                                        className="flex w-full"
                                      >
                                        <div className="w-full">
                                          <Input
                                            id={id}
                                            name={`${fileName}.${index}`}
                                            type="text"
                                            value={fileName}
                                            readOnly
                                            disabled
                                            className="w-full relative disabled:bg-gray-50 disabled:opacity-80 disabled:shadow-none text-black font-normal border-0"
                                            hasIcon={true}
                                            renderIcon={XIcon}
                                            iconHandler={() => {
                                              removeAttachment(id);
                                            }}
                                          />
                                        </div>
                                      </div>
                                    ),
                                  )}
                                <Input
                                  className="hidden"
                                  control={control}
                                  name="existingAttachments"
                                  label=""
                                  type="text"
                                  disabled={invoiceID}
                                  {...register('existingAttachments')}
                                />
                              </div>
                              <div className="flex justify-between align-middle">
                                <span className="text-[#4F4F4F] text-[12px]">
                                  The maximum size for each file is 25 MB. File
                                  formats - PNG, JPG, JPEG, PDF, Excel, CSV.
                                </span>
                              </div>

                              {showMessageOnError(
                                formState.errors['attachments']?.message,
                              )}
                            </div>
                            <div className="flex flex-col flex-grow">
                              <TextArea
                                label="Notes"
                                name="comment"
                                registration={{
                                  ...control.register('comment'),
                                }}
                                control={control}
                                placeholder="Input notes here"
                              />
                              {showMessageOnError(
                                formState.errors['comment']?.message,
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                );
              }}
            </Form>
          </div>

          {/* Right Panel */}
          {invoiceData && (
            <div className="w-full lg:w-[300px]">
              <StatusSection
                headerCn={'lg:border-b w-full p-1'}
                status={invoiceData?.isDraft ? 'IR Draft' : 'Invoice Received'}
              />
              <div className="flex flex-col sm:flex-row lg:flex-col justify-between items-start sm:items-center lg:items-start gap-2 max-w-full mx-auto bg-[#FFFFFF] p-6 rounded-lg shadow-sm text-sm mb-5">
                <h3 className="font-bold text-[#4F575E]">Assigned to</h3>
                <hr className="w-full border-t border-gray-300" />
                <p>
                  {invoiceData?.createdByUser?.firstName +
                    ' ' +
                    invoiceData?.createdByUser?.lastName}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Delivery Reports Table */}
        <div className="flex">
          <div className="w-64 flex-none">
            <h2 className="text-xl font-bold">Receiving Reports</h2>
          </div>
          <div className="size-14 grow"></div>
          <div className="w-32 flex-none">
            {' '}
            <div className="flex justify-end">
              {(!invoiceData || invoiceData?.isDraft) && (
                <Button
                  iconPosition="L"
                  icon={AddIcon}
                  disabled={selectedPo ? null : true}
                  className="w-full xl:w-fit py-3 px-5 mt-2 xl:mt-0 float-right mb-5"
                  onClick={() => {
                    refetchGetDeliveryReceipts();
                    setModalData({
                      isOpen: true,
                      type: MODAL_TYPES.ADD_DELIVERY_REPORT,
                    });
                  }}
                >
                  Add RR
                </Button>
              )}
            </div>
          </div>
        </div>
        <DeliveryReportsTable
          setSort={setSort}
          currentSort={currentSort}
          currentPage={currentPage}
          setPage={setPage}
          hasSelectedPO={selectedPo ? null : true}
          onAddData={() => {
            setModalData({
              isOpen: true,
              type: MODAL_TYPES.ADD_DELIVERY_REPORT,
            });
          }}
          tableData={tableList}
          isViewMode={
            !invoiceData ? false : invoiceData?.isDraft ? false : true
          }
          onSearch={() => {}}
          setLimit={setLimit}
          limit={currentLimit}
          handleItemClick={item => {
            refetchGetDeliveryReceipts();
            if (invoiceData) {
              setSelectedDR(item?.id);
            } else {
              const filteredItems = deliveryReportItemsList.filter(
                itm => itm?.drNumber === item?.drNumber,
              );
              setDeliveryItemsList(filteredItems);
              setSelectedDR(item?.id);
            }

            setModalData({
              isOpen: true,
              type: MODAL_TYPES.VIEW_DR_ITEMS,
              data: item,
            });
          }}
          onRemove={id => {
            setItemsList(itemsList.filter(itm => itm?.value !== id));
            setSelectedItems(selectedItems.filter(itm => itm !== id));
            setTableList(tableList.filter(itm => itm?.id !== id));
          }}
        />

        {/* Invoice Action Buttons */}
        {(!invoiceData || invoiceData?.isDraft) && (
          <div className="flex justify-between items-center mt-10">
            <div className="flex gap-4 items-center text-sm">
              <Button
                className="w-full xl:w-fit mt-2 xl:mt-0"
                variant="outline"
                type="button"
                hover="outline"
                onClick={() =>
                  setModalData({
                    isOpen: true,
                    type: MODAL_TYPES.DRAFT,
                  })
                }
              >
                Save Draft
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                className="w-full xl:w-fit mt-2 xl:mt-0"
                variant="outline"
                type="button"
                hover="outline"
                onClick={() => {
                  setModalData({
                    isOpen: true,
                    type: MODAL_TYPES.CANCEL,
                  });
                }}
              >
                Cancel
              </Button>
              <Button
                className="w-full xl:w-fit mt-2 xl:mt-0"
                variant="submit"
                type="button"
                size="lg"
                onClick={() => {
                  setModalData({
                    isOpen: true,
                    type: MODAL_TYPES.SUBMIT,
                  });
                }}
              >
                Submit
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Delivery Reports Modal */}
      <Modal
        size="medium"
        header="Select Receiving Report"
        onClose={() => {
          const drArr = [...defaultSelectedItems];
          setSelectedItems(drArr);

          const selectedArr = [...defaultItemsList].filter(item =>
            drArr.includes(item?.value),
          );
          setItemsList(selectedArr);

          searchRef.current.value = '';
          setSearchTerm('');
          closeModal();
        }}
        isOpen={
          modalData.isOpen && modalData.type === MODAL_TYPES.ADD_DELIVERY_REPORT
        }
      >
        <span className="my-4 text-sm">
          Please select the Item/s from the list below. Make sure all items
          selected are correct. Press Add to continue.
        </span>

        {/* Search */}
        <div className="flex items-center w-full gap-2 mb-4">
          <div className="flex-1">
            <Input
              ref={searchRef}
              type="text"
              isSearch={true}
              placeholder="Search Receiving Report"
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
          </div>
          <Button
            onClick={handleSearch}
            className="w-24"
            hover="action"
            variant="action"
          >
            Search
          </Button>
          <Button
            onClick={handleClear}
            className="w-24"
            hover="outline"
            variant="outline"
            disabled={!searchTerm}
          >
            Clear
          </Button>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {/* Items List */}

          <div className="flex-1">
            <h3 className="font-semibold mb-2">Receiving Report No</h3>
            <div className="flex-1 border rounded-lg p-3 overflow-y-auto min-h-40 max-h-40">
              {filteredItems
                .filter(dr => {
                  return dr?.key
                    .toUpperCase()
                    .includes(searchTerm.toUpperCase());
                })
                .map(item => (
                  <AccordionItem
                    key={item.id + JSON.stringify(item)}
                    item={item}
                    selectedItems={selectedItems}
                    handleSelectItems={handleSelectItems}
                  />
                ))}
            </div>
          </div>

          {/* Selected Items List */}
          <div className="flex-1">
            <h3 className="font-semibold mb-2">Selected Item/s</h3>
            <div className="border rounded-lg p-3 overflow-y-auto min-h-40 max-h-40 text-sm">
              {!itemsList?.length ? (
                <p className="text-sm">Empty Item List</p>
              ) : (
                itemsList.map(item => (
                  <div key={item?.key} className="flex justify-between py-0.5">
                    <span className="p-0.5 text-sm">{item?.key}</span>
                    <Button
                      onClick={() => {
                        setItemsList(
                          itemsList.filter(itm => itm?.value !== item?.value),
                        );
                        setSelectedItems(
                          selectedItems.filter(itm => itm !== item?.value),
                        );
                        //setDeliveryReceiptId(null);
                      }}
                      className="w-fit px-1 py-1"
                      icon={XIcon}
                      iconSize="w-2.5 h-2.5 text-gray-600"
                      variant="noColor"
                      hover="darkGlow"
                    />
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            hover="outline"
            onClick={() => {
              const drArr = [...defaultSelectedItems];
              setSelectedItems(drArr);
              const selectedArr = [...defaultItemsList].filter(item =>
                drArr.includes(item?.value),
              );
              // setFilteredItems(selectedArr);
              setItemsList(selectedArr);

              searchRef.current.value = '';
              setSearchTerm('');
              closeModal();
            }}
          >
            Cancel
          </Button>
          <Button
            variant="submit"
            hover="outline"
            onClick={() => {
              closeModal();

              if (selectedItems.length) {
                const deliveryArr = [...new Set(deliveryReportList)];
                const test = deliveryArr.filter(delivery =>
                  selectedItems.includes(delivery.id),
                );
                const newArrayTableList = [
                  ...tableList,
                  ...deliveryArr.filter(delivery =>
                    selectedItems.includes(delivery.id),
                  ),
                ];
                setTableList(newArrayTableList);
                const arr = [...filteredItems];
                setDefaultItemsList(arr);

                const temparr = [...selectedItems];
                setDefaultSelectedItems(temparr);
              } else {
                setTableList([]);
                setDefaultSelectedItems([]);
                setDefaultItemsList([]);
              }
            }}
          >
            Add
          </Button>
        </div>
      </Modal>

      <Modal
        size="medium"
        header={modalData?.data?.drNumber}
        headerClassName={'text-left'}
        onClose={() => {
          closeModal();
        }}
        isOpen={
          modalData.isOpen && modalData.type === MODAL_TYPES.VIEW_DR_ITEMS
        }
      >
        <span className="text-xl font-bold">Item/s</span>
        <div>
          <DeliveryReportsItemTable
            tableData={deliveryItemsList}
            isLoading={isLoadingDRItems}
          />
        </div>
        <div className="flex justify-center gap-2">
          <Button variant="outline" hover="outline" onClick={closeModal}>
            Close
          </Button>
        </div>
      </Modal>

      <Modal
        size="small"
        header={
          modalData?.type === MODAL_TYPES.SUBMIT
            ? 'Submit Invoice Report'
            : 'Save Draft'
        }
        onClose={closeModal}
        isOpen={
          modalData?.isOpen &&
          (modalData?.type === MODAL_TYPES.SUBMIT ||
            modalData?.type === MODAL_TYPES.DRAFT)
        }
      >
        <span className="my-4 text-sm">
          {modalData?.type === MODAL_TYPES.SUBMIT
            ? 'You are about to submit this record. Make sure all items are correct. Press submit if you want to proceed with this action.'
            : 'You are about to save this request as a draft. Make sure all items are correct. Press save if you want to proceed with this action.'}
        </span>
        <div className="flex justify-center gap-2">
          <Button variant="outline" hover="outline" onClick={closeModal}>
            Cancel
          </Button>
          <Button
            variant="submit"
            type="submit"
            onClick={() => mainFormRef.current.requestSubmit()}
          >
            {modalData?.type === MODAL_TYPES.SUBMIT ? 'Submit' : 'Draft'}
          </Button>
        </div>
      </Modal>

      <ConfirmModal
        isOpen={
          modalData?.isOpen && modalData?.type === MODAL_TYPES.CONFIRM_CHANGES
        }
        header="Change Purchase Order"
        onClose={closeModal}
        onConfirm={() => handleContinueSelection()}
        message="Changing the purchase order will remove the filled information below. Press continue if you want to proceed with this action."
      />

      <Modal
        size="small"
        header="Cancel Invoice Report"
        onClose={closeModal}
        isOpen={modalData?.isOpen && modalData?.type === MODAL_TYPES.CANCEL}
      >
        <span className="my-4 text-sm">
          You are about to cancel. All changes will not be saved. Press continue
          if you want to proceed with this action.
        </span>
        <div className="flex justify-center gap-2">
          <Button variant="submit" hover="outline" onClick={closeModal}>
            Cancel
          </Button>
          <Button
            variant="outline"
            hover="outline"
            onClick={() => {
              closeModal();
              navigate(`/app/requisition-slip/${rsID}`);
            }}
          >
            Continue
          </Button>
        </div>
      </Modal>

      <AttachmentAndNotesModal
        isOpen={
          modalData.type === MODAL_TYPES.ATTACHMENT ||
          modalData.type === MODAL_TYPES.NOTES
        }
        onClose={closeModal}
        type={modalData.type}
        model={MODELS.INVOICE}
        modelId={invoiceID}
      />
    </>
  );
};

InvoiceCreate.propTypes = {
  currentPage: PropTypes.number,
  currentLimit: PropTypes.number,
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  setSort: PropTypes.func,
  currentSort: PropTypes.array,
  sortBy: PropTypes.object,
};
