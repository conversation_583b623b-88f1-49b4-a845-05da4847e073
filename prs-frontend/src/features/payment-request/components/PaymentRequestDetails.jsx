import React, { useState, useRef, useMemo, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@src/components/ui/Button';
import { Form, Input } from '@src/components/ui/Form';
import { Modal } from '@src/components/ui/Modal';
import { File, TextArea } from '@src/components/ui/Form';
import FileIcon from '@src/assets/icons/file.svg?react';
import CommentIcon from '@src/assets/icons/comment.svg?react';
import { PaymentRequestItemsTable } from './PaymentRequestItemsTable';
import { SupplierDetailsModal } from '@src/features/purchase-order/components';
import { AttachmentAndNotesModal } from '@features/attachments/components';
import PropTypes from 'prop-types';
import { Pill } from '@components/ui/Pill';
import { useGetRequisitionSlip } from '@features/dashboard/api';
import { StatusSection } from '@src/features/dashboard/components/StatusSection';
import { formatDateToDMY, setDefaultDateValue } from '@utils/dateFormat';

import { useNotification } from '@hooks/useNotification';
import useRSTabStore from '@src/store/rsTabStore';
import PaymentRequestModalContent from './PaymentRequestModalContent';
import { PaymentRequestApproverSection } from './PaymentRequestApproverSection';
import { PendingAction } from './PendingAction';

import {
  useGetPaymentDetails,
  useGetPaymentRequestItems,
  useGetPaymentRequestApprovers,
  useGetPaymentRequestComments,
  useCreatePaymentRequestComment,
  useMarkNotesAsSeen,
  useMarkAttachmentAsSeen,
  useAddAdhocApprover,
  useRemoveAdhocApprover,
} from '../api';

import { MODELS } from '@config/modelsConfig';

const MODAL_TYPES = {
  NOTES: 'notes',
  ATTACHMENT: 'attachments',
};

import { useUserStore } from '@store';
import { InvoiceDetails } from './InvoiceDetails';

import {
  useAssignAttachments,
  useUploadAttachments,
  useGetAttachments,
} from '@features/attachments/api';
import VisitMainRSButton from '@src/components/ui/Button/VisitMainRSButton';

export const PaymentRequestDetails = ({
  currentPage,
  currentLimit,
  //setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
  permissions,
  setRequisitionID,
}) => {
  const [hasAttachments, setHasAttachments] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [note, setNote] = useState('');
  const [searchFilename, setSearchFilename] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const [deposit, setDeposit] = useState(0);
  const [remainingBalance, setRemainingBalance] = useState(0);
  const [retention, setRetention] = useState(0);

  const [isApprover, setIsApprover] = useState(false);
  const [approvers, setApprovers] = useState([]);
  const [addedApprover, setAddedApprover] = useState(false);

  //used for attachments and notes
  const [searchQuery, setSearchQuery] = useState({});
  const [dateData, setDateData] = useState({
    dateFrom: '',
    dateTo: '',
  });

  const mainFormRef = useRef(null);
  const hasAttachmentBadge = false;
  const hasCommentBadge = false;
  const navigate = useNavigate();

  const { resetTabs } = useRSTabStore();
  const { prId : id } = useParams();
  const { user } = useUserStore();
  const noteRef = useRef(null);

  const { showNotification } = useNotification();

  const { data: paymentRequestData, isLoading } = useGetPaymentDetails(id);
  const { mutateAsync: markNotesSeen } = useMarkNotesAsSeen();
  const { mutateAsync: markAttachmentSeen } = useMarkAttachmentAsSeen();
  const { data: requisition } = useGetRequisitionSlip(
    paymentRequestData?.requisitionId,
  );
  const {
    data: paymentRequestApprovers,
    refetch: refetchPaymentRequestApprovers,
  } = useGetPaymentRequestApprovers(id);
  const { data: paymentRequestComments, refetch: refetchNotes } =
    useGetPaymentRequestComments({
      id,
      dateFrom: dateData.dateFrom,
      dateTo: dateData.dateTo,
    });
  const { data: paymentRequestItems } = useGetPaymentRequestItems(
    {
      id: paymentRequestData?.purchaseOrderId,
      //page: currentPage,
      limit: currentLimit,
      sortBy,
      search: searchQuery?.search,
      type: requisition?.type,
    },
    { enabled: !!paymentRequestData?.purchaseOrderId },
  );
  const { data: fileAttachments, refetch: refetchAttacments } =
    useGetAttachments({
      modelId: id,
      model: 'rs_payment_request',
      search: searchFilename,
    });
  const { mutateAsync: createPaymentCommentRequest, isLoading: isUpdating } =
    useCreatePaymentRequestComment();
  const { mutateAsync: uploadAttachments, isLoading: isFileUploading } =
    useUploadAttachments();
  const { mutateAsync: assignAttachments, isLoading: isAssigning } =
    useAssignAttachments();

  const { mutateAsync: addAdhocApprover } = useAddAdhocApprover();
  const { mutateAsync: removeAdhocApprover } = useRemoveAdhocApprover();

  let requisitionID = paymentRequestData?.requisition?.id;

  const [modalData, setModalData] = useState({
    isOpen: false,
    type: '',
    attachments: [],
    isUploading: false,
    supplier: null,
  });

  useEffect(() => {
    if (paymentRequestApprovers?.data) {
      setApprovers(paymentRequestApprovers.data);
    }
  }, [paymentRequestApprovers]);

  useEffect(() => {
    if (paymentRequestApprovers && approvers) {
      checkIfUserIsApprover(paymentRequestApprovers?.data || []);
    }
  }, [paymentRequestApprovers, approvers]);

  useEffect(() => {
    if (
      paymentRequestData &&
      paymentRequestData?.status === 'draft' &&
      paymentRequestData?.requisition?.assignee?.id === user.id
    ) {
      navigate(
        `/app/requisition-slip/${paymentRequestData?.requisitionId}/payment-request/create/${paymentRequestData.id}`,
      );
    }
    setRequisitionID(paymentRequestData?.requisitionId);
  }, [paymentRequestData]);

  useEffect(() => {
    setAttachments(fileAttachments?.data);
  }, [fileAttachments]);

  const handleSupplierDetailsClick = () => {
    setModalData({
      isOpen: true,
      type: 'supplier',
      supplier: paymentRequestData?.purchaseOrder?.supplierDetails,
    });
  };

  const handleInvoiceDetailsClick = () => {
    setModalData({
      isOpen: true,
      type: 'invoice',
    });
  };

  const onCheckAttachment = () => {
    setModalData(prevData => ({
      type: MODAL_TYPES.ATTACHMENT,
      isOpen: !prevData?.isOpen,
      data: fileAttachments,
    }));
  };

  const onCheckNote = () => {
    setModalData(prevData => ({
      type: MODAL_TYPES.NOTES,
      isOpen: !prevData?.isOpen,
      data: paymentRequestComments,
    }));
  };

  const closeModal = async () => {
    if (modalData.type === MODAL_TYPES.NOTES) {
      try {
        await markNotesSeen({
          model: 'rs_payment_request',
          modelId: id,
        });
        refetchNotes();
      } catch (error) {
        console.error('Error marking notes as seen:', error);
      }
    } else {
      try {
        await markAttachmentSeen({
          model: 'rs_payment_request',
          modelId: id,
        });
        refetchAttacments();
      } catch (error) {
        console.error('Error marking attachments as seen:', error);
      }
    }
    setModalData(prevData => ({
      ...prevData,
      type: null,
      mode: null,
      data: null,
    }));
  };

  const isCIA = () => {
    const terms = paymentRequestData?.purchaseOrder?.terms;
    if (terms && terms === 'Cash in Advance (CIA)') {
      return true;
    }
    return false;
  };

  const isRetention = () => {
    const terms = paymentRequestData?.purchaseOrder?.terms;
    if (terms && terms.includes('RETENTION')) {
      return true;
    }
    return false;
  };

  const addTermsSpiel = () => {
    if (
      paymentRequestData &&
      paymentRequestData?.purchaseOrder?.terms == 'NET 15'
    ) {
      return 'Payment must be sent 15 days after the issued Invoice Date';
    } else if (
      paymentRequestData &&
      paymentRequestData?.purchaseOrder?.terms == 'NET 30'
    ) {
      return 'Payment must be sent 30 days after the issued Invoice Date';
    }
  };

  const showRemainingBalance = () => {
    const terms = paymentRequestData?.purchaseOrder?.terms;
    if (
      terms &&
      (terms.includes('NET') ||
        terms === 'Cash on Delivery (COD)' ||
        terms === 'Cash in Advance (CIA)')
    ) {
      return true;
    }
    return false;
  };

  const fileRef = useRef(null);

  const computeDeposit = (percent, total) => {
    const dp = useMemo(() => {
      return (total * (percent / 100)).toFixed(2);
    }, [total, percent]);
    setDeposit(dp);

    return dp;
  };

  const calculateRetention = (deposit, total) => {
    return useMemo(
      () => (total - deposit) * (10 / 100),
      [deposit, total],
    ).toFixed(2);
  };

  const calculateBalance = (total, deposit) => {
    return useMemo(() => total - deposit, [total, deposit]).toFixed(2);
  };

  const handleComputation = total => {
    const terms = paymentRequestData?.purchaseOrder?.terms;

    if (terms === 'NET 15') {
      computeDeposit(15, total);
    } else if (terms === 'NET 30') {
      computeDeposit(30, total);
    } else if (terms === '10% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(10, total)));
    } else if (terms === '20% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(20, total)));
    } else if (terms === '30% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(30, total)));
    } else if (terms === '50% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(50, total)));
    } else if (terms === '80% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(80, total)));
    } else if (terms === '10% DP, PB, 10% RETENTION') {
      const deposit = computeDeposit(10, total);
      const retention = calculateRetention(deposit, total);
      const balance = total - deposit;
      setRetention(retention);
      setRemainingBalance(balance);
    } else if (terms === '20% DP, PB, 10% RETENTION') {
      const deposit = computeDeposit(20, total);
      const retention = calculateRetention(deposit, total);
      const balance = total - deposit;
      setRetention(retention);
      setRemainingBalance(balance);
    } else if (terms === '30% DP, PB, 10% RETENTION') {
      const deposit = computeDeposit(30, total);
      const retention = calculateRetention(deposit, total);
      const balance = total - deposit;
      setRetention(retention);
      setRemainingBalance(balance.toFixed(2));
    }
  };

  const checkIfApprover = () => {
    return approvers.some(approver => approver.userId === user.id);
  };

  const onSubmitNote = async () => {
    try {
      let userType = '';

      if (user.id === paymentRequestData?.requisition?.assignedTo) {
        userType = 'Requestor';
      } else if (checkIfApprover(approvers)) {
        userType = 'Approver';
      } else {
        userType = 'Requestor';
      }

      const data = {
        model: 'rs_payment_request',
        modelId: parseInt(id),
        userType,
        commentType: 'Note',
        note,
      };

      await createPaymentCommentRequest(
        { data },
        {
          onSuccess: async () => {
            showNotification({
              type: 'success',
              message: 'Note added successfully',
            });
            setNote('');
            noteRef.current.clear();
            refetchNotes();
          },

          onError: error => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message ||
                'Failed to update supplier details',
            });
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  const onAssignAttachments = async attachments => {
    try {
      const attachmentIds = attachments.map(attachment => attachment.id);
      const model = 'rs_payment_request';

      await assignAttachments(
        { attachmentIds, model, modelId: parseInt(id) },
        {
          onSuccess: message => {},
          onError: error => {
            const errorMessage =
              error?.response?.data?.message || error?.message;
            showNotification({
              type: 'error',
              message: errorMessage,
            });
          },
        },
      );
    } catch (e) {
      throw e;
    }
  };

  const onSubmitAttachments = async values => {
    try {
      let fileArr = [];
      values?.attachments.forEach(attachment => {
        fileArr.push(attachment.file);
      });
      await uploadAttachments(
        {
          model: 'rs_payment_request',
          attachments: fileArr,
        },
        {
          onSuccess: async response => {
            setAttachments([]);
            fileRef.current.removeAllFiles();
            setHasAttachments(false);
            showNotification({
              type: 'success',
              message: 'Attachments uploaded successfully',
            });
            onAssignAttachments(response);
          },
          onError: error => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message || 'Failed to upload file',
            });
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  const checkIfUserIsApprover = approvers => {
    const currentUserApprovers = approvers?.filter(approver => {
      return (
        getDesignatedApprover(approver) === user.id &&
        approver.status === 'pending'
      );
    });

    if (currentUserApprovers?.length > 0) {
      for (const currentApprover of currentUserApprovers) {
        const isCurrentUserAdhoc = currentApprover.isAdhoc === true;

        const hasBlockingApprovers = approvers.some(approver => {
          const isHigherLevelPending =
            approver.level < currentApprover.level &&
            approver.status === 'pending';

          const isSameLevelPrimaryPending =
            isCurrentUserAdhoc &&
            approver.level === currentApprover.level &&
            approver.isAdhoc !== true &&
            approver.status === 'pending';

          return isHigherLevelPending || isSameLevelPrimaryPending;
        });

        if (!hasBlockingApprovers) {
          return true;
        }
      }
    }

    return false;
  };

  const getChargeToDetails = chargeToData => {
    if (chargeToData === 'project') return requisition?.project?.name;
    else if (chargeToData === 'department')
      return requisition?.department?.name;
    else if (chargeToData === 'company') return requisition?.company?.name;
    else '--';
  };

  const handleAddApprover = async newApprover => {
    await addAdhocApprover(
      {
        id: id,
        data: {
          approverId: newApprover.value,
        },
      },
      {
        onSuccess: () => {
          showNotification({
            type: 'success',
            message: 'Successfully added approver!',
          });
          refetchPaymentRequestApprovers();
        },
      },
    );
  };

  const handleDeleteApprover = async approverId => {
    await removeAdhocApprover(
      {
        id: id,
      },
      {
        onSuccess: () => {
          showNotification({
            type: 'success',
            message: 'Successfully removed approver!',
          });
          refetchPaymentRequestApprovers();
        },
      },
    );
  };

  const getDesignatedApprover = approver => {
    if (approver && requisition) {
      const dateRequired = new Date(
        setDefaultDateValue(requisition?.dateRequired),
      ).setHours(0, 0, 0, 0);
      if (approver?.approver?.userLeaves?.length > 0) {
        const found = approver?.approver?.userLeaves.find(leave => {
          const startDate = new Date(leave.startDate).setHours(0, 0, 0, 0);
          const endDate = new Date(leave.endDate).setHours(0, 0, 0, 0);
          return startDate <= dateRequired && dateRequired <= endDate;
        });
        if (!found) return approver?.userId;

        if (found && approver?.altApproverId && approver?.altApproverId) {
          return approver?.altApproverId;
        }
      }
      return approver?.userId;
    }
  };

  const currentUser = approvers.find(approver => {
    return getDesignatedApprover(approver) === user.id;
  });

  let currentUserId = getDesignatedApprover(currentUser);
  const isPaymentApprover = !!currentUser;
  const shouldShowApproverModal = approvers.some(
    approver =>
      approver.userId === currentUserId && approver.status === 'pending',
  );

  const canAddAdhoc = useMemo(() => {
    if (!approvers) return false;

    const currentUserApprovers = approvers.filter(
      approver => approver.level === currentUser?.level,
    );

    return currentUserApprovers.length <= 1;
  }, [approvers, currentUser?.level]);

  return (
    <>
      {isPaymentApprover &&
        paymentRequestData?.status !== 'draft' &&
        paymentRequestData?.status !== 'rejected' &&
        checkIfUserIsApprover(approvers) && (
          <PendingAction
            id={id}
            approvers={approvers}
            shouldShowApproverModal={shouldShowApproverModal}
            onAddApprover={handleAddApprover}
            addedApprover={addedApprover}
            canAddAdhoc={canAddAdhoc}
          />
        )}
      <div className="p-6">
        <div className="flex justify-between items-center border-t pt-5 border-b pb-5 mb-6">
          <div>
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold">Payment Request Number:</span>
              <span className="text-lg font-semibold">
                {paymentRequestData?.status === 'draft'
                  ? '--'
                  : 'VR' +
                    '-' +
                    paymentRequestData?.requisition?.companyCode +
                    paymentRequestData?.prLetter +
                    paymentRequestData?.prNumber}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-bold">R.S. Number:</span>
              <span className="font-semibold">{requisition?.rsNumber}</span>
            </div>
          </div>
          <div className="flex gap-5">
            <VisitMainRSButton rsId={requisitionID} />
          </div>
        </div>
        <Form
          ref={mainFormRef}
          hasErrorSpace={false}
          onSubmit={onSubmitAttachments}
          options={{
            shouldUnregister: true,
            values: useMemo(() => {
              return {
                payableDate:
                  formatDateToDMY(paymentRequestData?.payableDate) ?? '--',
                payableTo:
                  paymentRequestData?.purchaseOrder?.supplierDetails?.name ??
                  '--',
                chargeTo:
                  getChargeToDetails(
                    paymentRequestData?.requisition?.chargeTo,
                  ) ?? '--',
                discountAmount:
                  paymentRequestData?.discountIn === 'Fixed Amount'
                    ? `₱${paymentRequestData?.discountAmount}`
                    : `${paymentRequestData?.discountAmount}%`,
                deliveryAddress:
                  paymentRequestData?.requisition?.deliveryAddress ?? '--',
                po: paymentRequestData?.purchaseOrder?.poNumber?.includes('PO')
                  ? paymentRequestData?.purchaseOrder?.poNumber
                  : ('PO-' +
                      paymentRequestData?.requisition?.companyCode +
                      paymentRequestData?.purchaseOrder?.poLetter +
                      '' +
                      paymentRequestData?.purchaseOrder?.poNumber ?? '--'),
                supplier:
                  paymentRequestData?.purchaseOrder?.supplierDetails?.name,
                terms: paymentRequestData?.purchaseOrder?.terms ?? '--',
                deposit: deposit,
                retention: retention,
                employee:
                  paymentRequestData?.employee?.firstName +
                  ' ' +
                  paymentRequestData?.employee?.lastName,
                remainingBalance: remainingBalance,
                warranty:
                  paymentRequestData?.purchaseOrder?.warranty?.name ?? '--',
              };
            }, [paymentRequestData, deposit, retention, remainingBalance]),
          }}
        >
          {({ control, register }) => (
            <>
              {/* Main Content : Request Details */}
              <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 mb-10">
                {/* Left Column - Form */}
                <div className="flex-1 bg-white p-6 rounded-lg shadow-sm">
                  <h2 className="text-xl font-bold mb-6">Request Details</h2>
                  <hr className="mb-7 my-2 col-span-2" />

                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      label="Payable Date"
                      registration={{ ...register('payableDate') }}
                      control={control}
                      disabled
                    />
                    <Input
                      label="Payable To"
                      registration={{ ...register('payableTo') }}
                      control={control}
                      disabled
                    />
                    <Input
                      label="Charge To"
                      value={getChargeToDetails(
                        paymentRequestData?.requisition?.chargeTo,
                      )}
                      control={control}
                      disabled
                    />
                    <Input
                      label="Discount (in price or percentage)"
                      registration={{ ...control.register('discountAmount') }}
                      control={control}
                      disabled
                    />
                  </div>

                  <hr className="my-6" />

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Button
                        onClick={onCheckAttachment}
                        icon={FileIcon}
                        iconSize="h-5 w-auto"
                        type="button"
                        variant="outline"
                        hover="outline"
                        label={
                          <div className="flex justify-between align-middle">
                            <p className="pb-2">Attachments</p>
                            {fileAttachments?.hasNewNotifications && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8] align-middle">
                                New Attachment/s
                              </Pill>
                            )}
                          </div>
                        }
                      >
                        Check Attachments
                      </Button>
                    </div>

                    <div className="flex flex-col ">
                      <Button
                        onClick={onCheckNote}
                        icon={CommentIcon}
                        type="button"
                        iconSize="h-5 w-auto"
                        variant="outline"
                        hover="outline"
                        label={
                          <div className="flex justify-between align-middle">
                            <p className="pb-2">Notes</p>
                            {paymentRequestComments?.hasNewNotifications && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8]">
                                New Comment/s
                              </Pill>
                            )}
                          </div>
                        }
                      >
                        Check Notes
                      </Button>
                    </div>

                    <div className="flex flex-col">
                      <File
                        isUploading={isUploading}
                        ref={fileRef}
                        control={control}
                        name={'attachments'}
                        attachments={attachments}
                        hasAttachments={setHasAttachments}
                        fileTypeRestrictions=".png,.jpg,.jpeg,.pdf,.xls,.xlsx,.csv,.docx"
                      />
                      <div className="flex justify-between items-center mt-1">
                        <div className="text-[#4F4F4F] text-[12px]">
                          The maximum size for each file is 25 MB. File formats
                          - PNG, DOCX, JPG, JPEG, PDF, Excel, CSV.
                        </div>
                        <Button
                          variant="outline"
                          type="submit"
                          disabled={!hasAttachments === true ? true : null}
                          hover="outline"
                          className="w-32 h-8 text-sm text-[#420001] border-[#420001] ml-4"
                        >
                          Submit
                        </Button>
                      </div>
                    </div>
                    <div className="col-span-1">
                      <div className="flex flex-col">
                        <TextArea
                          label="Add Notes"
                          name={'notes'}
                          registration={{ ...control.register('notes') }}
                          control={control}
                          value={note}
                          placeholder="Input notes"
                          onInput={e => setNote(e.target.value)}
                          maxCharacters={100}
                          ref={noteRef}
                          className="mb-0"
                        />
                        <div className="flex justify-end items-center">
                          <div className="ml-4">
                            <Button
                              variant="outline"
                              disabled={note !== '' ? null : true}
                              hover="outline"
                              type="button"
                              onClick={onSubmitNote}
                              className="!w-32 h-8 text-sm"
                            >
                              Submit
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column - Status Section */}
                <div className="w-full lg:w-[300px]">
                  <StatusSection
                    headerCn={'lg:border-b w-full p-1'}
                    status={
                      paymentRequestData?.status === 'submitted'
                        ? 'for_pr_approval'
                        : paymentRequestData?.status
                    }
                  />

                  <div className="flex flex-col sm:flex-row lg:flex-col justify-between items-start sm:items-center lg:items-start gap-2 max-w-full mx-auto bg-[#FFFFFF] p-6 rounded-lg shadow-sm text-sm mb-5">
                    <h3 className="font-bold text-[#4F575E]">Assigned to</h3>
                    <hr className="w-full border-t border-gray-300" />
                    <p>{`${paymentRequestData?.requisition?.assignee?.firstName} ${paymentRequestData?.requisition?.assignee?.lastName}`}</p>
                  </div>

                  <PaymentRequestApproverSection
                    dateRequired={requisition?.dateRequired}
                    approvers={approvers}
                    shouldShowApproverModal={shouldShowApproverModal}
                    addedApprover={addedApprover}
                    onAddApprover={handleAddApprover}
                    onDeleteApprover={handleDeleteApprover}
                    assignedTo={paymentRequestData?.assignedTo}
                    canAddAdhoc={canAddAdhoc}
                  />
                </div>
              </div>

              {/* Main Content : Purchase Order Details */}
              <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 mb-9">
                <div className="flex-1 bg-white p-6 rounded-lg shadow-sm mb-9">
                  <h2 className="text-xl font-bold mb-6">
                    Purchase Order Details
                  </h2>
                  <hr className="mb-7 my-2 col-span-2" />
                  <Input
                    label="Purchase Order"
                    className="mb-3"
                    registration={{ ...control.register('po') }}
                    control={control}
                    disabled
                  />
                  <hr className="mb-7 my-2 col-span-2" />
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid grid-cols-12 gap-3">
                      <div className="col-span-9">
                        <Input
                          label="Supplier"
                          registration={{ ...control.register('supplier') }}
                          control={control}
                          disabled
                        />
                      </div>
                      <div className="col-span-3">
                        <Button
                          variant="outline"
                          type="button"
                          hover="outline"
                          className="mt-6 px-2 whitespace-nowrap text-sm h-10"
                          onClick={handleSupplierDetailsClick}
                        >
                          Supplier Details
                        </Button>
                      </div>
                    </div>
                    <Input
                      label="Delivery Address"
                      registration={{ ...control.register('deliveryAddress') }}
                      control={control}
                      disabled
                    />
                    <div>
                      <Input
                        label="Terms"
                        registration={{ ...control.register('terms') }}
                        control={control}
                        disabled
                      />
                      <div className="text-xs">{addTermsSpiel()}</div>
                    </div>
                    <div className={isCIA() ? '' : 'hidden'}>
                      <Input
                        label="Employee"
                        registration={{ ...control.register('employee') }}
                        control={control}
                        disabled
                      />
                    </div>

                    <div className={!isCIA() ? '' : 'hidden'}>
                      <div className="grid grid-cols-12 gap-3">
                        <div
                          className={
                            isRetention() ? 'col-span-6' : 'col-span-6 hidden'
                          }
                        >
                          <Input
                            label="Percent Retention (10%)"
                            registration={{ ...control.register('retention') }}
                            control={control}
                            disabled
                          />
                        </div>

                        <div
                          className={
                            isRetention() ? 'col-span-6' : 'col-span-12'
                          }
                        >
                          <Input
                            label="Deposit %"
                            registration={{ ...control.register('deposit') }}
                            control={control}
                            disabled
                          />
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-12 gap-3">
                      <div
                        className={
                          showRemainingBalance()
                            ? 'col-span-12 hidden'
                            : 'col-span-6'
                        }
                      >
                        <Input
                          label="Remaining Balance"
                          registration={{
                            ...control.register('remainingBalance'),
                          }}
                          control={control}
                          disabled
                        />
                      </div>
                      <div
                        className={
                          showRemainingBalance() ? 'col-span-12' : 'col-span-6'
                        }
                      >
                        <Input
                          label="Warranty"
                          registration={{ ...control.register('warranty') }}
                          control={control}
                          disabled
                        />
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-600">
                        Invoice Details
                      </div>
                      <Button
                        variant="outline"
                        type="button"
                        hover="outline"
                        className="px-2 whitespace-nowrap text-sm h-10"
                        onClick={handleInvoiceDetailsClick}
                      >
                        View Invoice
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </Form>

        {/* Modals */}

        <AttachmentAndNotesModal
          isOpen={
            modalData.type === MODAL_TYPES.ATTACHMENT ||
            modalData.type === MODAL_TYPES.NOTES
          }
          onClose={closeModal}
          type={modalData.type}
          model={MODELS.PAYMENT_REQUEST}
          modelId={id}
        />

        <div className="mt-6">
          <h2 className="text-xl font-bold">Items</h2>
          <div className="mt-4">
            <PaymentRequestItemsTable
              onSearch={val => setSearchQuery(val)}
              //setPage={setPage}
              setSort={setSort}
              currentPage={currentPage}
              currentSort={currentSort}
              type="ofm"
              itemsData={
                paymentRequestItems?.data ? paymentRequestItems?.data : []
              }
              otherCharges={{
                withholdingTax:
                  paymentRequestData?.withholdingTaxDeduction ?? 0,
                extraCharges: paymentRequestData?.extraCharges ?? 0,
                deliveryFee: paymentRequestData?.deliveryFee ?? 0,
                tip: paymentRequestData?.tip ?? 0,
              }}
              discount={{
                isFixed:
                  paymentRequestData?.discountIn === 'Fixed Amount'
                    ? true
                    : false,
                value: paymentRequestData?.discountAmount ?? 0,
              }}
              handleTotalAmount={total => {
                handleComputation(total);
              }}
            />
          </div>
        </div>

        {paymentRequestData &&
          modalData.type === 'supplier' &&
          modalData.isOpen && (
            <SupplierDetailsModal
              isOpen={modalData.type === 'supplier' && modalData.isOpen}
              onClose={closeModal}
              supplierId={modalData?.supplier?.id}
              supplier={modalData.supplier}
              onCheckAttachment={onCheckAttachment}
              onCheckNote={onCheckNote}
            />
          )}

        <InvoiceDetails
          deliveryReciept={paymentRequestData?.deliveryInvoice}
          deliveryInvoiceAttachment={
            paymentRequestData?.deliveryInvoice?.invoiceAttachment
          }
          isOpen={modalData.type === 'invoice' && modalData.isOpen}
          onClose={closeModal}
        />
      </div>
    </>
  );
};

PaymentRequestDetails.propTypes = {
  currentPage: PropTypes.number,
  currentLimit: PropTypes.number,
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  setSort: PropTypes.func,
  currentSort: PropTypes.array,
  sortBy: PropTypes.object,
  setRequisitionID: PropTypes.func,
};
