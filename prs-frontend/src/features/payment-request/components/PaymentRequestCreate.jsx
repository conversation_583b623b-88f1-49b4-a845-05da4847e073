import React, { useState, useRef, useMemo, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import XIcon from '@assets/icons/xicon.svg?react';
import { Button } from '@src/components/ui/Button';
import { Form, Input, Select } from '@src/components/ui/Form';
import { Modal } from '@src/components/ui/Modal';
import { File, TextArea } from '@src/components/ui/Form';
import { PaymentRequestItemsTable } from './PaymentRequestItemsTable';
import { SupplierDetailsModal } from '@src/features/purchase-order/components';
import { RadioButtonGroup } from '@components/ui/RadioButtonGroup';
import PropTypes from 'prop-types';
import { useGetRequisitionSlip } from '@features/dashboard/api';
import { setDefaultDateValue } from '@utils/dateFormat';
import { useNotification } from '@src/hooks/useNotification';
import useRSTabStore from '@src/store/rsTabStore';
import PercentIcon from '@assets/icons/percent.svg?react';
import PesoIcon from '@assets/icons/peso.svg?react';

import {
  useGetPaymentDetails,
  useGetPaymentRequestItems,
  useCreatePaymentRequest,
  useGetPaymentRequestAttachments,
  useGetPaymentRequestComments,
  useCreatePaymentRequestComment,
  usegetPOList,
  useGetPoDetails,
  useGetSupplierDetails,
} from '../api';

import { prFormSchema } from '@src/schema/payment-request-schema';

import {
  useAssignAttachments,
  useUploadAttachments,
} from '@features/attachments/api';

import { useGetApprovers } from '@features/user/api';

import { useUserStore } from '@store';
import { InvoiceDetails } from './InvoiceDetails';
import VisitMainRSButton from '@src/components/ui/Button/VisitMainRSButton';
import {
  matchingAccountCodeExtractor,
  nonSteelbarsItemsExtractor,
  steelbarsItemsExtractor,
} from '@src/utils/itemListSelector';
import { useGetOFMItems } from '@src/features/ofm/api';
import { SteelbarsTable } from '@src/features/dashboard/components/SteelbarsTable';

export const PaymentRequestCreate = ({
  currentPage,
  currentLimit,
  //setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
}) => {
  const [hasAttachments, setHasAttachments] = useState(false);
  const [attachmentsArr, setAttachments] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isDiscount, setIsDiscount] = useState(false);
  const [PurchaseOrderOptions, setPurchaseOrderOptions] = useState([]);
  const [employeesOption, setEmployeesOptions] = useState([]);
  const [isCancelReqModalOpen, setIsCancelReqModalOpen] = useState(false);

  const { resetTabs } = useRSTabStore();
  const { showNotification } = useNotification();

  //Used in terms data - with computation
  const [deposit, setDeposit] = useState(0);
  const [remainingBalance, setRemainingBalance] = useState(0);
  const [retention, setRetention] = useState(0);

  //Used in item table
  const [computationData, setComputationData] = useState({
    discountAmount: 0,
    deliveryFee: 0,
    withholdingTaxDeduction: 0,
    extraCharges: 0,
    terms: '',
    comment: '',
    payableDate: '',
    tip: 0,
  });

  const [hasAdditionalFees, setHasAdditionalFees] = useState(false);

  const [defaultPO, setDefaultPO] = useState(null);
  const [defaultEmployee, setDefaultEmployee] = useState(null);
  const [generatedId, setGeneratedId] = useState(null);
  const [generatedComment, setGeneratedComment] = useState(null);
  const [generatedFormData, setGeneratedFormData] = useState(null);
  const [selectedPo, setSelectedPo] = useState(null);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [totalAmout, setTotalAmount] = useState(0);
  const [discountAmountValue, setDiscountAmount] = useState(0);
  const [withholdingTaxValue, setWithholdingTax] = useState(0);
  const [deliveryFeeValue, setDeliveryFee] = useState(0);
  const [extraChargeValue, setExtraCharge] = useState(0);
  const [tipValue, setTip] = useState(0);
  const [searchQuery, setSearchQuery] = useState({});

  const mainFormRef = useRef(null);
  const poRef = useRef(null);
  const navigate = useNavigate();
  const { id: rsID, prId: id } = useParams();
  const { user } = useUserStore();

  const { data: paymentRequestData } = useGetPaymentDetails(id, {
    enabled: !!id,
  });
  const { data: poDetails } = useGetPoDetails(
    selectedPo || paymentRequestData?.purchaseOrderId,
    {
      enabled: !!selectedPo || !!paymentRequestData?.purchaseOrderId,
    },
  );

  const { data: supplierDetails } = useGetSupplierDetails({
    id: poDetails?.result?.supplierId,
    type:
      poDetails?.result?.supplierType === 'company' ? 'companies' : 'suppliers',
    config: {
      enabled: !!poDetails?.result?.supplierId,
    },
  });
  const { data: poList, refetch: refetchPOList } = usegetPOList(rsID, {
    enabled: !!rsID,
  });
  const { data: paymentRequestAttachments } = useGetPaymentRequestAttachments(
    id,
    {
      enabled: !!id,
    },
  );
  const { data: requisition } = useGetRequisitionSlip(rsID, {
    enabled: !!rsID,
  });

  const { data: paymentRequestItems } = useGetPaymentRequestItems(
    {
      id: id ? paymentRequestData?.purchaseOrderId : selectedPo?.toString(),
      //page: currentPage,
      limit: currentLimit,
      sortBy,
      search: searchQuery?.search,
      type: requisition?.type,
    },
    {
      enabled: !!selectedPo || !!paymentRequestData?.purchaseOrderId,
    },
  );

  const { data: OFMItems } = useGetOFMItems({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });

  const { data: employees } = useGetApprovers({ paginate: false });

  const { mutateAsync: createPaymentRequest } = useCreatePaymentRequest();

  const { mutateAsync: createPaymentCommentRequest, isLoading: isUpdating } =
    useCreatePaymentRequestComment();
  const { mutateAsync: uploadAttachments, isLoading: isFileUploading } =
    useUploadAttachments();
  const { mutateAsync: assignAttachments, isLoading: isAssigning } =
    useAssignAttachments();

  const discountValueRef = useRef(null);

  let requisitionID = paymentRequestData?.requisitions?.id;
  const [modalData, setModalData] = useState({
    isOpen: false,
    type: '',
    attachments: [],
    isUploading: false,
    supplier: null,
  });

  useEffect(() => {
    refetchPOList();
  }, []);

  useEffect(() => {
    if (paymentRequestData) {
      const discountType =
        paymentRequestData?.discountIn === 'Fixed Amount' ? false : true;

      setIsDiscount(discountType);
      if (
        paymentRequestData?.discountAmount ||
        paymentRequestData?.withholdingTaxDeduction ||
        paymentRequestData?.deliveryFee ||
        paymentRequestData?.tip
      ) {
        setHasAdditionalFees(true);

        setComputationData({
          discountAmount: parseFloat(paymentRequestData?.discountAmount || 0),
          deliveryFee: parseFloat(paymentRequestData?.deliveryFee || 0),
          withholdingTaxDeduction: parseFloat(
            paymentRequestData?.withholdingTaxDeduction || 0,
          ),
          extraCharges: parseFloat(paymentRequestData?.extraCharges || 0),
          terms: paymentRequestData?.purchaseOrder?.terms,
          tip: parseFloat(paymentRequestData?.tip || 0),
          payableDate: paymentRequestData?.payableDate,
          comment: '',
        });
      }

      if (
        paymentRequestData &&
        paymentRequestData?.status !== 'draft' &&
        paymentRequestData?.status !== 'rejected'
      ) {
        navigate(`/app/requisition-slip/${requisition.id}/payment-request/${id}`);
      }
    }
  }, [paymentRequestData]);

  useEffect(() => {
    if (generatedId) {
      if (generatedComment) {
        onSubmitNote();
      }
      submitAttachments();
    }
  }, [generatedId]);

  useEffect(() => {
    if (poDetails && id && requisition) {
      const options = [
        {
          key:
            'PO-' +
            requisition?.companyCode +
            requisition?.rsLetter +
            poDetails?.result?.poNumber,
          value: poDetails?.result?.id,
        },
      ];

      setPurchaseOrderOptions(options);

      setDefaultPO({
        key:
          'PO-' +
          requisition?.companyCode +
          requisition?.rsLetter +
          poDetails?.result?.poNumber,
        value: poDetails?.result?.id,
      });
      setSelectedPo(poDetails?.result?.id);
    }
  }, [poDetails]);

  useEffect(() => {
    setAttachments(paymentRequestAttachments?.data);
  }, [paymentRequestAttachments]);

  useEffect(() => {
    if (poList && paymentRequestData) {
    } else {
      const options = poList?.result?.data.map(po => {
        const poValue = po?.poNumber;
        return {
          key: poValue,
          value: po?.id,
        };
      });

      setPurchaseOrderOptions(options);
    }
  }, [poList, paymentRequestData]);

  useEffect(() => {
    if (employees && paymentRequestData) {
      const options = employees?.data?.map(employee => {
        if (
          paymentRequestData &&
          paymentRequestData?.employee?.id === employee?.id
        ) {
          setDefaultEmployee({
            key: `${employee.firstName} ${employee.lastName}`,
            value: employee.id,
          });
        }
        return {
          key: `${employee.firstName} ${employee.lastName}`,
          value: employee.id,
        };
      });

      setEmployeesOptions(options);
    } else {
      const options = employees?.data?.map(employee => {
        return {
          key: `${employee.firstName} ${employee.lastName}`,
          value: employee.id,
        };
      });

      setEmployeesOptions(options);
    }
  }, [employees, paymentRequestData]);

  const discountTypeOptions = [
    { key: 'Fixed Amount', value: 'fixed' },
    { key: 'Percentage', value: 'percent' },
  ];

  const handleSupplierDetailsClick = () => {
    setModalData({
      isOpen: true,
      type: 'supplier',
      supplier: supplierDetails,
    });
  };

  const handleInvoiceDetailsClick = () => {
    setModalData({
      isOpen: true,
      type: 'invoice',
    });
  };

  const handleCancelClick = () => {
    setModalData({
      isOpen: true,
      type: 'cancel',
    });
  };

  const onCheckAttachment = () => {
    setModalData(prevData => ({
      type: 'attachment',
      isOpen: !prevData?.isOpen,
    }));
  };
  const onCheckNote = () => {
    setModalData(prevData => ({
      type: 'comment',
      isOpen: !prevData?.isOpen,
    }));
  };

  const closeModal = () => {
    setModalData(prevData => ({
      ...prevData,
      isOpen: !prevData?.isOpen,
    }));
  };

  const fileRef = useRef(null);

  const onCloseReqModal = () => {
    setIsCancelReqModalOpen(false);
  };

  const handleInputChange = (field, value) => {
    if (field === 'discountType') {
      setIsDiscount(!isDiscount);
    }
  };

  const isCIA = () => {
    const terms = id
      ? paymentRequestData?.purchaseOrder?.terms
      : poDetails?.result?.terms;
    if (terms && terms === 'Cash in Advance (CIA)') {
      return true;
    }
    return false;
  };

  const isRetention = () => {
    const terms = id
      ? paymentRequestData?.purchaseOrder?.terms
      : poDetails?.result?.terms;
    if (terms && terms.includes('RETENTION')) {
      return true;
    }
    return false;
  };

  const showRemainingBalance = () => {
    const terms = id
      ? paymentRequestData?.purchaseOrder?.terms
      : poDetails?.result?.terms;
    if (
      terms &&
      (terms.includes('NET') ||
        terms === 'Cash on Delivery (COD)' ||
        terms === 'Cash in Advance (CIA)')
    ) {
      return true;
    }
    return false;
  };

  const computeDeposit = (percent, total) => {
    const dp = useMemo(() => {
      return (total * (percent / 100)).toFixed(2);
    }, [total, percent]);
    setDeposit(dp);

    return dp;
  };

  const calculateRetention = (deposit, total) => {
    return useMemo(
      () => (total - deposit) * (10 / 100),
      [deposit, total],
    ).toFixed(2);
  };

  const calculateBalance = (total, deposit) => {
    return useMemo(() => total - deposit, [total, deposit]).toFixed(2);
  };

  const handleComputation = (total, terms) => {
    if (terms === 'NET 15') {
      computeDeposit(15, total);
    } else if (terms === 'NET 30') {
      computeDeposit(30, total);
    } else if (terms === '10% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(10, total)));
    } else if (terms === '20% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(20, total)));
    } else if (terms === '30% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(30, total)));
    } else if (terms === '50% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(50, total)));
    } else if (terms === '80% DP, Balance upon delivery') {
      setRemainingBalance(calculateBalance(total, computeDeposit(80, total)));
    } else if (terms === '10% DP, PB, 10% RETENTION') {
      const deposit = computeDeposit(10, total);
      const retention = calculateRetention(deposit, total);
      const balance = total - deposit;
      setRetention(retention);
      setRemainingBalance(balance);
    } else if (terms === '20% DP, PB, 10% RETENTION') {
      const deposit = computeDeposit(20, total);
      const retention = calculateRetention(deposit, total);
      const balance = total - deposit;
      setRetention(retention);
      setRemainingBalance(balance);
    } else if (terms === '30% DP, PB, 10% RETENTION') {
      const deposit = computeDeposit(30, total);
      const retention = calculateRetention(deposit, total);
      const balance = total - deposit;
      setRetention(retention);
      setRemainingBalance(balance.toFixed(2));
    }

    setTotalAmount(total);
  };

  const addTermsSpiel = () => {
    if (poDetails && poDetails?.result?.terms == 'NET 15') {
      return 'Payment must be sent 15 days after the issued Invoice Date';
    } else if (poDetails && poDetails?.result?.terms == 'NET 30') {
      return 'Payment must be sent 30 days after the issued Invoice Date';
    }
  };

  const onSubmitNote = async () => {
    try {
      const data = {
        model: 'rs_payment_request',
        modelId: parseInt(generatedId),
        userType: 'Requestor',
        commentType: 'Note',
        note: generatedComment,
      };

      await createPaymentCommentRequest({
        data,
      });
    } catch (error) {
      throw error;
    }
  };

  const handleAssignAttachments = async attachments => {
    try {
      const attachmentIds = attachments.map(attachment => attachment.id);
      const model = 'rs_payment_request';

      await assignAttachments(
        { attachmentIds, model, modelId: parseInt(generatedId) },
        {
          onSuccess: message => {
            if (modalData.type === 'submit') {
              navigate(`/app/requisition-slip/${requisition.id}/payment-request/${generatedId}`);
            } else {
              navigate(`/app/requisition-slip/${rsID}/payment-request/create/${generatedId}`);
            }
          },
          onError: error => {
            const errorMessage =
              error?.response?.data?.message || error?.message;
            showNotification({
              type: 'error',
              message: errorMessage,
            });
          },
        },
      );
    } catch (e) {
      throw e;
    }
  };

  const submitAttachments = async () => {
    try {
      await uploadAttachments(
        {
          model: 'rs_payment_request',
          attachments: generatedFormData,
        },
        {
          onSuccess: async response => {
            setAttachments([]);
            fileRef.current.removeAllFiles();
            setHasAttachments(false);
            handleAssignAttachments(response);
            /*
            showNotification({
              type: 'success',
              message: 'Attachments uploaded successfully',
            });
            */
          },

          onError: error => {
            showNotification({
              type: 'error',
              message:
                error?.response?.data?.message || 'Failed to upload file',
            });
          },
        },
      );
    } catch (error) {
      throw error;
    }
  };

  const preventMinus = e => {
    if (e.code === 'Minus') {
      e.preventDefault();
    }
  };

  const showMessageOnError = error => {
    if (error) {
      return <div className="text-red-600/100 text-sm">{error}</div>;
    }
  };

  const handleSubmit = async values => {
    try {
      let fileArr = [];
      values?.attachments?.forEach(attachment => {
        fileArr.push(attachment.file);
      });

      const formData = {};
      setGeneratedComment(values?.comment);

      //NOTE: TO UPDATE!!!
      if (modalData.type !== 'submit' || id) {
        formData['id'] = id;
      }

      if ((selectedPo || defaultPO?.value) && values?.payableDate) {
        formData['purchaseOrderId'] = selectedPo
          ? selectedPo
          : defaultPO?.value;
        formData['terms'] = 'CIA'; //values?.terms?.toString() === '--' ? 'CIA' : values?.terms?.toString(),
        formData['isDraft'] = modalData.type === 'submit' ? 'false' : 'true';
        formData['requisitionId'] = requisition?.id;
        formData['comment'] = values?.comment;
        formData['totalAmount'] = totalAmout.toFixed(2).toString();
        formData['termsData'] = {
          employeeId: selectedEmployee
            ? selectedEmployee
            : defaultEmployee?.value,
        };

        if (hasAdditionalFees) {
          formData['withholdingTaxDeduction'] =
            computationData.withholdingTaxDeduction.toFixed(2).toString();

          formData['deliveryFee'] = computationData.deliveryFee
            .toFixed(2)
            .toString();
          formData['tip'] = computationData.tip.toFixed(2).toString();
          formData['extraCharges'] = computationData.extraCharges
            .toFixed(2)
            .toString();
        }
        formData['payableDate'] = values?.payableDate;
        formData['discountIn'] = isDiscount ? 'Percentage' : 'Fixed Amount';
        formData['discountAmount'] = computationData.discountAmount
          .toFixed(2)
          .toString();
        formData['deliveryInvoiceId'] =
          poDetails?.result?.deliveryInvoice?.id ??
          paymentRequestData?.deliveryInvoiceId;

        setGeneratedFormData(fileArr);
        await createPaymentRequest(formData, {
          onSuccess: ({ result }) => {
            setGeneratedId(result?.paymentRequest?.id);

            closeModal();
            showNotification({
              type: 'success',
              message:
                modalData.type === 'submit'
                  ? 'Purchase Request submitted successfully'
                  : 'Draft created successfully',
            });
          },
        });
      } else {
        throw 'Purchase Order ID and Payable Date is required';
      }
    } catch (error) {
      closeModal();
      const errorMessage = error?.response?.data?.message || error?.message;
      showNotification({
        type: 'error',
        message: errorMessage,
      });
      setIsConfirmModalOpen(false);
    }
  };

  const onSelectedPO = value => {
    setSelectedPo(value);
  };

  const onSelectedEmployee = value => {
    setSelectedEmployee(value);
  };

  const showAdditionalFees = willShow => {
    setHasAdditionalFees(willShow);
  };

  const getChargeToDetails = chargeToData => {
    if (chargeToData === 'project') return requisition?.project?.name;
    else if (chargeToData === 'department')
      return requisition?.department?.name;
    else if (chargeToData === 'company') return requisition?.company?.name;
  };

  const hasSteelbars = paymentRequestItems?.data?.some(
    item => item.isSteelbars === true,
  );

  const steelbarItemsList = steelbarsItemsExtractor(
    paymentRequestItems?.data,
    OFMItems?.data,
  );

  const steelbarItems = matchingAccountCodeExtractor(
    steelbarItemsList,
    paymentRequestItems?.data,
  );

  const nonSteelbarItems = nonSteelbarsItemsExtractor(
    steelbarItems,
    paymentRequestItems?.data,
  );

  return (
    <>
      <div className="p-6 flex flex-col gap-4">
        {/* Header with PO Numbers and Actions */}
        <div className="flex flex-col md:flex-row md:justify-end md:items-center gap-4 mb-4 md:absolute md:top-8 md:right-8 text-sm">
          <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
            <Button
              className="w-full xl:w-fit mt-2 xl:mt-0"
              variant="outline"
              hover="outline"
              onClick={() =>
                setModalData({
                  isOpen: true,
                  type: 'draft',
                })
              }
              // disabled={!isFormValid}
            >
              Save Draft
            </Button>
          </div>
        </div>

        <div className="flex justify-between items-center border-t pt-5 border-b pb-5 mb-6">
          <div>
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold">Payment Request Number:</span>
              <span className="text-lg font-semibold">
                {paymentRequestData?.isDraft === true
                  ? 'VR' +
                    '-' +
                    paymentRequestData?.requisition?.companyCode +
                    paymentRequestData?.prLetter +
                    paymentRequestData?.prDraftNumber
                  : '--'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-bold">R.S. Number:</span>
              <span className="font-semibold">{requisition?.rsNumber}</span>
            </div>
          </div>
          <div className="flex gap-5">
            <VisitMainRSButton
              rsId={rsID}
            />
          </div>
        </div>
        <Form
          ref={mainFormRef}
          onSubmit={handleSubmit}
          hasErrorSpace={false}
          schema={prFormSchema}
          options={{
            //shouldUnregister: true,
            values: useMemo(() => {
              let values = {};
              if (id) {
                values = {
                  poId: defaultPO?.value,
                  employee: defaultEmployee?.value,
                  payableDate:
                    setDefaultDateValue(paymentRequestData?.payableDate) ??
                    '--',
                  payableTo: supplierDetails?.name ?? '--',
                  deliveryFee: paymentRequestData?.deliveryFee ?? 0,
                  withholdingTaxDeduction:
                    paymentRequestData?.withholdingTaxDeduction ?? 0,
                  tip: paymentRequestData?.tip ?? 0,
                  extraCharges: paymentRequestData?.extraCharges ?? 0,
                  chargeTo: getChargeToDetails(requisition?.chargeTo) ?? '',
                  discountAmount: paymentRequestData?.discountAmount,
                  deliveryAddress:
                    paymentRequestData?.requisition?.deliveryAddress ?? '--',
                  po:
                    paymentRequestData?.purchaseOrder?.poLetter +
                      '' +
                      paymentRequestData?.purchaseOrder?.poNumber ?? '--',
                  supplier: supplierDetails?.name,
                  terms: paymentRequestData?.purchaseOrder?.terms ?? '--',
                  comment: '',
                  warranty:
                    paymentRequestData?.purchaseOrder?.warranty?.name ?? '--',
                };
              } else {
                values = {
                  poId: selectedPo,
                  employee: selectedEmployee,
                  payableDate: computationData?.payableDate ?? '--',
                  payableTo: supplierDetails?.name ?? '--',
                  chargeTo: getChargeToDetails(requisition?.chargeTo) ?? '--',
                  discountAmount: paymentRequestData?.discountAmount ?? 0,
                  deliveryAddress: requisition?.deliveryAddress ?? '--',
                  deliveryFee: 0,
                  withholdingTaxDeduction: 0,
                  tip: 0,
                  extraCharges: 0,
                  po:
                    paymentRequestData?.purchaseOrder?.poLetter +
                      '' +
                      paymentRequestData?.purchaseOrder?.poNumber ?? '--',
                  supplier: supplierDetails?.name ?? '--',
                  terms: poDetails?.result?.terms ?? '--',
                  warranty: poDetails?.result?.warranty?.name ?? '--',
                };
              }
              return values;
            }, [
              paymentRequestData,
              poDetails,
              requisition,
              selectedPo,
              defaultPO,
              supplierDetails,
            ]),
          }}
          watchfields={[
            'discountAmount',
            'deliveryFee',
            'withholdingTaxDeduction',
            'extraCharges',
            'terms',
            'tip',
          ]}
          onChange={values => {}}
        >
          {({ control, register, watch, setValue, formState }) => {
            const discountAmount = watch('discountAmount');
            const withholdingTaxDeduction = watch('withholdingTaxDeduction');
            const deliveryFee = watch('deliveryFee');
            const extraCharges = watch('extraCharges');
            const tip = watch('tip');
            const terms = watch('terms');

            const updateValue = (fieldName, object) => {
              let value = object;

              if (
                value?.length > 1 &&
                value.charAt(0) === '0' &&
                value !== '0.00'
              ) {
                value = value.slice(1);
              }

              object === ''
                ? setValue(fieldName, 0)
                : setValue(fieldName, value);
            };

            useEffect(() => {
              updateValue('discountAmount', discountAmount);
              updateValue('deliveryFee', deliveryFee);
              updateValue('extraCharges', extraCharges);
              updateValue('tip', tip);
              updateValue('withholdingTaxDeduction', withholdingTaxDeduction);

              if (terms !== '--') {
                setComputationData({
                  discountAmount: parseFloat(discountAmount) || 0,
                  deliveryFee: parseFloat(deliveryFee) || 0,
                  withholdingTaxDeduction:
                    parseFloat(withholdingTaxDeduction) || 0,
                  extraCharges: parseFloat(extraCharges) || 0,
                  terms: terms,
                  tip: parseFloat(tip) || 0,
                });
              }
            }, [
              discountAmount,
              deliveryFee,
              extraCharges,
              tip,
              withholdingTaxDeduction,
              terms,
            ]);

            //Close confirmation modal if error is form is found
            useEffect(() => {
              if (
                formState.isSubmitting &&
                modalData.isOpen &&
                !formState.isValid
              ) {
                closeModal();
              }
            }, [formState]);

            return (
              <>
                {/* Main Content : Purchase Order Details */}
                <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 mb-9">
                  <div className="flex-1 bg-white p-6 rounded-lg shadow-sm  mb-9">
                    <h2 className="text-xl font-bold mb-6">
                      Purchase Order Details
                    </h2>
                    <hr className="mb-7 my-2 col-span-2" />
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Select
                          searchable
                          label="Purchase Order"
                          control={control}
                          ref={poRef}
                          error={formState.errors['poId']}
                          handleChange={onSelectedPO}
                          {...register('poId')}
                          options={PurchaseOrderOptions} //ADD PO OPTIONS
                        />
                        {showMessageOnError(formState.errors['poId']?.message)}
                      </div>

                      <div></div>
                      <div className="grid grid-cols-12 gap-3">
                        <div className="col-span-9">
                          <Input
                            label="Supplier"
                            registration={{ ...control.register('supplier') }}
                            control={control}
                            disabled
                          />
                        </div>
                        <div className="col-span-3">
                          <Button
                            variant="outline"
                            type="button"
                            hover="outline"
                            className="mt-6 px-2 whitespace-nowrap text-sm h-10"
                            onClick={handleSupplierDetailsClick}
                          >
                            Supplier Details
                          </Button>
                        </div>
                      </div>
                      <Input
                        label="Delivery Address"
                        registration={{
                          ...control.register('deliveryAddress'),
                        }}
                        control={control}
                        disabled
                      />
                      <div>
                        <Input
                          label="Terms"
                          registration={{ ...control.register('terms') }}
                          control={control}
                          disabled
                        />
                        <div className="text-xs">{addTermsSpiel()}</div>
                      </div>
                      <div className={isCIA() ? '' : 'hidden'}>
                        <Select
                          {...register('employee')}
                          label="Employee"
                          placeholder="Select an Employee"
                          error={formState.errors['employee']}
                          control={control}
                          handleChange={onSelectedEmployee}
                          options={employeesOption}
                        />
                        {showMessageOnError(
                          formState.errors['employee']?.message,
                        )}
                      </div>

                      <div className={!isCIA() ? '' : 'hidden'}>
                        <div className="grid grid-cols-12 gap-3">
                          <div
                            className={
                              isRetention() ? 'col-span-6' : 'col-span-6 hidden'
                            }
                          >
                            <Input
                              label="Percent Retention (10%)"
                              value={retention}
                              control={control}
                              disabled
                            />
                          </div>

                          <div
                            className={
                              isRetention() ? 'col-span-6' : 'col-span-12'
                            }
                          >
                            <Input
                              label="Deposit %"
                              control={control}
                              value={deposit}
                              disabled
                            />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-12 gap-3">
                        <div
                          className={
                            showRemainingBalance()
                              ? 'col-span-12 hidden'
                              : 'col-span-6'
                          }
                        >
                          <Input
                            label="Remaining Balance"
                            value={remainingBalance}
                            control={control}
                            disabled
                          />
                        </div>
                        <div
                          className={
                            showRemainingBalance()
                              ? 'col-span-12'
                              : 'col-span-6'
                          }
                        >
                          <Input
                            label="Warranty"
                            registration={{ ...control.register('warranty') }}
                            control={control}
                            disabled
                          />
                        </div>
                      </div>

                      <div>
                        <div className="text-sm font-medium text-gray-600">
                          Invoice Details
                        </div>
                        <Button
                          variant="outline"
                          type="button"
                          hover="outline"
                          className="px-2 whitespace-nowrap text-sm h-10"
                          onClick={handleInvoiceDetailsClick}
                        >
                          View Invoice
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Main Content : Request Details */}
                <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 mb-10">
                  {/* Left Column - Form */}
                  <div className="flex-1 bg-white p-6 rounded-lg shadow-sm">
                    <h2 className="text-xl font-bold mb-6">Request Details</h2>
                    <hr className="mb-7 my-2 col-span-2" />

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Input
                          control={control}
                          {...register('payableDate')}
                          name="payableDate"
                          type="date"
                          disabled={!selectedPo ? true : null}
                          error={formState.errors['payableDate']}
                          label="Payable Date"
                          minDate={setDefaultDateValue(new Date())}
                        />
                        {showMessageOnError(
                          formState.errors['payableDate']?.message,
                        )}
                      </div>

                      <Input
                        label="Payable To"
                        registration={{ ...register('payableTo') }}
                        control={control}
                        disabled
                      />
                      <Input
                        label="Charge To"
                        registration={{ ...control.register('chargeTo') }}
                        control={control}
                        disabled
                      />
                      <div className="grid grid-cols-12 gap-3">
                        <div className="col-span-5">
                          <RadioButtonGroup
                            disabled={!selectedPo ? true : null}
                            name={`discountType`}
                            label="Discount (in price or percentage):"
                            options={discountTypeOptions}
                            defaultValue={!isDiscount ? 'fixed' : 'percent'}
                            onChange={val =>
                              handleInputChange('discountType', val)
                            }
                          />
                        </div>
                        <div className="col-span-7">
                          <Input
                            disabled={!selectedPo ? true : null}
                            type="number"
                            min="0"
                            step="any"
                            label={<span>&nbsp;</span>}
                            placeholder="Input Discount"
                            control={control}
                            onKeyPress={preventMinus}
                            registration={{
                              ...control.register('discountAmount'),
                            }}
                            //onChange={e=> setDiscountAmount(e.target.value)}
                            beforeIcon={!isDiscount ? PesoIcon : PercentIcon}
                          />
                        </div>
                      </div>
                    </div>

                    <hr className="my-2" />
                    <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 mb-5">
                      <div className="flex-1 bg-white p-6 rounded-lg shadow-sm">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div
                              className={
                                'text-sm font-medium text-gray-600 ' +
                                (!hasAdditionalFees ? '' : 'hidden')
                              }
                            >
                              Additional Fees
                            </div>
                            <Button
                              variant="outline"
                              type="button"
                              hover="outline"
                              className={
                                'px-2 whitespace-nowrap text-sm h-10 ' +
                                (!hasAdditionalFees ? '' : 'hidden')
                              }
                              onClick={() => {
                                setHasAdditionalFees(true);
                              }}
                            >
                              Include Additional Fees +
                            </Button>
                          </div>

                          <div>
                            <div className="grid grid-cols-12 gap-3">
                              <div className="col-span-9">
                                <div className="text-sm font-medium text-gray-600">
                                  &nbsp;
                                </div>
                              </div>
                              <div className="col-span-3">
                                <div className="text-sm font-medium text-gray-600">
                                  &nbsp;
                                </div>
                                <Button
                                  variant="outline"
                                  type="button"
                                  hover="outline"
                                  className={
                                    'px-2 whitespace-nowrap text-sm h-10 ' +
                                    (hasAdditionalFees ? '' : 'hidden')
                                  }
                                  onClick={() => {
                                    setValue('withholdingTaxDeduction', 0);
                                    setValue('extraCharges', 0);
                                    setValue('deliveryFee', 0);
                                    setValue('tip', 0);
                                    setHasAdditionalFees(false);
                                  }}
                                >
                                  Remove Fees
                                </Button>
                              </div>
                            </div>
                          </div>
                          <div className={hasAdditionalFees ? '' : 'hidden'}>
                            <Input
                              label="Withholding Tax Deduction"
                              disabled={!selectedPo ? true : null}
                              registration={{
                                ...register('withholdingTaxDeduction'),
                              }}
                              type="number"
                              step="any"
                              onKeyPress={preventMinus}
                              //onChange={e=> setWithholdingTax(e.target.value)}
                              min="0"
                              control={control}
                            />
                          </div>

                          <div className={hasAdditionalFees ? '' : 'hidden'}>
                            <Input
                              label="Delivery Fee"
                              disabled={!selectedPo ? true : null}
                              registration={{ ...register('deliveryFee') }}
                              control={control}
                              step="any"
                              //onChange={e=> setDeliveryFee(e.target.value)}
                              type="number"
                              onKeyPress={preventMinus}
                              min="0"
                            />
                          </div>

                          <div className={hasAdditionalFees ? '' : 'hidden'}>
                            <Input
                              label="Extra Charge"
                              disabled={!selectedPo ? true : null}
                              step="any"
                              registration={{
                                ...control.register('extraCharges'),
                              }}
                              control={control}
                              //onChange={e=> setExtraCharge(e.target.value)}
                              onKeyPress={preventMinus}
                              type="number"
                              min="0"
                            />
                          </div>

                          <div className={hasAdditionalFees ? '' : 'hidden'}>
                            <Input
                              label="Tip"
                              step="any"
                              disabled={!selectedPo ? true : null}
                              registration={{
                                ...control.register('tip'),
                              }}
                              //onChange={e=> setTip(e.target.value)}
                              control={control}
                              onKeyPress={preventMinus}
                              type="number"
                              min="0"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <hr className="my-6" />

                    <div className="bg-white p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-x-6 gap-y-4">
                        <div className="flex flex-col">
                          <File
                            isUploading={isUploading}
                            ref={fileRef}
                            control={control}
                            name="attachments"
                            attachments={requisition?.attachments?.data}
                            hasAttachments={setHasAttachments}
                          />

                          {isUploading && (
                            <div className="flex items-center">
                              <div className="text-sm mr-2 font-semibold">
                                {uploadProgress}%
                              </div>
                              <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
                                <div
                                  className="bg-blue-500 h-full transition-all duration-200 ease-out"
                                  style={{ width: `${uploadProgress}%` }}
                                />
                              </div>
                            </div>
                          )}
                          <div className="flex flex-col gap-2 bg-gray-50 mb-2 rounded-lg shadow-sm max-h-36 overflow-y-auto">
                            {attachmentsArr &&
                              attachmentsArr?.map(
                                ({ id, modelId, fileName }, index) => (
                                  <div key={modelId} className="flex w-full">
                                    <div className="w-full">
                                      <Input
                                        id={id}
                                        name={`${fileName}.${index}`}
                                        type="text"
                                        value={fileName}
                                        readOnly
                                        disabled
                                        className="w-full relative disabled:bg-gray-50 disabled:opacity-80 disabled:shadow-none text-black font-normal border-0"
                                        hasIcon={true}
                                        renderIcon={XIcon}
                                        iconHandler={() => {
                                          removeAttachment(id);
                                        }}
                                      />
                                    </div>
                                  </div>
                                ),
                              )}
                          </div>
                          <div className="flex justify-between align-middle">
                            <span className="text-[#4F4F4F] text-[12px]">
                              The maximum size for each file is 25 MB. File
                              formats - PNG, JPG, JPEG, PDF, Excel, CSV.
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col flex-grow">
                          <TextArea
                            label="Notes"
                            name="comment"
                            registration={{
                              ...control.register('comment'),
                            }}
                            control={control}
                            placeholder="Input notes here"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            );
          }}
        </Form>

        <div className="mt-6">
          <h2 className="text-xl font-bold">Items</h2>
          <div className="mt-4">
            {hasSteelbars ? (
              <SteelbarsTable
                tableData={
                  paymentRequestItems?.data ? paymentRequestItems?.data : []
                }
              />
            ) : (
              <PaymentRequestItemsTable
                onSearch={val => setSearchQuery(val)}
                //setPage={setPage}
                setSort={setSort}
                currentPage={currentPage}
                currentSort={currentSort}
                type="ofm"
                itemsData={
                  paymentRequestItems?.data ? paymentRequestItems?.data : []
                }
                otherCharges={{
                  withholdingTax: computationData.withholdingTaxDeduction,
                  extraCharges: computationData.extraCharges,
                  deliveryFee: computationData.deliveryFee,
                  tip: computationData.tip,
                }}
                discount={{
                  isFixed: isDiscount ? false : true,
                  value: parseFloat(computationData.discountAmount),
                }}
                handleTotalAmount={total => {
                  if (poDetails) {
                    handleComputation(total, computationData.terms);
                  }
                }}
              />
            )}
          </div>
        </div>

        <hr className="mt-5 mb-5" />
        <div className="flex justify-between items-center mt-10">
          <div className="flex gap-4 items-center text-sm">
            <Button
              className="w-full xl:w-fit mt-2 xl:mt-0"
              variant="outline"
              type="button"
              hover="outline"
              onClick={() =>
                setModalData({
                  isOpen: true,
                  type: 'draft',
                })
              }
            >
              Save Draft
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              className="w-full xl:w-fit mt-2 xl:mt-0"
              variant="outline"
              type="button"
              hover="outline"
              onClick={() => handleCancelClick()}
            >
              Cancel
            </Button>
            <Button
              className="w-full xl:w-fit mt-2 xl:mt-0"
              variant="submit"
              type="button"
              size="lg"
              onClick={() =>
                setModalData({
                  isOpen: true,
                  type: 'submit',
                })
              }
            >
              Submit
            </Button>
          </div>
        </div>

        <Modal
          size="small"
          header={
            modalData?.type === 'submit'
              ? 'Submit Payment Request'
              : 'Save Draft'
          }
          onClose={closeModal}
          isOpen={
            modalData?.isOpen &&
            (modalData?.type === 'submit' || modalData?.type === 'draft')
          }
        >
          <span className="my-4 text-sm">
            {modalData?.type === 'submit'
              ? 'You are about to submit this record. Make sure all items are correct. Press submit if you want to proceed with this action.'
              : 'You are about to save this request as a draft. Make sure all items are correct. Press save if you want to proceed with this action.'}
          </span>
          <div className="flex justify-center gap-2">
            <Button variant="outline" hover="outline" onClick={closeModal}>
              Cancel
            </Button>
            <Button
              variant="submit"
              type="submit"
              onClick={() => mainFormRef.current.requestSubmit()}
            >
              {modalData?.type === 'submit' ? 'Submit' : 'Draft'}
            </Button>
          </div>
        </Modal>
        {poDetails && modalData.type === 'supplier' && modalData.isOpen && (
          <SupplierDetailsModal
            isOpen={modalData.type === 'supplier' && modalData.isOpen}
            onClose={closeModal}
            supplierId={modalData?.supplier?.id}
            supplier={modalData.supplier}
            onCheckAttachment={onCheckAttachment}
            onCheckNote={onCheckNote}
          />
        )}

        <Modal
          size="small"
          header="Cancel Payment Request"
          onClose={closeModal}
          isOpen={modalData?.isOpen && modalData?.type === 'cancel'}
        >
          <span className="my-4 text-sm">
            You are about to cancel. All changes will not be saved. Press
            continue if you want to proceed with this action.
          </span>
          <div className="flex justify-center gap-2">
            <Button variant="submit" hover="outline" onClick={closeModal}>
              Cancel
            </Button>
            <Button
              variant="outline"
              hover="outline"
              onClick={() => {
                closeModal();
                navigate(`/app/requisition-slip/${rsID}`);
              }}
            >
              Continue
            </Button>
          </div>
        </Modal>

        <InvoiceDetails
          deliveryReciept={
            id
              ? paymentRequestData?.deliveryInvoice
              : poDetails?.result?.deliveryInvoice
          }
          deliveryInvoiceAttachment={poDetails?.result?.invoiceAttachment}
          isOpen={modalData.type === 'invoice' && modalData.isOpen}
          onClose={closeModal}
        />
      </div>
    </>
  );
};

PaymentRequestCreate.propTypes = {
  id: PropTypes.number,
  currentPage: PropTypes.number,
  currentLimit: PropTypes.number,
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  setSort: PropTypes.func,
  currentSort: PropTypes.array,
  sortBy: PropTypes.object,
};
