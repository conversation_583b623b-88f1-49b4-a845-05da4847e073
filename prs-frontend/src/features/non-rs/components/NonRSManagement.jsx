import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from 'react';
import {
  Form,
  Input,
  Select,
  TextArea,
  FileUpload,
  FieldWrapper,
  RadioTab,
  CustomSelect,
} from '@components/ui/Form';
import { Pill } from '@src/components/ui/Pill';

import { NonRSDetails } from './NonRSDetails';
import { Button } from '@components/ui/Button';
import { Spinner } from '@components/ui/Spinner';
import { NonRSFinancial } from './NonRSFinancial';
import { DiscountButton } from './DiscountButton';
import { SearchBar } from '@components/ui/SearchBar';
import { Table, Pagination } from '@components/ui/Table';
import { ConfirmModal, ActionModal } from '@components/ui/Modal';
import { SectionWrapper } from '@components/layouts/SectionWrapper';
import { NonRSPendingAction } from './NonRSPendingAction';
import { NonRSApproversSection } from './NonRSApproversSection';
import { StatusSection } from '@features/dashboard/components/StatusSection';
import { AttachmentAndNotesModal } from '@features/attachments/components';

import { useSticky } from '@hooks/useSticky';
import { useNotification } from '@hooks/useNotification';
import { useNavigate, useParams } from 'react-router-dom';

import z, { set } from 'zod';
import { cn } from '@utils/cn';
import {
  discountSchema,
  nonRSItemSchema,
  nonRSCreationSchema,
  itemListCreationSchema,
  draftItemListCreationSchema,
} from '@schema/non-rs.schema';
import {
  useCancelNonRS,
  useGetNonRSDetails,
  useGetNonRSItemList,
  useGetNonRSApprovers,
  useSubmitNonRequisitionSlip,
  useGetNonRsUnits,
} from '../api';
import { useGetNotes, useUpdateSeenNotes } from '@features/notes/api';
import {
  useGetAttachments,
  useDeleteAttachment,
  useUpdateSeenAttachments,
} from '@features/attachments/api';
import { useCreateNotes } from '@features/notes/api';
import { useAssignAttachments } from '@features/attachments/api';
import { usegetChargeToClientList } from '@features/dashboard/api';
import { useGetDepartments } from '@features/department/api';
import { useGetActiveSuppliers } from '@features/supplier/api';
import { useGetFormOptions } from '../api';
import {
  formatDateToDMY,
  formatDateToLocalTime,
  setDefaultDateValue,
} from '@utils/dateFormat';
import {
  getNextApprover,
  shouldShowComponentViaApprovers,
} from '@utils/objectValidator';
import { sortTableItems } from '@utils/query';
import { unitsConfig } from '@config/unitsConfig';

import { MODELS } from '@config/modelsConfig';
import { NOTES_TYPE, NOTES_USER_TYPES } from '@config/notesConfig';

import PesoIcon from '@assets/icons/peso.svg?react';
import FileIcon from '@assets/icons/file.svg?react';
import NailXIcon from '@assets/icons/nailx.svg?react';
import PercentIcon from '@assets/icons/percent.svg?react';
import CommentIcon from '@assets/icons/comment.svg?react';
import XIcon from '@assets/icons/xicon.svg?react';

import { transformOptions } from '@utils/itemListSelector';

import { useUserStore, useNonRSItemsStore } from '@store';
import { useDownloadTemplatePdf } from '@src/hooks/useDownloadTemplatePdf';
import { NumericalInput } from '@src/components/ui/Form/NumericalInput';

const MODAL_TYPES = {
  NONE: 'NONE',
  CONFIRM_SUBMIT: 'CONFIRM_SUBMIT',
  CANCEL_SUBMIT: 'CANCEL_SUBMIT',
  CANCEL_REQUEST: 'CANCEL_REQUEST',
  SAVE_DRAFT: 'SAVE_DRAFT',
  EDIT_DISCOUNT: 'EDIT_DISCOUNT',
  ATTACHMENT: 'ATTACHMENT',
  INVOICE_ATTACHMENT: 'INVOICE_ATTACHMENT',
  NOTES: 'NOTES',
  INVOICE_NOTES: 'INVOICE_NOTES',
  SUBMIT_NOTE: 'SUBMIT_NOTE',
  SUBMIT_INVOICE_NOTE: 'SUBMIT_INVOICE_NOTE',
  SUBMIT_ATTACHMENT: 'SUBMIT_ATTACHMENT',
  SUBMIT_INVOICE_ATTACHMENT: 'SUBMIT_INVOICE_ATTACHMENT',
  ATTACHMENTS: 'ATTACHMENTS',
  NOTES: 'NOTES',
};

const discountTypeOptions = [
  { key: 'Fixed Amount', value: 'fixed' },
  { key: 'Percentage', value: 'percent' },
];

const categoryOptions = [
  {
    key: 'Company',
    value: 'company',
  },
  {
    key: 'Association',
    value: 'association',
  },
  {
    key: 'Project',
    value: 'project',
  },
];

const chargeToCategories = [
  {
    key: 'Company',
    value: 'company',
  },
  {
    key: 'Supplier',
    value: 'supplier',
  },
  {
    key: 'Association',
    value: 'association',
  },
  {
    key: 'Project',
    value: 'project',
  },
].sort((a, b) => a.key.localeCompare(b.key));

const sortTypeConfig = {
  name: 'string',
  unit: 'string',
  quantity: 'number',
  amount: 'number',
  discountValue: 'number',
  discountedPrice: 'number',
};

const NonRSManagement = ({
  currentPage,
  currentLimit,
  setPage,
  setSort,
  sortBy,
  currentSort,
  permissions,
}) => {
  const { id } = useParams();
  const { user } = useUserStore();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const isCreateMode = !id;

  const {
    addItem,
    addItems,
    deleteItems,
    itemList,
    removeItem,
    setItemList,
    removeAllItems,
    updateItem,
    filterItemList,
    filteredItemList,
  } = useNonRSItemsStore();

  const noteRef = useRef(null);
  const invoiceNoteRef = useRef(null);
  const fileRef = useRef(null);
  const invoiceFileRef = useRef(null);
  const itemFormRef = useRef(null);
  const submitFormRef = useRef(null);
  const searchBarRef = useRef(null);
  const { isSticky, elementRef } = useSticky({
    offset: 300,
  });

  const [supplierInvoiceAmount, setSupplierInvoiceAmount] = useState(0);
  const [groupDiscount, setGroupDiscount] = useState({
    groupDiscountPrice: 0,
    groupDiscountType: 'fixed',
  });
  const [searchQuery, setSearchQuery] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [formDisabled, setFormDisabled] = useState(false);
  const [modalState, setModalState] = useState({
    type: MODAL_TYPES.NONE,
    data: null,
  });
  const [approvers, setApprovers] = useState([]);
  const [chargeToCategory, setChargeToCategory] = useState('');
  const [itemErrors, setItemErrors] = useState({});
  const [customUnits, setCustomUnits] = useState(unitsConfig.unitsOptions);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedCompany, setSelectedCompany] = useState('');
  const [selectedProject, setSelectedProject] = useState('');
  const [isUploadingInvoiceAttachments, setIsUploadingInvoiceAttachments] =
    useState(false);

  const searchItems = [
    {
      type: 'text',
      label: 'Search:',
      name: 'itemName',
      placeholder: 'Search Items',
      isParent: true,
    },
  ];

  const { mutateAsync: cancelNonRS, isPending: isCancellingRequest } =
    useCancelNonRS();

  const { mutateAsync: submitNonRSSlip, isPending: isSubmittingRequest } =
    useSubmitNonRequisitionSlip();

  const { mutateAsync: deleteAttachment } = useDeleteAttachment();

  const { mutate: downloadPdf, isPending: isPdfDownloading } =
    useDownloadTemplatePdf();

  const { data: nonRSDetails, isFetching: isFetchingNonRSDetails } =
    useGetNonRSDetails(id);

  const { data: nonRSItemList, isFetching: isFetchingNonRSItemList } =
    useGetNonRSItemList(id);

  const { data: chargeToList, isFetching: isFetchingChargeToList } =
    usegetChargeToClientList(
      {
        category: nonRSDetails?.chargeTo || chargeToCategory,
      },
      {
        select: res => {
          return res.result?.data?.map(list => ({
            key: list?.name,
            value: list?.id,
          }));
        },
      },
    );

  const { data: formOptions } = useGetFormOptions({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });

  const canDownload =
    user?.id === nonRSDetails?.createdBy ||
    user?.role?.name.includes('Purchasing');

  const filteredCompany = formOptions?.companies.filter(
    company => company.category === selectedCategory,
  );
  const companyDataOptions =
    selectedCategory !== 'project' ? filteredCompany : formOptions?.companies;

  const project = formOptions?.projects.find(p => p.id === selectedProject);
  const companyResult = formOptions?.companies.find(
    company => company.id === project?.company.id,
  );

  const companyOptions = selectedProject
    ? companyResult && [
      {
        key: companyResult?.displayName,
        value: companyResult?.id,
      },
    ]
    : companyDataOptions &&
    companyDataOptions.map(company => ({
      key: company?.displayName,
      value: company?.id,
    }));

  const filteredProjects = formOptions?.projects.filter(
    project => project.company?.id === selectedCompany,
  );
  const projectOptions = selectedCompany
    ? filteredProjects &&
    filteredProjects?.map(project => ({
      key: project?.name,
      value: project?.id,
    }))
    : formOptions?.projects &&
    formOptions?.projects?.map(project => ({
      key: project?.name,
      value: project?.id,
    }));

  const { data: departments } = useGetDepartments({
    paginate: false,
    sortBy: { name: 'ASC' },
  });

  const departmentOptions =
    departments &&
    departments?.data?.map(department => ({
      key: department?.name,
      value: department?.id,
    }));

  const { data: suppliers } = useGetActiveSuppliers({
    paginate: false,
    sortBy: { name: 'ASC' },
  });

  const supplierOptions =
    suppliers &&
    suppliers?.data?.map(supplier => ({
      key: supplier?.name,
      value: supplier?.id,
    }));

  const { data: nonrsUnits, isFetching: isFetchingNonrsUnits } =
    useGetNonRsUnits();

  useEffect(() => {
    if (nonrsUnits?.data?.data && !isFetchingNonrsUnits) {
      const dynamicUnitNames = nonrsUnits.data.data.map(item => item.unit);

      const dynamicUnitOptions = dynamicUnitNames.map(unitName => ({
        key: unitName,
        value: unitName,
      }));

      const uniqueSortedUnits = transformOptions(
        unitsConfig.unitsOptions,
        dynamicUnitOptions,
      );

      setCustomUnits(uniqueSortedUnits);
    }
  }, [nonrsUnits?.data?.data, isFetchingNonrsUnits]);

  const handleAddNewUnit = newUnit => {
    setCustomUnits(prev => {
      const newUnits = [...prev, newUnit];
      return newUnits.sort((a, b) => a.key.localeCompare(b.key));
    });
  };

  const { data: nonRSApprovers, isFetching: isFetchingNonRSApprovers } =
    useGetNonRSApprovers(id);

  const { mutateAsync: uploadAttachments, isPending: isUploadingAttachments } =
    useAssignAttachments();

  const { mutateAsync: createNotes, isPending: isCreatingNote } =
    useCreateNotes();

  const { data: attachmentsData } = useGetAttachments(
    {
      model: MODELS.NON_RS,
      modelId: id,
    },
    {
      enabled: !!id,
    },
  );

  const { data: invoiceAttachmentsData } = useGetAttachments(
    {
      model: MODELS.NON_RS_INVOICE,
      modelId: id,
    },
    {
      enabled: !!id,
    },
  );

  const { data: invoiceNotesData } = useGetNotes(
    {
      model: MODELS.NON_RS_INVOICE,
      modelId: id,
      dateFrom: '',
      dateTo: '',
    },
    {
      enabled: !!id,
    },
  );

  const { data: notesData, refetch: refetchNotes } = useGetNotes(
    {
      model: MODELS.NON_RS,
      modelId: id,
      dateFrom: '',
      dateTo: '',
    },
    {
      enabled: !!id,
    },
  );

  const timeoutRef = useRef(null);

  const debouncedUpdateItem = useCallback(
    (item, field, value) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        updateItem({
          ...item,
          [field]: value,
        });
      }, 200);
    },
    [updateItem],
  );

  useEffect(() => {
    if (!isFetchingNonRSApprovers) {
      setApprovers(nonRSApprovers?.data);
    }
  }, [isFetchingNonRSApprovers]);

  useEffect(() => {
    if (!isFetchingNonRSItemList && !isCreateMode) {
      setItemList(nonRSItemList?.data);
    } else {
      removeAllItems();
    }
  }, [isFetchingNonRSItemList]);

  const openModal = (type, data = null) => {
    setModalState({ type, data });
  };

  const closeModal = () => {
    setModalState({ type: MODAL_TYPES.NONE, data: null });
  };

  const validateItems = useCallback(
    asDraft => {
      try {
        let parsedItemList;
        if (asDraft) {
          parsedItemList = draftItemListCreationSchema.parse({ itemList });
        } else {
          parsedItemList = itemListCreationSchema.parse({ itemList });
        }

        setItemErrors({});
        return { isValid: true, parsedItemList: parsedItemList };
      } catch (error) {
        const newErrors = {};
        if (error instanceof z.ZodError) {
          error.errors.forEach(err => {
            if (err.path.length === 1 && err.path[0] === 'itemList') {
              newErrors['itemList'] = err.message;
            } else if (err.path[0] === 'itemList') {
              const itemIndex = err.path[1];
              const field = err.path[2];
              const itemId =
                itemList[itemIndex]?.id || itemList[itemIndex]?.insertedId;
              if (itemId) {
                newErrors[`${itemId}.${field}`] = err.message;
              }
            }
          });

          setItemErrors(newErrors);
        }
        return { isValid: false, newErrors };
      }
    },
    [JSON.stringify(itemList)],
  );

  const handleSubmitAttachments = async () => {
    const fileReference = fileRef.current;
    const attachmentIds = fileReference?.attachmentIds;

    await uploadAttachments(
      { attachmentIds, model: MODELS.NON_RS, modelId: parseInt(id) },
      {
        onSuccess: message => {
          showNotification({
            type: 'success',
            message: message?.message || 'Attachments uploaded successfully',
          });
          fileReference.removeAllFiles();
          closeModal();
        },
        onError: error => {
          const errorMessage = error?.response?.data?.message || error?.message;
          showNotification({
            type: 'error',
            message: errorMessage,
          });
        },
      },
    );
  };

  const handleSubmitInvoiceAttachments = async () => {
    const fileReference = invoiceFileRef.current;
    const attachmentIds = fileReference?.attachmentIds;

    await uploadAttachments(
      { attachmentIds, model: MODELS.NON_RS_INVOICE, modelId: parseInt(id) },
      {
        onSuccess: message => {
          showNotification({
            type: 'success',
            message:
              message?.message || 'Invoice attachments uploaded successfully',
          });
          fileReference.removeAllFiles();
          closeModal();
        },
        onError: error => {
          const errorMessage = error?.response?.data?.message || error?.message;
          showNotification({
            type: 'error',
            message: errorMessage,
          });
        },
      },
    );
  };

  const handleCreateNotes = async ({ isOneOfApprovers }) => {
    const notesReference = noteRef.current;
    const notesValue = notesReference?.value;

    const userType = isOneOfApprovers
      ? NOTES_USER_TYPES.APPROVER
      : NOTES_USER_TYPES.REQUESTOR;

    const payload = {
      model: MODELS.NON_RS,
      modelId: id,
      note: notesValue,
      userType,
      commentType: NOTES_TYPE.NOTE,
    };

    await createNotes(payload, {
      onSuccess: () => {
        showNotification({
          type: 'success',
          message: 'Notes added successfully',
        });
        closeModal();
        notesReference?.clear();
      },
      onError: error => {
        showNotification({
          type: 'error',
          message:
            error?.response?.data?.message ||
            error?.message ||
            'An error occurred while adding a note.',
        });
      },
    });
  };

  const handleCreateInvoiceNotes = async ({ isOneOfApprovers }) => {
    const notesReference = invoiceNoteRef.current;
    const notesValue = notesReference?.value;

    const userType = isOneOfApprovers
      ? NOTES_USER_TYPES.APPROVER
      : NOTES_USER_TYPES.REQUESTOR;

    const payload = {
      model: MODELS.NON_RS_INVOICE,
      modelId: id,
      note: notesValue,
      userType,
      commentType: NOTES_TYPE.NOTE,
    };

    await createNotes(payload, {
      onSuccess: () => {
        showNotification({
          type: 'success',
          message: 'Invoice notes added successfully',
        });
        closeModal();
        notesReference?.clear();
      },
      onError: error => {
        showNotification({
          type: 'error',
          message:
            error?.response?.data?.message ||
            error?.message ||
            'An error occurred while adding an invoice note.',
        });
      },
    });
  };

  const handleCancelNonRS = async () => {
    try {
      await cancelNonRS(
        { id },
        {
          onSuccess: async response => {
            showNotification({
              type: 'success',
              message: response?.message || 'Request cancelled successfully',
            });
            closeModal();
            navigate('/app/non-requisition-slip/dashboard', {
              replace: true,
            });
          },
        },
      );
    } catch (error) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Failed to cancel request slip';

      showNotification({
        type: 'error',
        message: errorMessage,
      });
    }
  };

  const submitForm = async values => {
    const { asDraft } = values;
    try {
      const {
        isValid,
        newErrors = {},
        parsedItemList,
      } = validateItems(asDraft);

      if (!isValid) {
        if (newErrors.itemList) {
          return showNotification({
            type: 'error',
            message: newErrors.itemList,
          });
        }

        const fieldError = Object.values(newErrors)[0];
        return showNotification({
          type: 'error',
          message: fieldError,
        });
      }

      const noteReference = noteRef?.current;
      const noteValue = noteReference?.value?.trim();
      const fileReference = fileRef?.current;
      const invoiceNoteReference = invoiceNoteRef?.current;
      const invoiceFileReference = invoiceFileRef?.current;

      const {
        projectId,
        groupDiscountType,
        groupDiscountPrice,
        ...restValues
      } = values;
      const convertedValues = {
        ...restValues,
        supplierId: values.supplierId?.toString() || null,
        companyId: values.companyId?.toString() || null,
        departmentId: values.departmentId?.toString() || null,
      };

      if (projectId) {
        convertedValues.projectId = values.projectId?.toString();
      }
      if (groupDiscountType) {
        convertedValues.groupDiscountType = values.groupDiscountType;
      }
      if (groupDiscountPrice) {
        convertedValues.groupDiscountPrice = values.groupDiscountPrice;
      }

      let dataForParsing = {
        ...(!isCreateMode ? nonRSDetails : {}),
        ...JSON.parse(JSON.stringify(convertedValues)),
        attachmentIds: fileReference?.attachmentIds,
        invoiceAttachmentIds: invoiceFileReference?.attachmentIds,
        ...(noteValue && { notes: noteValue }),
      };

      if (values.groupDiscountType === null) {
        delete dataForParsing.groupDiscountType;
      }
      if (values.groupDiscountPrice === 0) {
        delete dataForParsing.groupDiscountPrice;
      }

      const parsedNonRSDetails = nonRSCreationSchema.parse(dataForParsing);

      const payload = {
        ...parsedNonRSDetails,
        isDraft: asDraft,
        itemList: parsedItemList.itemList,
      };

      await submitNonRSSlip(payload, {
        onSuccess: async response => {
          showNotification({
            type: 'success',
            message: asDraft
              ? 'Draft saved successfully'
              : 'Request submitted successfully',
          });
          fileReference?.removeAllFiles();
          invoiceFileReference?.removeAllFiles();
          noteReference?.clear();
          invoiceNoteReference?.clear();
          closeModal();
          setPage(1);

          if (isCreateMode && response.id) {
            navigate(`/app/non-requisition-slip/${response.id}`, {
              replace: true,
            });
          }
        },
      });
    } catch (error) {
      let errorMessage =
        error?.response?.data?.message || 'Failed to submit request';

      if (error instanceof z.ZodError) {
        const parsedError = JSON.parse(JSON.stringify(error));
        errorMessage = parsedError.issues?.[0].message;
      }

      showNotification({
        type: 'error',
        message: errorMessage,
      });
    }
  };

  const handleAddItems = values => {
    try {
      const parsedValues = nonRSItemSchema.parse(values);
      addItem(parsedValues);
      itemFormRef?.current?.resetForm();
      unitSelectRef?.current?.clearInput();
      searchBarRef?.current?.clear();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const parsedError = JSON.parse(JSON.stringify(error));
        const errorMessage = parsedError.issues?.[0].message;
        showNotification({
          type: 'error',
          message: errorMessage,
        });
      }
    }
  };

  const handleRemoveItem = item => {
    removeItem(item);
    filterItemList(searchQuery);

    const currentItemsLength = itemsToShow.length - 1;
    const currentMaxPage = Math.ceil(currentItemsLength / currentLimit);

    if (
      currentPage >= currentMaxPage &&
      currentPage !== currentMaxPage &&
      currentMaxPage !== 0
    ) {
      setPage(currentMaxPage);
    }
  };

  const handleUpdateDiscount = item => {
    try {
      discountSchema.parse(item);

      const { data } = modalState;
      updateItem({ ...data, ...item });
      closeModal();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const parsedError = JSON.parse(JSON.stringify(error));
        const errorMessage = parsedError.issues?.[0].message;
        showNotification({
          type: 'error',
          message: errorMessage,
        });
      }
    }
  };

  const deleteApproverOnList = approverId => {
    setApprovers(prevApprovers => {
      const updatedApprovers = prevApprovers.filter(
        approver => approver.userId !== approverId,
      );

      return [...updatedApprovers];
    });
  };

  const addApproverOnList = newApprover => {
    const { key, value } = newApprover;

    setApprovers(prevApprovers => {
      const currentUserIndex = prevApprovers.findIndex(
        approver => approver.userId === user.id,
      );

      if (currentUserIndex === -1) {
        return prevApprovers;
      }

      const currentUser = prevApprovers[currentUserIndex];
      const currentUserLevel = currentUser.level;

      const existingAdhocIndex = prevApprovers.findIndex(
        approver => approver.isAdhoc && approver.level === currentUserLevel,
      );

      if (existingAdhocIndex !== -1) {
        const updatedApprovers = [...prevApprovers];
        updatedApprovers[existingAdhocIndex] = {
          ...updatedApprovers[existingAdhocIndex],
          isNew: true,
          userId: value,
          approver: {
            id: value,
            firstName: key,
          },
        };
        return updatedApprovers;
      }

      const newApproverData = {
        level: currentUserLevel,
        isAdhoc: true,
        status: 'pending',
        isNew: true,
        userId: value,
        approver: {
          id: value,
          firstName: key,
        },
      };

      const updatedApprovers = [...prevApprovers];
      updatedApprovers.splice(currentUserIndex + 1, 0, newApproverData);

      return updatedApprovers;
    });
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const userId = user.id;
  const userDepartment = user?.department?.id;
  const draftSavedDate = nonRSDetails?.updatedAt;

  const isRequestDraft = nonRSDetails?.status === 'draft';
  const isRequestRejected = nonRSDetails?.status === 'rejected';
  const isRequestCancelled = nonRSDetails?.status === 'cancelled';
  const isRequestForApproval = nonRSDetails?.status === 'for_approval';
  const isRequestClosed = nonRSDetails?.status === 'closed';
  const isStatusSubmitted = !isRequestDraft && !isCreateMode;
  const isCreateModeOrDraft = isRequestDraft || isCreateMode;

  const isCreator = user.id === nonRSDetails?.createdBy;
  const isOneOfApprovers = nonRSApprovers?.data?.some(
    approver => approver.userId === userId,
  );
  const isAllApproversForPending = nonRSApprovers?.data.every(
    approver => approver.status === 'pending',
  );

  const isDraftAndIsCreator = isRequestDraft && isCreator;
  const userCanSubmit =
    (((!isRequestForApproval && !isRequestClosed && !isRequestCancelled) ||
      isRequestRejected) &&
      isCreator) ||
    isCreateMode;

  const shouldShowApproverModal = shouldShowComponentViaApprovers(
    approvers,
    userId,
  );
  const nextApproverData = getNextApprover(approvers, userId);

  const approverCanEdit = isRequestForApproval && !!nextApproverData;

  const renderHeaders = () => {
    return [
      {
        key: 'name',
        value: 'Item',
        css: 'w-[50%] min-w-[150px] max-w-[550px]',
      },
      { key: 'unit', value: 'Unit', css: 'w-[10%] min-w-[50px] max-w-[100px]' },
      { key: 'quantity', value: 'Quantity' },
      { key: 'amount', value: 'Amount' },
      {
        key: 'discountValue',
        value: 'Discount',
        css: 'w-[10%] min-w-[50px] max-w-[100px]',
      },
      { key: 'discountedPrice', value: 'Discounted Price' },
      ...(userCanSubmit
        ? [{ key: 'actions', value: 'Actions', hasNoSort: true }]
        : []),
    ];
  };

  const tdDesign = {
    name: {
      css: 'px-2',
      render: item => (
        <Input
          name="name"
          placeholder="Input Item Name"
          className="mx-auto"
          defaultValue={item.name}
          onChange={e => debouncedUpdateItem(item, 'name', e.target.value)}
          error={{
            message:
              itemErrors[`${item.id}.name`] ||
              itemErrors[`${item.insertedId}.name`],
          }}
          key={`${item.id || item.insertedId}.name`}
          disabled={!userCanSubmit}
        />
      ),
    },
    unit: {
      css: 'px-2',
      render: item => (
        <div className="relative">
          <CustomSelect
            name="unit"
            options={customUnits}
            defaultValue={item.unit}
            onAddNewOption={handleAddNewUnit}
            placeholder="Select or type unit..."
            key={`${item.id || item.insertedId}.unit.${item.unit}`}
            disabled={!userCanSubmit}
          />
        </div>
      ),
    },
    quantity: {
      css: 'px-2',
      render: item => (
        <NumericalInput
          label=""
          decimalPlaces={3}
          maxLimit={12}
          inputProps={{
            min: 0.00,
            max: 99999.999,
            disabled: !!nextApproverData ? !approverCanEdit : !userCanSubmit
          }}
          name="quantity"
          placeholder="Input Quantity"
          className="max-w-48 mx-auto"
          value={item.quantity ?? ''}
          onChange={e => {
            const value = e.target.value;
            const formattedValue = value ? Number(parseFloat(value)) : null;
            debouncedUpdateItem(item, 'quantity', formattedValue);
          }}
          error={{
            message:
              itemErrors[`${item.id}.quantity`] ||
              itemErrors[`${item.insertedId}.quantity`],
          }}
          disabled={!!nextApproverData ? !approverCanEdit : !userCanSubmit}
          key={`${item.id || item.insertedId}.quantity`}
        />
      ),
    },
    amount: {
      css: 'px-2',
      render: item => (
        <NumericalInput
          label=""
          decimalPlaces={2}
          maxLimit={12}
          inputProps={{
            min: 0.00,
            max: 9999999999.99,
            disabled: !userCanSubmit
          }}
          name="amount"
          placeholder="Input Amount"
          className="max-w-48 mx-auto"
          value={item.amount ?? ''}
          onChange={e => {
            const value = e.target.value;
            const formattedValue = value ? Number(parseFloat(value).toFixed(2)) : null;
            debouncedUpdateItem(item, 'amount', formattedValue);
          }}
          error={{
            message:
              itemErrors[`${item.id}.amount`] ||
              itemErrors[`${item.insertedId}.amount`],
          }}
          disabled={!userCanSubmit}
          key={`${item.id || item.insertedId}.amount`}
        />
      ),
    },
    discountValue: {
      css: 'px-2 text-center',
      render: item => (
        <DiscountButton
          discountValue={item.discountValue.toFixed(2)}
          discountType={item.discountType}
          onClick={() => openModal(MODAL_TYPES.EDIT_DISCOUNT, item)}
          disabled={!!nextApproverData ? !approverCanEdit : !userCanSubmit}
          key={`${item.id || item.insertedId}.discountValue.discountType`}
        />
      ),
    },
    discountedPrice: {
      css: 'px-2 w-fit',
      render: item => (
        <div className="inline-flex justify-between">
          <Input
            type="number"
            name="discountedPrice"
            min={0}
            disabled={true}
            className="max-w-48"
            beforeIcon={PesoIcon}
            defaultValue={item.discountedPrice.toFixed(2)}
            key={`${item.id}${item.discountedPrice}`}
          />
        </div>
      ),
    },
    actions: {
      render: item => (
        <Button
          variant="icon"
          hover="submit"
          icon={NailXIcon}
          iconSize="ml-0"
          className="bg-red-500 hover:bg-red-400"
          onClick={() => handleRemoveItem(item)}
          title="Delete Item"
          disabled={!userCanSubmit}
        />
      ),
    },
  };

  const displayItems = useMemo(() => {
    return searchQuery?.trim() ? filteredItemList : itemList;
  }, [addItems, deleteItems, filteredItemList, itemList, searchQuery]);

  const [lastSortedItems, setLastSortedItems] = useState([]);

  useEffect(() => {
    if (!currentSort?.length) {
      setLastSortedItems([]);
      return;
    }
    const sorted = sortTableItems(displayItems, currentSort, sortTypeConfig);
    setLastSortedItems(sorted);
  }, [currentSort, searchQuery, addItems, deleteItems]);

  const itemsToShow = useMemo(() => {
    return lastSortedItems.length ? lastSortedItems : displayItems;
  }, [lastSortedItems, displayItems]);

  const handleSearch = (val = {}) => {
    const { itemName } = val;
    filterItemList(itemName);
    setSearchQuery(itemName);
  };

  const unitSelectRef = useRef(null);

  useEffect(() => {
    if (nonRSDetails?.companyId && nonRSDetails?.projectId) {
      setSelectedCompany(nonRSDetails.companyId);
      setSelectedProject(nonRSDetails.projectId);
    }
  }, [nonRSDetails]);

  if (isFetchingNonRSDetails) {
    return <Spinner className="mx-auto" />;
  }

  const isRequestor = user?.id === nonRSDetails?.createdBy ? true : false;

  const removeAttachment = async attachment => {
    try {
      await deleteAttachment({ id: attachment });
    } catch (error) {
      throw error;
    }
  };

  const handleDownload = () => {
    downloadPdf({
      type: 'non-requisitions',
      id: id,
    });
  };

  return (
    <React.Fragment>
      {isCreateModeOrDraft && (
        <div className="xl:absolute xl:top-8 xl:right-8 text-sm">
          <div className="flex flex-wrap flex-col xl:flex-row xl:items-center gap-2 xl:space-x-2">
            {isRequestDraft && !isCreateMode && (
              <React.Fragment>
                <div className="flex items-center space-x-1">
                  <span className="font-bold text-[#219653]">Draft Saved:</span>
                  <span>
                    {draftSavedDate ? formatDateToDMY(draftSavedDate) : 'N/A'}
                  </span>
                  <div className="hidden xl:block">|</div>
                  <span>
                    {draftSavedDate
                      ? formatDateToLocalTime(draftSavedDate)
                      : 'N/A'}
                  </span>
                </div>
              </React.Fragment>
            )}
            {(isCreateMode || isDraftAndIsCreator) && (
              <Button
                variant="outline"
                hover="outline"
                type="button"
                className="w-full sm:w-auto min-w-[6rem]"
                onClick={() => openModal(MODAL_TYPES.SAVE_DRAFT)}
              // disabled={canSubmitCanvass}
              >
                Save Draft
              </Button>
            )}
          </div>
        </div>
      )}

      <div className="flex flex-col space-y-4">
        {isOneOfApprovers && nextApproverData !== null && (
          <NonRSPendingAction
            refetch={refetchNotes}
            shouldShowApproverModal={shouldShowApproverModal}
            approvers={approvers}
            onAddApprover={addApproverOnList}
            validateItems={validateItems}
          />
        )}

        <NonRSDetails
          nonRSDetails={nonRSDetails}
          isCreateMode={isCreateMode}
          isLoading={isPdfDownloading}
          handleDownload={handleDownload}
          canDownload={canDownload}
        />

        <div className="flex flex-col gap-y-4">
          <div
            className={cn({
              'grid grid-cols-1 xl:grid-cols-6 gap-4': isStatusSubmitted,
            })}
          >
            <div className="xl:col-span-5">
              <Form
                hasErrorSpace={false}
                onSubmit={submitForm}
                options={{
                  shouldUnregister: true,
                  defaultValues: {
                    category: nonRSDetails?.category,
                    companyId: nonRSDetails?.companyId,
                    projectId: nonRSDetails?.projectId,
                    departmentId:
                      nonRSDetails?.departmentId || userDepartment || null,
                    supplierId: nonRSDetails?.supplierId,
                    payableTo: nonRSDetails?.payableTo,
                    invoiceNo: nonRSDetails?.invoiceNo,
                    invoiceDate: setDefaultDateValue(nonRSDetails?.invoiceDate),
                    supplierInvoiceAmount: nonRSDetails?.supplierInvoiceAmount,
                    groupDiscountType: nonRSDetails?.groupDiscountType,
                    groupDiscountPrice: nonRSDetails?.groupDiscountPrice,
                    chargeTo: nonRSDetails?.chargeTo,
                    chargeToId: nonRSDetails?.chargeToId,
                  },
                }}
                ref={submitFormRef}
              >
                {({ control, register, setValue, watch, disabled }) => {
                  const groupDiscountType = watch('groupDiscountType');
                  const groupDiscountPrice = watch('groupDiscountPrice');

                  useEffect(() => {
                    setFormDisabled(disabled);
                  }, [disabled]);
                  useEffect(() => {
                    if (nonRSDetails && formOptions) {
                      const { companyId, projectId, category } = nonRSDetails;

                      const categoryToSet = category || '';
                      setSelectedCategory(categoryToSet);

                      // Set Company and Project based on details
                      const companyToSet = companyId || null;
                      const projectToSet = projectId || null;

                      setSelectedCategory(prev =>
                        prev !== categoryToSet ? categoryToSet : prev,
                      );
                      setSelectedCompany(prev =>
                        prev !== companyToSet ? companyToSet : prev,
                      );
                      setSelectedProject(prev =>
                        prev !== projectToSet ? projectToSet : prev,
                      );
                    }
                  }, [nonRSDetails]);

                  useEffect(() => {
                    // Run only when formOptions are loaded and local states are potentially set
                    if (formOptions && nonRSDetails) {
                      const companyToSet = nonRSDetails.companyId || null;
                      const projectToSet = nonRSDetails.projectId || null;
                      const categoryToSet = nonRSDetails.category || '';
                      // Check current RHF values
                      const currentCompanyId = watch('companyId');
                      const currentProjectId = watch('projectId');
                      const currentCategory = watch('category');
                      // Update RHF only if needed and local state matches target
                      if (
                        selectedCategory === categoryToSet &&
                        currentCategory !== categoryToSet
                      ) {
                        setValue('category', categoryToSet, {
                          shouldDirty: false,
                          shouldValidate: false,
                        });
                      }
                      if (
                        selectedCompany === companyToSet &&
                        currentCompanyId !== companyToSet
                      ) {
                        setValue('companyId', companyToSet, {
                          shouldDirty: false,
                          shouldValidate: false,
                        });
                      }
                      if (
                        selectedProject === projectToSet &&
                        currentProjectId !== projectToSet
                      ) {
                        setValue('projectId', projectToSet, {
                          shouldDirty: false,
                          shouldValidate: false,
                        });
                      }
                    }
                  }, [
                    formOptions,
                    nonRSDetails,
                    selectedCategory,
                    selectedCompany,
                    selectedProject,
                    setValue,
                    watch,
                  ]);

                  const chargeTo = watch('chargeTo');
                  const companyId = watch('companyId');
                  const projectId = watch('projectId');
                  const supplierId = watch('supplierId');

                  // To auto-populate Charge To (Client)
                  useEffect(() => {
                    let clientValueToSet = null;

                    if (chargeTo === 'company' || chargeTo === 'association') {
                      clientValueToSet = companyId || null;
                    } else if (chargeTo === 'project') {
                      clientValueToSet = projectId || null;
                    } else if (chargeTo === 'supplier') {
                      clientValueToSet = supplierId || null;
                    }

                    if (watch('chargeToId') !== clientValueToSet) {
                      setValue('chargeToId', clientValueToSet, {
                        shouldDirty: true,
                      });
                    }
                  }, [
                    chargeTo,
                    companyId,
                    projectId,
                    supplierId,
                    setValue,
                    watch,
                  ]);

                  useEffect(() => {
                    const subscription = watch(values =>
                      setGroupDiscount({
                        groupDiscountPrice: values?.groupDiscountPrice,
                        groupDiscountType: values?.groupDiscountType,
                      }),
                    );

                    return () => subscription.unsubscribe();
                  }, [watch, groupDiscountType, groupDiscountPrice]);

                  return (
                    <div className="flex flex-col space-y-4">
                      <SectionWrapper header="Request Details">
                        <div className="flex flex-col space-y-6">
                          <div className="grid grid-cols-2 gap-4 flex-1 flex-grow">
                            <Select
                              label="Category"
                              name="category"
                              control={control}
                              {...register('category')}
                              options={categoryOptions}
                              onChange={value => {
                                setSelectedCategory(value);
                              }}
                              searchable
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />
                            <Select
                              searchable
                              clearable
                              label="Company"
                              name="companyId"
                              control={control}
                              {...register('companyId')}
                              options={companyOptions}
                              onChange={value => {
                                setSelectedCompany(value);
                                if (!value) {
                                  setSelectedProject(null);
                                  setValue('projectId', null);
                                }
                              }}
                              disabled={
                                isFetchingNonRSDetails ||
                                !userCanSubmit ||
                                !companyOptions?.length
                              }
                            />
                            <Select
                              searchable
                              clearable
                              label="Project"
                              name="projectId"
                              control={control}
                              {...register('projectId')}
                              options={projectOptions}
                              onChange={value => {
                                setSelectedProject(value);
                                if (!value) {
                                  setSelectedCompany(null);
                                  setValue('companyId', null, {
                                    shouldDirty: true,
                                  });
                                } else {
                                  const project = formOptions?.projects.find(
                                    p => p.id === value,
                                  );
                                  if (project?.company?.id) {
                                    setSelectedCompany(project.company.id);
                                    setValue('companyId', project.company.id, {
                                      shouldDirty: true,
                                    });
                                  }
                                }
                              }}
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />
                            <Select
                              searchable
                              label="Department"
                              name="departmentId"
                              control={control}
                              {...register('departmentId')}
                              options={departmentOptions}
                              defaultValue={userDepartment}
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />
                          </div>
                        </div>
                        <hr className="my-6" />
                        <div className="flex flex-col space-y-4">
                          <div className="grid grid-cols-2 gap-4 flex-1 flex-grow">
                            <Select
                              searchable
                              label="Supplier"
                              name="supplierId"
                              control={control}
                              {...register('supplierId')}
                              options={supplierOptions}
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />

                            <Input
                              label="Payable To"
                              name="payableTo"
                              placeholder="Input Payable To"
                              {...register('payableTo')}
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />

                            <Input
                              label="Invoice No."
                              name="invoiceNo"
                              placeholder="Input Invoice Number"
                              {...register('invoiceNo')}
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />

                            <Input
                              type="date"
                              label="Supplier Invoice Date"
                              name="invoiceDate"
                              placeholder="Select date"
                              maxDate={setDefaultDateValue(new Date())}
                              {...register('invoiceDate')}
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />
                            <NumericalInput
                              label="Supplier Invoice Amount"
                              decimalPlaces={2}
                              maxLimit={6}
                              inputProps={{
                                min: 0.000,
                                max: 9999999999.99,
                                disabled: isFetchingNonRSDetails || !userCanSubmit
                              }}
                              registration={register('supplierInvoiceAmount', {
                                setValueAs: value => {
                                  return value
                                    ? Number(parseFloat(value))
                                    : null;
                                },
                              })}
                              name="supplierInvoiceAmount"
                              placeholder="Input Supplier Invoice Amount"
                              beforeIcon={PesoIcon}
                              value={watch('supplierInvoiceAmount') ?? ''}
                              onChange={e => {
                                const value = e.target.value;
                                setValue('supplierInvoiceAmount', value);
                                if (value) {
                                  setSupplierInvoiceAmount(Number(parseFloat(value)));
                                } else {
                                  setSupplierInvoiceAmount(null);
                                }
                              }}
                            />

                            <div className="flex flex-col lg:flex-row items-end gap-2">
                              <div className="flex-shrink-0 [&div>label>label]:cursor-not-allowed [&div>label>label]:disabled [&div>labe>label]:opacity-50">
                                <RadioTab
                                  name="groupDiscountType"
                                  label="Group Discount (in price or percentage)"
                                  options={discountTypeOptions}
                                  control={control}
                                  disabled={!userCanSubmit}
                                />
                              </div>

                              <div className="flex-grow">
                                <NumericalInput
                                  label={<span>&nbsp;</span>}
                                  decimalPlaces={2}
                                  maxLimit={12}
                                  inputProps={{
                                    min: 0.00,
                                    max: 9999999999.99,
                                    disabled: isFetchingNonRSDetails || !userCanSubmit
                                  }}
                                  registration={register('groupDiscountPrice', {
                                    setValueAs: value => {
                                      return value
                                        ? Number(parseFloat(value))
                                        : null;
                                    },
                                  })}
                                  name="groupDiscountPrice"
                                  placeholder="Input Discount"
                                  beforeIcon={
                                    groupDiscountType === 'percent'
                                      ? PercentIcon
                                      : PesoIcon
                                  }
                                  value={watch('groupDiscountPrice') ?? ''}
                                  onChange={e => {
                                    const value = e.target.value;
                                    setValue('groupDiscountPrice', Number(parseFloat(value)));
                                  }}
                                />
                              </div>
                            </div>

                            <hr className="col-span-2" />

                            <div className="col-span-1 flex flex-col gap-y-2">
                              {isStatusSubmitted && (
                                <FieldWrapper>
                                  <Button
                                    type="button"
                                    onClick={() =>
                                      openModal(
                                        MODAL_TYPES.INVOICE_ATTACHMENT,
                                        'attachments',
                                      )
                                    }
                                    icon={FileIcon}
                                    iconSize="h-5 w-auto"
                                    variant="outline"
                                    hover="outline"
                                    label={
                                      <div className="flex justify-between align-middle sm:flex-row flex-col">
                                        <p>Invoice Attachments</p>
                                        {invoiceAttachmentsData?.hasNewNotifications && (
                                          <Pill className="text-[#EB5757] bg-[#FCE9D8] text-center">
                                            New Attachment/s
                                          </Pill>
                                        )}
                                      </div>
                                    }
                                  >
                                    Check Invoice Attachments
                                  </Button>
                                </FieldWrapper>
                              )}
                              <FileUpload
                                name="invoiceAttachments"
                                label="Invoice Attachment/s"
                                modelType={MODELS.NON_RS_INVOICE}
                                ref={invoiceFileRef}
                                setIsUploading={
                                  setIsUploadingInvoiceAttachments
                                }
                                disabled={
                                  !(
                                    isOneOfApprovers ||
                                    isCreator ||
                                    isCreateMode
                                  ) ||
                                  isUploadingInvoiceAttachments ||
                                  isUploading ||
                                  isRequestCancelled
                                }
                              />
                              {!isStatusSubmitted && (
                                <div className="flex flex-col gap-2 bg-gray-50 mb-2 rounded-lg shadow-sm max-h-36 overflow-y-auto">
                                  {nonRSDetails?.invoiceAttachment &&
                                    nonRSDetails?.invoiceAttachment?.map(
                                      ({ id, modelId, fileName }, index) => (
                                        <div
                                          key={`${modelId}-${index}`}
                                          className="flex w-full"
                                        >
                                          <div className="w-full">
                                            <Input
                                              id={id}
                                              name={`${fileName}.${index}`}
                                              type="text"
                                              value={fileName}
                                              readOnly
                                              disabled
                                              className="w-full relative disabled:bg-gray-50 disabled:opacity-80 disabled:shadow-none text-black font-normal border-0"
                                              hasIcon={isRequestor}
                                              renderIcon={XIcon}
                                              iconHandler={() => {
                                                removeAttachment(id);
                                              }}
                                            />
                                          </div>
                                        </div>
                                      ),
                                    )}
                                </div>
                              )}

                              {isStatusSubmitted &&
                                !isRequestCancelled &&
                                (isOneOfApprovers || isCreator) && (
                                  <Button
                                    className="w-fit self-end"
                                    variant="outline"
                                    hover="outline"
                                    type="button"
                                    onClick={() =>
                                      openModal(
                                        MODAL_TYPES.SUBMIT_INVOICE_ATTACHMENT,
                                      )
                                    }
                                    disabled={
                                      isUploading ||
                                      isUploadingInvoiceAttachments
                                    }
                                    isLoading={
                                      isUploading ||
                                      isUploadingInvoiceAttachments
                                    }
                                  >
                                    Submit
                                  </Button>
                                )}
                            </div>

                            <div className="col-span-1 flex flex-col relative gap-y-2">
                              {isStatusSubmitted && (
                                <FieldWrapper>
                                  <Button
                                    type="button"
                                    onClick={() =>
                                      openModal(
                                        MODAL_TYPES.INVOICE_NOTES,
                                        'notes',
                                      )
                                    }
                                    icon={CommentIcon}
                                    iconSize="h-5 w-auto"
                                    variant="outline"
                                    hover="outline"
                                    label={
                                      <div className="flex justify-between align-middle sm:flex-row flex-col">
                                        <p>Invoice Note</p>
                                        {invoiceNotesData?.hasNewNotifications && (
                                          <Pill className="text-[#EB5757] bg-[#FCE9D8] text-center">
                                            New Comment/s
                                          </Pill>
                                        )}
                                      </div>
                                    }
                                  >
                                    Check Invoice Notes
                                  </Button>
                                </FieldWrapper>
                              )}
                              <TextArea
                                label="Invoice Note"
                                name="invoiceNotes"
                                placeholder="Input note"
                                defaultValue={
                                  !isStatusSubmitted
                                    ? nonRSDetails?.invoiceNotes?.note
                                    : ''
                                }
                                ref={invoiceNoteRef}
                                textLeft={true}
                                control={control}
                                disabled={
                                  !(
                                    isOneOfApprovers ||
                                    isCreator ||
                                    isCreateMode
                                  ) ||
                                  isCreatingNote ||
                                  isRequestCancelled
                                }
                              />
                              {isStatusSubmitted &&
                                !isRequestCancelled &&
                                (isOneOfApprovers || isCreator) && (
                                  <Button
                                    className="w-fit self-end"
                                    variant="outline"
                                    hover="outline"
                                    type="button"
                                    onClick={() =>
                                      openModal(
                                        MODAL_TYPES.SUBMIT_INVOICE_NOTE,
                                        {
                                          isOneOfApprovers,
                                        },
                                      )
                                    }
                                    disabled={isCreatingNote}
                                  >
                                    Submit
                                  </Button>
                                )}
                            </div>
                          </div>
                        </div>
                      </SectionWrapper>

                      <SectionWrapper
                        header={
                          <>
                            Charge To &thinsp;
                            <span
                              style={{
                                fontStyle: 'italic',
                                fontSize: '0.9rem',
                                fontWeight: 'normal',
                              }}
                            >
                              (optional)
                            </span>
                          </>
                        }
                        hasDivider={false}
                      >
                        <div className="flex flex-col space-y-6">
                          <div className="grid grid-cols-2 gap-4 flex-1 flex-grow">
                            <Select
                              label={
                                <>
                                  Charge To (Category)&thinsp;
                                  <span
                                    style={{
                                      fontStyle: 'italic',
                                      fontSize: '0.9em',
                                    }}
                                  >
                                    (optional)
                                  </span>
                                </>
                              }
                              name="chargeTo"
                              control={control}
                              {...register('chargeTo')}
                              options={chargeToCategories}
                              searchable
                              onChange={(value, fieldName) => {
                                setChargeToCategory(value);
                                if (fieldName === 'chargeTo') {
                                  setValue('chargeToId', '');
                                }
                              }}
                              disabled={
                                isFetchingNonRSDetails || !userCanSubmit
                              }
                            />
                            <Select
                              searchable
                              label={
                                <>
                                  Charge To &thinsp;
                                  <span
                                    style={{
                                      fontStyle: 'italic',
                                      fontSize: '0.9em',
                                    }}
                                  >
                                    (optional)
                                  </span>
                                </>
                              }
                              name="chargeToId"
                              control={control}
                              {...register('chargeToId')}
                              options={chargeToList}
                              disabled={
                                isFetchingChargeToList || !userCanSubmit
                              }
                            />
                          </div>
                        </div>
                      </SectionWrapper>
                    </div>
                  );
                }}
              </Form>
            </div>

            {isStatusSubmitted && (
              <div className="xl:col-span-1 space-y-4">
                <StatusSection status={nonRSDetails?.status} />
                <NonRSApproversSection
                  approvers={approvers}
                  shouldShowApproverModal={shouldShowApproverModal}
                  onAddApprover={addApproverOnList}
                  onDeleteApprover={deleteApproverOnList}
                  assignedTo={isCreator}
                />
              </div>
            )}
          </div>

          <hr />

          <SectionWrapper header="Item/s" className="p-0 bg-transparent">
            <SearchBar
              ref={searchBarRef}
              className="p-0 bg-transparent"
              searchItems={searchItems}
              onSearch={handleSearch}
              onClear={handleSearch}
              setPage={setPage}
            />
          </SectionWrapper>

          {userCanSubmit && (
            <Form
              onSubmit={handleAddItems}
              hasErrorSpace={false}
              className="flex flex-col gap-y-4"
              ref={itemFormRef}
            >
              {({ register, control, formState, watch, setValue }) => {
                const discountType = watch('discountType');
                return (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-4 px-2 justify-end items-end">
                    <div className="col-span-1 sm:col-span-2 lg:col-span-2">
                      <Input
                        name="name"
                        label="Item"
                        placeholder="Input Item"
                        {...register('name')}
                        className="w-full bg-white"
                      />
                    </div>

                    <div className="col-span-1 sm:col-span-1 lg:col-span-2">
                      <CustomSelect
                        label="Unit"
                        name="unit"
                        className="max-w-48 mx-auto [&>label]:mb-0"
                        inputClassName="bg-white"
                        {...register('unit')}
                        control={control}
                        options={customUnits}
                        error={formState.errors['unit']}
                        onAddNewOption={handleAddNewUnit}
                        placeholder="Select or type unit..."
                        ref={unitSelectRef}
                      />
                    </div>

                    <div className="col-span-1 sm:col-span-1 lg:col-span-2">
                      <NumericalInput
                        label="Quantity"
                        decimalPlaces={3}
                        maxLimit={12}
                        inputProps={{
                          min: 0.00,
                          max: 99999.999,
                        }}
                        inputClassName="bg-white"
                        name="quantity"
                        placeholder="Input Quantity"
                        value={watch('quantity') ?? ''}
                        onChange={e => {
                          const value = e.target.value;
                          setValue('quantity', value ? Number(parseFloat(value)) : null);
                        }}
                      />
                    </div>

                    <div className="col-span-1 sm:col-span-1 lg:col-span-2">
                      <NumericalInput
                        label="Amount"
                        decimalPlaces={2}
                        maxLimit={12}
                        inputProps={{
                          min: 0.00,
                          max: 9999999999.99
                        }}
                        inputClassName="bg-white"
                        name="amount"
                        placeholder="Input Amount"
                        value={watch('amount') ?? ''}
                        onChange={e => {
                          const value = e.target.value;
                          setValue('amount', value ? Number(parseFloat(value).toFixed(2)) : null);
                        }}
                      />
                    </div>

                    <div className="col-span-1 sm:col-span-2 lg:col-span-3">
                      <div className="flex flex-col sm:flex-row gap-4 w-full">
                        <div className="flex-1">
                          <RadioTab
                            name="discountType"
                            label="Discount Type"
                            options={discountTypeOptions}
                            className="w-full bg-white"
                            control={control}
                            defaultValue={'fixed'}
                          />
                        </div>
                        <div className="flex-1">
                          <NumericalInput
                            label={<span>&nbsp;</span>}
                            decimalPlaces={2}
                            maxLimit={12}
                            inputProps={{
                              min: 0.00,
                              max: 9999999999.99,
                            }}
                            inputClassName="bg-white"
                            name="discountValue"
                            placeholder="Input Discount"
                            beforeIcon={
                              discountType === 'percent'
                                ? PercentIcon
                                : PesoIcon
                            }
                            value={watch('discountValue') ?? ''}
                            onChange={e => {
                              const value = e.target.value;
                              setValue('discountValue', value ? Number(parseFloat(value).toFixed(2)) : null);
                            }}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="col-span-1 sm:col-span-2 lg:col-span-1 flex justify-end mt-4 lg:mt-0">
                      <Button
                        variant="outline"
                        hover="outline"
                        className="w-full sm:w-fit min-w-24"
                      >
                        Add Item
                      </Button>
                    </div>
                  </div>
                );
              }}
            </Form>
          )}

          <hr />

          <Pagination
            total={itemsToShow?.length || 0}
            setPage={setPage}
            asHOC={true}
            maxPerPage={currentLimit}
          >
            {({ currentPage, limit }) => (
              <Table
                headers={renderHeaders()}
                tdDesign={tdDesign}
                data={itemsToShow}
                isLoading={isFetchingNonRSItemList}
                onSort={setSort}
                currentSort={currentSort}
                page={currentPage}
                limit={limit}
                hasHoverTitle={false}
                removeOverflowHidden={true}
              />
            )}
          </Pagination>

          <SectionWrapper>
            <div className="flex flex-col space-y-6">
              <div className="grid grid-cols-2 gap-4 flex-1 flex-grow">
                <div className="col-span-1 flex flex-col gap-y-2">
                  {isStatusSubmitted && (
                    <FieldWrapper>
                      <Button
                        type="button"
                        onClick={() =>
                          openModal(MODAL_TYPES.ATTACHMENT, 'attachments')
                        }
                        icon={FileIcon}
                        iconSize="h-5 w-auto"
                        variant="outline"
                        hover="outline"
                        label={
                          <div className="flex justify-between align-middle sm:flex-row flex-col">
                            <p>Additional Attachments</p>
                            {attachmentsData?.hasNewNotifications && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8] text-center">
                                New Attachment/s
                              </Pill>
                            )}
                          </div>
                        }
                      >
                        Check Additional Attachments
                      </Button>
                    </FieldWrapper>
                  )}
                  <FileUpload
                    name="attachments"
                    label="Additional Attachment/s"
                    modelType={MODELS.NON_RS}
                    ref={fileRef}
                    setIsUploading={setIsUploading}
                    disabled={
                      !(isOneOfApprovers || isCreator || isCreateMode) ||
                      isUploadingAttachments ||
                      isUploading ||
                      isRequestCancelled
                    }
                  />
                  {!isStatusSubmitted && (
                    <div className="flex flex-col gap-2 bg-gray-50 mb-2 rounded-lg shadow-sm max-h-36 overflow-y-auto">
                      {nonRSDetails?.attachments &&
                        nonRSDetails?.attachments?.map(
                          ({ id, modelId, fileName }, index) => (
                            <div
                              key={`${modelId}-${index}`}
                              className="flex w-full"
                            >
                              <div className="w-full">
                                <Input
                                  id={id}
                                  name={`${fileName}.${index}`}
                                  type="text"
                                  value={fileName}
                                  readOnly
                                  disabled
                                  className="w-full relative disabled:bg-gray-50 disabled:opacity-80 disabled:shadow-none text-black font-normal border-0"
                                  hasIcon={isRequestor}
                                  renderIcon={XIcon}
                                  iconHandler={() => {
                                    removeAttachment(id);
                                  }}
                                />
                              </div>
                            </div>
                          ),
                        )}
                    </div>
                  )}

                  {isStatusSubmitted &&
                    !isRequestCancelled &&
                    (isOneOfApprovers || isCreator) && (
                      <Button
                        className="w-fit self-end"
                        variant="outline"
                        hover="outline"
                        type="button"
                        onClick={() => openModal(MODAL_TYPES.SUBMIT_ATTACHMENT)}
                        disabled={isUploading || isUploadingAttachments}
                        isLoading={isUploading || isUploadingAttachments}
                      >
                        Submit
                      </Button>
                    )}
                </div>

                <div className="col-span-1 flex flex-col relative gap-y-2">
                  {isStatusSubmitted && (
                    <FieldWrapper>
                      <Button
                        type="button"
                        onClick={() => openModal(MODAL_TYPES.NOTES, 'notes')}
                        icon={CommentIcon}
                        iconSize="h-5 w-auto"
                        variant="outline"
                        hover="outline"
                        label={
                          <div className="flex justify-between align-middle sm:flex-row flex-col">
                            <p>Additional Notes</p>
                            {notesData?.hasNewNotifications && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8] text-center">
                                New Comment/s
                              </Pill>
                            )}
                          </div>
                        }
                      >
                        Check Additional Notes
                      </Button>
                    </FieldWrapper>
                  )}

                  <TextArea
                    label="Additional Notes"
                    name="notes"
                    placeholder="Input note"
                    defaultValue={
                      !isStatusSubmitted ? nonRSDetails?.note?.note : ''
                    }
                    ref={noteRef}
                    textLeft={true}
                    // control={control}
                    disabled={
                      !(isOneOfApprovers || isCreator || isCreateMode) ||
                      isCreatingNote ||
                      isRequestCancelled
                    }
                  />
                  {isStatusSubmitted &&
                    !isRequestCancelled &&
                    (isOneOfApprovers || isCreator) && (
                      <Button
                        className="w-fit self-end"
                        variant="outline"
                        hover="outline"
                        type="button"
                        onClick={() =>
                          openModal(MODAL_TYPES.SUBMIT_NOTE, {
                            isOneOfApprovers,
                          })
                        }
                        disabled={isCreatingNote}
                      >
                        Submit
                      </Button>
                    )}
                </div>
              </div>
            </div>
          </SectionWrapper>

          <hr />

          <div className="flex flex-row justify-end">
            <NonRSFinancial
              supplierInvoiceAmount={
                supplierInvoiceAmount || nonRSDetails?.supplierInvoiceAmount
              }
              groupDiscount={{
                groupDiscountPrice:
                  nonRSDetails?.groupDiscountPrice ??
                  groupDiscount.groupDiscountPrice,
                groupDiscountType:
                  nonRSDetails?.groupDiscountType ??
                  groupDiscount.groupDiscountType,
              }}
            />
          </div>

          <div
            className={cn('flex gap-x-2 justify-between', {
              'justify-end': !(isCreateMode || isDraftAndIsCreator),
              'justify-start': !userCanSubmit,
            })}
          >
            {(isCreateMode || isDraftAndIsCreator) && (
              <Button
                variant="outline"
                hover="outline"
                type="button"
                className="w-fit min-w-24"
                onClick={() => openModal(MODAL_TYPES.SAVE_DRAFT)}
                disabled={formDisabled}
              >
                Save Draft
              </Button>
            )}

            {!userCanSubmit &&
              !isRequestCancelled &&
              isCreator &&
              isAllApproversForPending && (
                <div className="flex gap-x-4 items-center">
                  <Button
                    variant="danger"
                    hover="danger"
                    type="button"
                    className="w-fit min-w-24"
                    onClick={() => openModal(MODAL_TYPES.CANCEL_REQUEST)}
                    disabled={formDisabled}
                  >
                    Cancel Request
                  </Button>
                  <div className="flex text-sm gap-x-2">
                    <span className="font-bold">Warning:</span>
                    <p>
                      This action is irreversible. All progress will be lost
                      once request is cancelled.
                    </p>
                  </div>
                </div>
              )}

            {userCanSubmit && (
              <div className="flex gap-x-2 self-end">
                <Button
                  className="w-fit min-w-24"
                  variant="outline"
                  hover="outline"
                  type="button"
                  onClick={() => openModal(MODAL_TYPES.CANCEL_SUBMIT)}
                >
                  Cancel
                </Button>
                <Button
                  className="w-fit min-w-24"
                  type="button"
                  onClick={() => openModal(MODAL_TYPES.CONFIRM_SUBMIT)}
                  disabled={formDisabled}
                >
                  Submit
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <ConfirmModal
        isOpen={modalState.type === MODAL_TYPES.CONFIRM_SUBMIT}
        onClose={closeModal}
        onConfirm={() => submitFormRef.current.requestSubmit()}
        // isLoading={isSubmittingCanvassItems}
        header="Submit Non-RS Payment Request"
        message="You are about to submit this request. Make sure all items are correct. Press submit if you want to proceed with this action."
      />

      <ConfirmModal
        isOpen={modalState.type === MODAL_TYPES.SAVE_DRAFT}
        onClose={closeModal}
        onConfirm={() => submitFormRef.current.requestSubmit({ asDraft: true })}
        isLoading={isSubmittingRequest}
        header="Save Draft"
        message="You are about to save this request as a draft. Make sure all items are correct. Press save if you want to proceed with this action."
      />

      <ConfirmModal
        isOpen={modalState.type === MODAL_TYPES.CANCEL_REQUEST}
        onClose={closeModal}
        onConfirm={handleCancelNonRS}
        isLoading={isCancellingRequest}
        header="Cancel Non-RS Payment Request"
        message="You are about to cancel this request. This action is irreversible. All progress will be lost once request is cancelled."
      />

      <ActionModal
        isOpen={modalState.type === MODAL_TYPES.CANCEL_SUBMIT}
        onClose={closeModal}
        header="Cancel Non-RS Payment Request"
      >
        <div className="flex flex-col space-y-4">
          <span>
            You are about to cancel this request. Press continue if you want to
            proceed with this action.
          </span>
          <div className="grid grid-cols-3 gap-x-2">
            <Button
              type="button"
              className="w-full col-span-1"
              onClick={closeModal}
            >
              Cancel
            </Button>

            {!isCreateModeOrDraft ? (
              <Button
                type="button"
                className="w-full col-span-1"
                variant="outline"
                hover="outline"
                onClick={() => openModal(MODAL_TYPES.CONFIRM_SUBMIT)}
                disabled={formDisabled || isUploading || isUploadingAttachments}
                isLoading={isUploading || isUploadingAttachments}
              >
                Submit
              </Button>
            ) : (
              <Button
                type="button"
                className="w-full col-span-1"
                variant="outline"
                hover="outline"
                onClick={() => openModal(MODAL_TYPES.SAVE_DRAFT)}
                disabled={formDisabled || isUploading || isUploadingAttachments}
                isLoading={isUploading || isUploadingAttachments}
              >
                Save Draft
              </Button>
            )}

            <Button
              type="button"
              className="w-full col-span-1"
              variant="outline"
              hover="outline"
              onClick={() => window.location.reload()}
            >
              Continue
            </Button>
          </div>
        </div>
      </ActionModal>

      <ActionModal
        isOpen={modalState.type === MODAL_TYPES.EDIT_DISCOUNT}
        onClose={closeModal}
        header="Item Discount"
      >
        <div className="flex flex-col space-y-4">
          <span>
            Please enter the discount for the item selected. You can choose
            between fixed amount or percentage. Discounted amount will be
            automatically updated.
          </span>

          <Form
            onSubmit={handleUpdateDiscount}
            hasErrorSpace={false}
            className="flex flex-col gap-y-4"
            options={{
              defaultValues: {
                discountType: modalState?.data?.discountType || 'fixed',
                discountValue: modalState?.data?.discountValue || ''
              }
            }}
          >
            {({ register, control, watch, setValue }) => {
              const discountType = watch('discountType');
              return (
                <React.Fragment>
                  <div className="grid grid-cols-2">
                    <RadioTab
                      name="discountType"
                      label="Discount Type"
                      options={discountTypeOptions}
                      className="w-full bg-white"
                      control={control}
                      defaultValue={modalState?.data?.discountType || 'fixed'}
                    />
                    <NumericalInput
                      label={<span>&nbsp;</span>}
                      decimalPlaces={2}
                      maxLimit={12}
                      inputProps={{
                        min: 0.00,
                        max: 9999999999.99,
                      }}
                      registration={register('discountValue', {
                        setValueAs: value => {
                          return value
                            ? Number(parseFloat(value).toFixed(2))
                            : null;
                        },
                      })}
                      name="discountValue"
                      placeholder="Input Discount"
                      beforeIcon={
                        (discountType || modalState?.data?.discountType || 'fixed') === 'percent' ? PercentIcon : PesoIcon
                      }
                      value={watch('discountValue') ?? ''}
                      onChange={e => {
                        const value = e.target.value;
                        setValue('discountValue', value);
                      }}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-x-2">
                    <Button
                      type="button"
                      className="w-full col-span-1"
                      onClick={closeModal}
                    >
                      Cancel
                    </Button>

                    <Button
                      type="submit"
                      className="w-full col-span-1"
                      variant="outline"
                      hover="outline"
                    >
                      Apply
                    </Button>
                  </div>
                </React.Fragment>
              );
            }}
          </Form>
        </div>
      </ActionModal>

      <ConfirmModal
        isOpen={modalState.type === MODAL_TYPES.SUBMIT_INVOICE_ATTACHMENT}
        onClose={closeModal}
        onConfirm={() => handleSubmitInvoiceAttachments()}
        header="Submit Invoice Attachment"
      />

      <ConfirmModal
        isOpen={modalState.type === MODAL_TYPES.SUBMIT_INVOICE_NOTE}
        onClose={closeModal}
        onConfirm={() => handleCreateInvoiceNotes(modalState.data)}
        header="Submit Invoice Note"
      />

      {/* Add this new AttachmentAndNotesModal for invoice */}
      <AttachmentAndNotesModal
        isOpen={
          modalState.type === MODAL_TYPES.INVOICE_ATTACHMENT ||
          modalState.type === MODAL_TYPES.INVOICE_NOTES
        }
        onClose={closeModal}
        type={modalState.data}
        model={MODELS.NON_RS_INVOICE}
        modelId={id}
      />

      <ConfirmModal
        isOpen={modalState.type === MODAL_TYPES.SUBMIT_ATTACHMENT}
        onClose={closeModal}
        onConfirm={() => handleSubmitAttachments()}
        header="Submit Attachment"
      />

      <ConfirmModal
        isOpen={modalState.type === MODAL_TYPES.SUBMIT_NOTE}
        onClose={closeModal}
        onConfirm={() => handleCreateNotes(modalState.data)}
        header="Submit Note"
      />

      <AttachmentAndNotesModal
        isOpen={
          modalState.type === MODAL_TYPES.ATTACHMENT ||
          modalState.type === MODAL_TYPES.NOTES
        }
        onClose={closeModal}
        type={modalState.data}
        model={MODELS.NON_RS}
        modelId={id}
      />
    </React.Fragment>
  );
};

export { NonRSManagement };
