import React from 'react';
import { useParams } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import useRSTabStore from '@src/store/rsTabStore';
import VisitMainRSButton from '@src/components/ui/Button/VisitMainRSButton';
import { Button } from '@src/components/ui/Button';
import Download from '@assets/icons/download.svg?react';
import { useDownloadTemplatePdf } from '@src/hooks/useDownloadTemplatePdf';
import { useUserStore } from '@store';
import { useGetRequisitionSlip } from '@features/dashboard/api/get-requisition-slip';

const CanvassDetails = ({ isCreateMode, canvassDetails = {} }) => {
  const { mutate: downloadPdf, isPending: isPdfDownloading } =
    useDownloadTemplatePdf();


  const navigate = useNavigate();
  const { resetTabs } = useRSTabStore();
  const { canvassId } = useParams();
  const { id, canvassRequisitions, rsNumber, companyCode } = canvassDetails;
  const { user } = useUserStore();

  const { data: requisition } = useGetRequisitionSlip(id);
  const canvassData = canvassRequisitions.find(
    canvass => canvass.id === parseInt(canvassId),
  );
  const { csLetter, draftCsNumber, csNumber } = canvassData || {};

  const isDraft = canvassData?.status === 'draft';
  const canvassNumber = isDraft
    ? `CS-TMP-${companyCode}${csLetter}${draftCsNumber}`
    : `CS-${companyCode}${csLetter}${csNumber}`;

  const canDownload =
    user?.id === requisition?.createdByUser?.id ||
    user?.role?.name.includes('Purchasing');

  const handleDownload = () => {
    downloadPdf({
      type: 'canvass',
      id: canvassId,
    });
  };

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-4 md:gap-6">
      <div className="flex flex-col gap-y-4 md:col-span-3">
        <div className="flex flex-col gap-y-2 text-xl font-bold md:flex-row md:gap-4">
          <div className="flex gap-x-1 items-center">
            <span>Canvass Sheet Number</span>
            {isDraft && <span className="text-[#F2994A]">(draft)</span>}:
          </div>
          <div>{!isCreateMode ? canvassNumber : '--'}</div>
        </div>
        <div className="flex flex-col gap-y-2 text-sm md:flex-row md:gap-4">
          <div className="font-bold">R.S. Number:</div>
          <div>{rsNumber || '--'}</div>
        </div>
      </div>

      <div className="flex items-center flex-wrap justify-center md:flex-nowrap md:justify-end">
        {!isCreateMode && !isDraft && canDownload && (
          <Button
            variant="action"
            hover="action"
            className="bg-[#F0F1F1] text-[#420001] w-auto mr-4"
            icon={Download}
            iconPosition="L"
            isLoading={isPdfDownloading}
            disabled={isPdfDownloading}
            onClick={handleDownload}
          >
            Download
          </Button>
        )}
        <VisitMainRSButton
          rsId={id}
          className="w-full md:w-fit"
        />
      </div>
    </div>
  );
};

export { CanvassDetails };
