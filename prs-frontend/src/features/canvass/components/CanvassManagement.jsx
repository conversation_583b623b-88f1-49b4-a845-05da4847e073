import React, { useState, useEffect, useRef, useMemo, useReducer } from 'react';
import { Pill } from '@components/ui/Pill';
import { CanvassDetails } from './CanvassDetails';
import { Button } from '@src/components/ui/Button';
import { SupplierDetails } from './SupplierDetails';
import { SearchBar } from '@src/components/ui/SearchBar';
import { Table, Pagination } from '@components/ui/Table';
import { ActionModal, ConfirmModal } from '@components/ui/Modal';
import { Form, File, TextArea, FileUpload } from '@components/ui/Form';
import { SectionWrapper } from '@components/layouts/SectionWrapper';
import { useNavigate, useParams } from 'react-router-dom';
import { ItemGroupSelectionModal } from './ItemGroupSelectionModal';
import { AddItemModal } from './AddItemModal';
import { PendingAction } from './PendingAction';
import { CanvassStatus } from './CanvassStatus';
import { Spinner } from '@src/components/ui/Spinner';
import { EnterCanvassModal } from './EnterCanvassModal';
import { StatusSection } from './StatusSection';
import { ApproversSection } from './ApproversSection';
import { AssignedStaffSection } from './AssignedStaffSection';
import { AttachmentAndNotesModal } from '@features/attachments/components';
import { Tabs } from '@src/components/ui/Tabs';
import { Input } from '@components/ui/Form';
import { cn } from '@utils/cn';
import { sortTableItems } from '@utils/query';
import { setDefaultDateValue } from '@utils/dateFormat';
import { submitCanvassSchema } from '@schema/canvass.schema';
import { formatDateToDMY, formatDateToLocalTime } from '@utils/dateFormat';
import {
  getNextApprover,
  shouldShowComponentViaApprovers,
} from '@utils/objectValidator';
import { MODELS } from '@config/modelsConfig';
import { NOTES_TYPE, NOTES_USER_TYPES } from '@config/notesConfig';

import { useNotification } from '@hooks/useNotification';
import { useUserStore, useCanvassItemsStore } from '@store';
import {
  useSubmitCanvass,
  useGetCanvassItems,
  useGetCanvassApprovers,
} from '../api';
import { useGetOFMItems } from '@features/ofm/api';
import { useGetNotes, useCreateNotes } from '@features/notes/api';
import {
  useGetAttachments,
  useAssignAttachments,
} from '@features/attachments/api';
import { useGetRSCanvassDetails } from '@features/dashboard/api';
import { useSticky } from '@hooks/useSticky';
import {
  steelbarsItemsExtractor,
  nonSteelbarsItemsExtractor,
  matchingAccountCodeExtractor,
} from '@src/utils/itemListSelector';
import useTableTabs from '@src/hooks/useTableTabs';
import DownIcon from '@assets/icons/down.svg?react';
import AddIcon from '@assets/icons/add.svg?react';
import FileIcon from '@src/assets/icons/file.svg?react';
import PencilIcon from '@assets/icons/pencil.svg?react';
import CommentIcon from '@src/assets/icons/comment.svg?react';
import MoreHorizontalIcon from '@assets/icons/more_horizontal.svg?react';
import { DropdownMenu } from '@src/components/ui/DropdownMenu/DropdownMenu';
import PurchaseHistoryModal from './PurchaseHistoryModal';
import { ScreenModal } from '@components/ui/Modal';

const MODAL_TYPES = {
  NONE: 'NONE',
  ADD_ITEM_CANVASS: 'ADD_ITEM_CANVASS',
  SUPPLIER_CANVASS: 'SUPPLIER_CANVASS',
  CONFIRM_SUBMIT: 'CONFIRM_SUBMIT',
  CANCEL_SUBMIT: 'CANCEL_SUBMIT',
  SAVE_DRAFT: 'SAVE_DRAFT',
  NOTES: 'NOTES',
  ATTACHMENT: 'ATTACHMENT',
  SUBMIT_NOTES: 'SUBMIT_NOTES',
  SUBMIT_ATTACHMENT: 'SUBMIT_ATTACHMENT',
  VIEW_PURCHASE_HISTORY: 'VIEW_PURCHASE_HISTORY',
  DELETE_ITEM_CONFIRM: 'DELETE_ITEM_CONFIRM',
  SCREEN_MODAL: 'SCREEN_MODAL',
};

const MODES = {
  VIEW: 'VIEW',
  EDIT: 'EDIT',
  CREATE: 'CREATE',
};

const FORM_KEYS = {
  ADD_ITEM: 'addItems',
  UPDATE_ITEM: 'updateItems',
  DELETE_ITEM: 'deleteItems',
};

const MAIN_LEVELS = [1, 2];

const searchItems = [
  {
    type: 'text',
    label: 'Search:',
    name: 'itemName',
    placeholder: 'Search Items',
    isParent: true,
  },
];

const sortTypeConfig = {
  item: 'string',
  accountCode: 'string',
  unit: 'string',
  quantity: 'number',
  weight: 'number',
  remainingGfq: 'number',
  approvedQty: 'number',
  status: 'string',
};

const CanvassManagement = ({
  currentPage,
  currentLimit,
  setPage,
  setSort,
  resetPage,
  sortBy,
  currentSort,
  permissions,
  setNavigateTo = null,
}) => {
  const navigate = useNavigate();
  const { id, canvassId } = useParams();
  const isCreateMode = !canvassId;

  const { user } = useUserStore();
  const { showNotification } = useNotification();

  const { create: createPermission, update: updatePermission } = permissions;

  const [approvers, setApprovers] = useState([]);
  const [formDisabled, setFormDisabled] = useState(false);
  const [canvassModalState, setCanvassModalState] = useState({
    type: MODAL_TYPES.NONE,
    mode: null,
    data: null,
    activeTab: null,
  });

  const [isUploading, setIsUploading] = useState(false);
  const [hasAttachments, setHasAttachments] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  function groupedSteelbarsReducer(state, action) {
    switch (action.type) {
      case 'TOGGLE': {
        return state.includes(action.id) 
          ? state.filter(i => i !== action.id)
          : [...state, action.id];
      }
      case 'RESET':
        return [];
      default:
        return state;
    }
  }

  const [groupedSteelbars, setGroupedSteelbars] = useReducer(groupedSteelbarsReducer, []);

  const noteRef = useRef(null);
  const fileRef = useRef(null);
  const submitFormRef = useRef(null);
  const searchBarRef = useRef(null);

  const { mutateAsync: assignAttachments, isPending: isSubmittingAttachments } =
    useAssignAttachments();

  const { mutateAsync: createNotes, isPending: isSubmittingNotes } =
    useCreateNotes();

  const { data: canvassApprovers, isFetching: isFetchingCanvassApprovers } =
    useGetCanvassApprovers({ id: canvassId }, { enabled: !!canvassId });

  const { data: canvassDetails, isFetching: isFetchingCanvassDetails } =
    useGetRSCanvassDetails(id, { staleTime: 0 });

  const { data: canvassItems, isFetching: isFetchingCanvassItems } =
    useGetCanvassItems({ id, canvassId }, { staleTime: 0 });

  const { data: attachmentsData } = useGetAttachments(
    {
      model: MODELS.CANVASS,
      modelId: canvassId,
    },
    { enabled: !!canvassId },
  );

  const { data: notesData } = useGetNotes(
    {
      model: MODELS.CANVASS,
      modelId: canvassId,
      dateFrom: '',
      dateTo: '',
    },
    { enabled: !!canvassId },
  );

  const { isSticky, elementRef } = useSticky({
    offset: 200,
    disabled:
      isFetchingCanvassDetails ||
      isFetchingCanvassApprovers ||
      isFetchingCanvassItems,
  });

  const {
    mutateAsync: submitCanvassItems,
    isPending: isSubmittingCanvassItems,
  } = useSubmitCanvass(id);

  const isTOM = useMemo(
    () => canvassDetails?.type.includes('tom'),
    [canvassDetails],
  );

  const isOFMEnabled = useMemo(
    () => canvassDetails?.type === 'ofm' || canvassDetails?.type === 'ofm-tom',
    [canvassDetails],
  );

  const { data: OFMItems, isLoading: isFetchingOFMItems } = useGetOFMItems({
    paginate: false,
    sortBy: { initial: 'ASC' },
  });

  useEffect(() => {
    if (id && setNavigateTo) {
      setNavigateTo(id);
    }
  }, [id, setNavigateTo]);

  useEffect(() => {
    if (canvassApprovers && !isFetchingCanvassApprovers) {
      setApprovers([...canvassApprovers]);
    }
  }, [canvassApprovers, isFetchingCanvassApprovers]);

  useEffect(() => {
    if (canvassModalState.type !== MODAL_TYPES.SCREEN_MODAL && canvassModalState.type !== MODAL_TYPES.NONE) {
      setGroupedSteelbars({ type: 'RESET' });
    }
  }, [canvassModalState]);

  const {
    tableItems,
    setTableItems,
    addItems,
    updateItems,
    deleteItems,
    resetItems,
    resetTempItems,
    removeAllTableItems,
    filteredTableItems,
    setFilteredTableItems,
    updateIsExpandedOnSupplier,
    updateSupplierSelection,
    addToDeleteItems,
  } = useCanvassItemsStore();

  useEffect(() => {
    resetItems();
    resetTempItems();
    removeAllTableItems();
  }, []);

  useEffect(() => {
    if (canvassItems && !isFetchingCanvassItems && canvassId) {
      const combinedItems = [...canvassItems, ...addItems];
      const filteredItems = combinedItems.filter(
        item => !deleteItems.some(i => i.id === item.id),
      )
      setTableItems(filteredItems);
    } else {
      if (addItems.length > 0 && isCreateMode) {
        setTableItems(addItems.filter(item => !deleteItems.some(i => i.id === item.id)));
      } else {
        removeAllTableItems();
      }
    }
  }, [canvassItems, isFetchingCanvassItems, canvassId, addItems, deleteItems]);

  const steelbarItemsList = steelbarsItemsExtractor(
    tableItems,
    OFMItems?.data,
    isOFMEnabled,
  );

  const steelbarItems = matchingAccountCodeExtractor(
    steelbarItemsList,
    tableItems,
  );

  const nonSteelbarItems = nonSteelbarsItemsExtractor(
    steelbarItems,
    tableItems,
  );

  const isSteelbarsOnly = !nonSteelbarItems?.length && steelbarItems?.length > 0;

  const isNonSteelbarsOnly = !steelbarItems?.length && nonSteelbarItems?.length > 0;

  const computeTableConfig = () => {
    if (isSteelbarsOnly) {
      return [{
          key: 'steel-bars',
          value: 'Steel Bars',
      }];
    } else if (isNonSteelbarsOnly) {
      return [{
          key: 'items',
          value: 'Items',
        }];
    } else {
      return [
        {
          key: 'items',
          value: 'Items',
        },
        {
          key: 'steel-bars',
          value: 'Steel Bars',
      }]
    }
  };

  const { setActiveTab, activeTab, tabs } = useTableTabs(setPage, currentPage, computeTableConfig());

  const handleSubmitAttachment = async () => {
    const fileReference = fileRef?.current;
    const attachmentIds = fileReference?.attachmentIds;
    const model = MODELS.CANVASS;

    await assignAttachments(
      { attachmentIds, model, modelId: parseInt(canvassId) },
      {
        onSuccess: message => {
          showNotification({
            type: 'success',
            message: message?.message || 'Attachments uploaded successfully',
          });
          fileReference.removeAllFiles();
          setHasAttachments(false);
          closeCanvassModal();
        },
        onError: error => {
          const errorMessage = error?.response?.data?.message || error?.message;
          showNotification({
            type: 'error',
            message: errorMessage,
          });
        },
      },
    );
  };

  const handleCreateNotes = async ({ isOneOfApprovers }) => {
    const notesReference = noteRef.current;
    const notesValue = notesReference?.value;

    const userType = isOneOfApprovers
      ? NOTES_USER_TYPES.APPROVER
      : NOTES_USER_TYPES.REQUESTOR;

    const payload = {
      model: MODELS.CANVASS,
      modelId: canvassId,
      note: notesValue,
      userType,
      commentType: NOTES_TYPE.NOTE,
    };

    await createNotes(payload, {
      onSuccess: () => {
        showNotification({
          type: 'success',
          message: 'Notes added successfully',
        });
        closeCanvassModal();
        notesReference?.clear();
      },
      onError: error => {
        showNotification({
          type: 'error',
          message:
            error?.response?.data?.message ||
            error?.message ||
            'An error occurred while adding a note.',
        });
      },
    });
  };

  const deleteApproverOnList = approverId => {
    setApprovers(prevApprovers => {
      const updatedApprovers = prevApprovers.filter(
        approver => approver.userId !== approverId,
      );

      return [...updatedApprovers];
    });
  };

  const addApproverOnList = newApprover => {
    const { key, value } = newApprover;

    setApprovers(prevApprovers => {
      const currentUserIndex = prevApprovers.findIndex(
        approver => approver.userId === user.id,
      );
      if (currentUserIndex === -1) {
        return prevApprovers;
      }

      const currentUser = prevApprovers[currentUserIndex];
      const currentUserLevel = currentUser.level;

      const existingAdhocIndex = prevApprovers.findIndex(
        approver => approver.isAdhoc && approver.level === currentUserLevel,
      );

      if (existingAdhocIndex !== -1) {
        const updatedApprovers = [...prevApprovers];
        updatedApprovers[existingAdhocIndex] = {
          ...updatedApprovers[existingAdhocIndex],
          isNew: true,
          userId: value,
          approver: {
            id: value,
            firstName: key,
          },
        };
        return updatedApprovers;
      }

      const newApproverData = {
        level: currentUserLevel,
        isAdhoc: true,
        status: 'pending',
        isNew: true,
        userId: value,
        approver: {
          id: value,
          firstName: key,
        },
      };

      const updatedApprovers = [...prevApprovers];
      updatedApprovers.splice(currentUserIndex + 1, 0, newApproverData);

      return updatedApprovers;
    });
  };

  const openCanvassModal = (
    type,
    mode = 'VIEW',
    data = null,
    activeTab = null,
  ) => {
    setCanvassModalState({ type, mode, data, activeTab });
  };

  const closeCanvassModal = () => {
    setCanvassModalState({
      type: MODAL_TYPES.NONE,
      mode: null,
      data: null,
      activeTab: null,
    });
  };

  const appendItemsToFormData = (formData, items, key) => {
    items.forEach((item, index) => {
      if (key === FORM_KEYS.DELETE_ITEM) {
        return formData.append(`${key}[${index}][id]`, item.id);
      }

      if (key === FORM_KEYS.ADD_ITEM) {
        formData.append(`${key}[${index}][requisitionItemListId]`, item.id);
      } else {
        formData.append(`${key}[${index}][id]`, item.id);
        formData.append(
          `${key}[${index}][requisitionItemListId]`,
          item.requisitionItemListId,
        );
      }

      if (item?.suppliers?.length) {
        item.suppliers.forEach((supplier, supplierIndex) => {
          const supplierBase = `${key}[${index}][suppliers][${supplierIndex}]`;

          if (supplier.id) {
            formData.append(`${supplierBase}[id]`, supplier.id);
          }

          formData.append(`${supplierBase}[supplierId]`, supplier.supplierId);
          formData.append(`${supplierBase}[order]`, supplierIndex + 1);
          formData.append(`${supplierBase}[term]`, supplier.term);
          formData.append(`${supplierBase}[quantity]`, supplier.quantity);
          formData.append(`${supplierBase}[unitPrice]`, supplier.unitPrice);
          formData.append(`${supplierBase}[isSteelbars]`, supplier.isSteelbars);
          formData.append(
            `${supplierBase}[supplierType]`,
            supplier.supplierType,
          );
          formData.append(
            `${supplierBase}[discountType]`,
            supplier.discountType,
          );
          formData.append(
            `${supplierBase}[discountValue]`,
            supplier.discountValue,
          );

          if (supplier.notes) {
            formData.append(`${supplierBase}[notes]`, supplier.notes);
          }

          if (supplier.attachmentIds?.length) {
            supplier.attachmentIds.forEach((id, supplierAttachmentIndex) => {
              formData.append(
                `${supplierBase}[attachmentIds][${supplierAttachmentIndex}]`,
                id,
              );
            });
          }
        });
      }
    });
  };

  const hasIncompleteCanvassQuantity = useMemo(() => {
    const itemsNotInAddOrUpdateItems = tableItems.filter(
      item =>
        item.canvassItemId &&
        ![...addItems, ...updateItems].some(
          i => i.canvassItemId === item.canvassItemId,
        ),
    );

    const tableItemsQuantity =
      itemsNotInAddOrUpdateItems.length > 0 &&
      itemsNotInAddOrUpdateItems.some(item => {
        if (!item?.suppliers?.length) return true;

        const summedSuppliersQuantity = item.suppliers.reduce(
          (acc, curr) => acc + curr.quantity,
          0,
        );

        return summedSuppliersQuantity < item.quantity;
      });

    const addItemsQuantity =
      addItems.length > 0 &&
      addItems.some(item => {
        if (!item?.suppliers?.length) return true;

        const summedSuppliersQuantity = item.suppliers.reduce(
          (acc, curr) => acc + curr.quantity,
          0,
        );

        return summedSuppliersQuantity < item.quantity;
      });

    const updateItemsQuantity =
      updateItems.length > 0 &&
      updateItems.some(item => {
        if (!item?.suppliers.length) return true;

        const tableItem = tableItems.find(
          tableItem => tableItem.canvassItemId === item.canvassItemId,
        );
        if (!tableItem) return false;

        const summedSuppliersQuantity = item.suppliers.reduce(
          (acc, curr) => acc + curr.quantity,
          0,
        );

        return summedSuppliersQuantity < tableItem.quantity;
      });

    return addItemsQuantity || updateItemsQuantity || tableItemsQuantity;
  }, [addItems, updateItems, tableItems]);

  const handleOverQtyCanvassItems = async items => {
    addToDeleteItems(items);
    await Promise.resolve();
    submitFormRef.current.requestSubmit();
  };

  const submitCanvassForm = async values => {
    try {
      const { asDraft, comment, attachments } = values;
      const formData = new FormData();

      if (canvassId) formData.append('id', canvassId);
      formData.append('requisitionId', id);
      formData.append('isDraft', asDraft || false);

      if (attachments?.length) {
        attachments.forEach(attachment => {
          formData.append('attachments', attachment.file);
        });
      }

      if (comment) {
        formData.append('notes', comment);
      }

      const submitPayload = {
        addItems,
        updateItems,
        deleteItems,
      };

      const {
        addItems: parsedAddItems,
        updateItems: parsedUpdateItems,
        deleteItems: parsedDeleteItems,
      } = submitCanvassSchema.parse(submitPayload);

      appendItemsToFormData(formData, parsedAddItems, FORM_KEYS.ADD_ITEM);
      appendItemsToFormData(formData, parsedUpdateItems, FORM_KEYS.UPDATE_ITEM);
      appendItemsToFormData(formData, parsedDeleteItems, FORM_KEYS.DELETE_ITEM);

      await submitCanvassItems(
        {
          data: formData,
          onProgress: progress => {
            setUploadProgress(progress);
          },
        },
        {
          onSuccess: async response => {
            setUploadProgress(0);
            showNotification({
              type: 'success',
              message: 'Canvass items updated successfully',
            });
            fileRef.current?.removeAllFiles();
            noteRef?.current?.clear();
            resetItems();
            resetTempItems();
            closeCanvassModal();
            setSearchQuery('');
            setFilteredTableItems('');
            navigate(`/app/requisition-slip/${id}/canvass/${response.id}`, {
              state: {
                redirected: true,
              },
            });
          },
          onError: error => {
            if (error?.response?.data?.errorCode === 'ITEM_OVER_CANVASS') {
              openCanvassModal(
                MODAL_TYPES.DELETE_ITEM_CONFIRM,
                null,
                error?.response?.data?.description,
              );
            }
            setUploadProgress(0);
          },
        },
      );
    } catch (error) {
      const errorMessage = error?.response?.data?.message || error?.message;
      showNotification({
        type: 'error',
        message: errorMessage,
      });
    }
  };

  const [lastSortedItems, setLastSortedItems] = useState([]);

  const displayItems = useMemo(() => {
    return searchQuery?.trim() ? filteredTableItems : activeTab === 'items' ? nonSteelbarItems : steelbarItems;
  }, [filteredTableItems, nonSteelbarItems, steelbarItems, searchQuery]);

  useEffect(() => {
    if (!currentSort?.length) {
      setLastSortedItems([]);
      return;
    }
    const sorted = sortTableItems(displayItems, currentSort, sortTypeConfig);
    setLastSortedItems(sorted);
  }, [currentSort, searchQuery, addItems, deleteItems]);

  const itemsToShow = useMemo(() => {
    return lastSortedItems?.length ? lastSortedItems : displayItems;
  }, [lastSortedItems, displayItems]);

  const handleSearch = (val = {}) => {
    const { itemName } = val;
    setFilteredTableItems(itemName);
    setSearchQuery(itemName);
  };

  const getDesignatedApprover = approver => {
    const dateRequired = new Date(
      setDefaultDateValue(canvassDetails?.dateRequired),
    ).setHours(0, 0, 0, 0);

    if (approver?.approver?.userLeaves?.length) {
      const found = approver?.approver?.userLeaves.find(leave => {
        const startDate = new Date(leave.startDate).setHours(0, 0, 0, 0);
        const endDate = new Date(leave.endDate).setHours(0, 0, 0, 0);
        return startDate <= dateRequired && dateRequired <= endDate;
      });

      if (!found) return approver?.userId;

      if (found && approver?.altApproverId && approver?.altApproverId) {
        return approver?.altApproverId;
      }
    }
    return approver?.userId;
  };

  const currentUser = approvers.find(approver => {
    return getDesignatedApprover(approver) === user.id;
  });

  let currentUserId = getDesignatedApprover(currentUser);
  const isCanvassApprover = !!currentUser;

  const canvassData = canvassDetails?.canvassRequisitions?.find(
    canvass => canvass.id === parseInt(canvassId),
  );

  const canvassStatus = canvassData?.status;

  const isCanvassApproved = canvassStatus === 'approved';
  const isCanvassRejected = canvassStatus === 'rejected';
  const isCanvassPartial = canvassStatus === 'partially_canvassed';
  const isCanvassDraft = canvassStatus === 'draft';

  const isStatusSubmitted =
    !isCanvassPartial && !isCanvassDraft && !isCreateMode;
  const isStatusRejected = isCanvassRejected && !isCreateMode;
  const isCreateModeOrDraft = isCanvassDraft || isCreateMode;

  const assignedTo = canvassDetails?.assignedTo;
  const assignedToMe = user.id === assignedTo;
  const isRolePurHead = user.role.name === 'Purchasing Head';

  const userCanSubmit =
    (!isStatusSubmitted || isCanvassRejected) &&
    updatePermission &&
    assignedToMe;

  const oneOfApprovers = approvers.some(
    approver => approver.userId === currentUserId,
  );

  const shouldShowApproverModal = shouldShowComponentViaApprovers(
    approvers,
    currentUserId,
  );

  const nextApproverData = getNextApprover(
    approvers,
    currentUserId,
    canvassDetails?.dateRequired,
  );

  const isNextApproverMain =
    (MAIN_LEVELS.includes(nextApproverData?.level) &&
      !nextApproverData?.isAdhoc) ||
    null;

  const canSubmitCanvass =
    isSubmittingCanvassItems ||
    (!addItems.length &&
      !updateItems.length &&
      !deleteItems.length &&
      !hasAttachments &&
      formDisabled);

  const draftSavedDate = canvassData?.updatedAt;

  useEffect(() => {
    if (isSteelbarsOnly)
      setActiveTab('steel-bars');
    else if (isNonSteelbarsOnly)
      setActiveTab('items');
    else
      setActiveTab(activeTab);
  }, [isSteelbarsOnly, isNonSteelbarsOnly]);

  const renderHeader = (permissions, isSteelbarsTable) => {
    return [
      isSteelbarsTable && !isStatusSubmitted &&  { key: 'groupSelect', value: '', hasNoSort: true },
      { key: 'item', value: 'Item', css: 'w-[56%] min-w-[250px] max-w-[750px]', },
      { key: 'unit', value: 'Unit' },
      {
        key: 'quantity',
        value:
          activeTab === 'steel-bars' ? 'Requested Qty/Pcs' : 'Requested Qty',
      },
      isSteelbarsTable && { key: 'weight', value: 'Weight(kg)' },
      { key: 'approvedQty', value: 'Approved Qty' },
      { key: 'remainingGfq', value: 'Remaining GFQ' },
      // !permissions?.isCanvassApprover && {
      //   key: 'canvass',
      //   value: 'Canvass',
      //   hasNoSort: true,
      // },
      { key: 'actions', value: 'Actions', hasNoSort: true },
    ].filter(Boolean);
  };

  const findSuppliers = id => {
    const inAddItems = addItems.find(canvassItem => canvassItem.id === id);
    const inUpdateItems = updateItems.find(
      canvassItem => canvassItem.id === id,
    );

    return inAddItems ?? inUpdateItems;
  };

  const renderTdDesign = (permissions, groupedSteelbars = null) => {
    const { update: updatePerm } = permissions;

    const enableEdit = isNextApproverMain
      ? isStatusSubmitted && updatePermission
      : userCanSubmit;

    return {
      groupSelect: {
        render: data => {
          return (
            <Input
              type="checkbox"
              checked={groupedSteelbars?.includes(data?.id)} 
              onChange={e => {
                e.stopPropagation();
                setGroupedSteelbars({ type: 'TOGGLE', id: data?.id });
              }}
              className={cn(
                'w-4 h-4 text-[#754445] bg-gray-100 border-gray-300 rounded focus:ring-[#754445] focus:ring-2 accent-gray-600 disabled:accent-black',
              )}
              disabled={
                findSuppliers(data.id)?.suppliers?.length ||
                data?.suppliers?.length
              } // local | backend
              key={`index-${data.id ?? data.item}`}
            />
          );
        },
        excludeColExpand: true,
      },
      item: {
        render: data => {
          return (
            <p className="flex text-blue-500 underline text-wrap break-all">
              {data?.item ?? '---'}
            </p>
          );
        },
      },
      unit: {
        render: data => {
          return <p className=' '>{data?.unit?.length > 0 ? data?.unit : '---'}</p>
        }
      },
      approvedQty: {
        render: data => {
          if (isStatusSubmitted && data?.approvedQty) {
            return <p className=' '>{parseFloat(data?.approvedQty).toFixed(3)}</p>
          } else {
            return <p className=' '>---</p>;
          }
        },
      },
      remainingGfq: {
        render: data => {
          return <p className=' '>{data?.remainingGfq ? parseFloat(data?.remainingGfq).toFixed(3) : '---'}</p>
        }
      },
      quantity: {
        render: data => {
          return <p className=' '>{parseFloat(data?.quantity).toFixed(3) ?? '---'}</p>
        }
      },
      // canvass: {
      //   render: data => (
      //     <React.Fragment>
      //       {findSuppliers(data.id)?.suppliers?.length ||
      //       data?.suppliers?.length ? (
      //         <Button
      //           variant="noColor"
      //           className="border border-gray-600"
      //           hover="outline"
      //           disabled={true}
      //         >
      //           Canvass Entered
      //         </Button>
      //       ) : (
      //         <Button
      //           title="Enter Canvass"
      //           variant="outline"
      //           hover="outline"
      //           disabled={
      //             (!assignedToMe && !isCanvassApprover && !updatePerm) ||
      //             isCanvassApproved
      //           }
      //           onClick={() =>
      //             openCanvassModal(
      //               MODAL_TYPES.SUPPLIER_CANVASS,
      //               MODES.CREATE,
      //               data,
      //             )
      //           }
      //         >
      //           Enter Canvass
      //         </Button>
      //       )}
      //     </React.Fragment>
      //   ),
      // },
      weight: {
        render: row => {
          return row?.weight ? Number(row?.weight).toFixed(3) : '---';
        },
      },
      actions: {
        render: ({ isExpanded, ...canvass }, { handleExpand }) => {
          const { id, suppliers } = canvass;

          const canvassItemSuppliers =
            findSuppliers(id)?.suppliers || suppliers;

          canvass = {
            ...canvass,
            suppliers: canvassItemSuppliers,
          };

          return (
            <div
              className="flex gap-x-2 justify-center h-full lg:flex-row flex-col gap-y-4 flex-wrap"
              title="Actions"
            >
              {/* <Button
                type="button"
                title="Edit Canvass Item Suppliers"
                variant="icon"
                hover="highlight"
                icon={PencilIcon}
                className="w-auto h-auto"
                iconSize="ml-0 w-3"
                disabled={!enableEdit || !canvassItemSuppliers?.length}
                onClick={() =>
                  openCanvassModal(
                    MODAL_TYPES.SUPPLIER_CANVASS,
                    MODES.EDIT,
                    canvass,
                  )
                }
              /> */}
              <DropdownMenu
                className='-translate-x-3/4 p-2'
                dropdownConfig={{
                  wrapper: '',
                  default: 'translate-y-4',
                  flip: '-translate-y-32',
                }}
                triggerProps={{
                  type: 'button',
                  title: 'More Actions',
                  variant: 'icon',
                  hover: 'highlight',
                  icon: MoreHorizontalIcon,
                  className: 'w-auto h-auto',
                  iconSize: 'ml-0',
                }}
                menuOptionsDesign={[
                  {
                    label: isStatusSubmitted ? 'View/Edit' : 'Enter Canvass',
                    onClick: () => {
                      openCanvassModal(
                        MODAL_TYPES.SUPPLIER_CANVASS,
                        canvassItemSuppliers?.length
                          ? MODES.VIEW
                          : MODES.CREATE,
                        canvass,
                      );
                    },
                  },
                  {
                    label: 'View Purchase History',
                    onClick: () => {
                      openCanvassModal(
                        MODAL_TYPES.VIEW_PURCHASE_HISTORY,
                        null,
                        canvass,
                      );
                    },
                  },
                ]}
              />
            </div>
          );
        },
        excludeColExpand: true,
      },
    };
  };

  const expandedRowDesign = {
    css: 'bg-gray-100 p-4',
    render: data => {
      const canvassItemSuppliers =
        findSuppliers(data.id)?.suppliers ?? data?.suppliers;

      const enableEdit = isNextApproverMain
        ? isStatusSubmitted && updatePermission
        : userCanSubmit;

      data = {
        ...data,
        suppliers: canvassItemSuppliers,
      };

      // Auto select all suppliers if TOM since it will always be 1 supplier for each canvassed item
      if (isTOM && data.suppliers?.length > 0) {
        // Update visually to mark all checkbox as checked
        data = {
          ...data,
          suppliers: data.suppliers.map(supplier => {
            return {
              ...supplier,
              isSelected: true,
            };
          }),
        };
      }

      return (
        <SupplierDetails
          data={data}
          isSubmitted={isStatusSubmitted}
          approverData={nextApproverData}
          isTransferOfMaterials={isTOM}
          isOFM={isOFMEnabled}
          canEdit={!enableEdit || !canvassItemSuppliers?.length}
          editSupplierDetails={(index) => openCanvassModal(
              MODAL_TYPES.SUPPLIER_CANVASS,
              MODES.EDIT,
              data,
              index
          )}
        />
      );
    },
  };

  const screenModalProps = {
    isOpen: canvassModalState.type === MODAL_TYPES.SCREEN_MODAL,
    baseOnClose: closeCanvassModal,
    baseOnConfirm: () => {
      setGroupedSteelbars({ type: 'RESET'})
      closeCanvassModal();
    },
    portal: 'app-content',
    header: {
      title: 'Item Group Canvass',
    },
  };


  if (
    isFetchingCanvassDetails ||
    isFetchingCanvassApprovers ||
    isFetchingCanvassItems
  ) {
    return <Spinner className="mx-auto" />;
  }

  return (
    <div className={canvassModalState.type === MODAL_TYPES.SCREEN_MODAL ? 'hidden' : ''}>
      <React.Fragment>
        <div className="relative" ref={elementRef}></div>

        {isCanvassApprover && nextApproverData !== null && (
          <PendingAction
            shouldShowApproverModal={shouldShowApproverModal}
            approvers={approvers}
            onAddApprover={addApproverOnList}
            className={cn({
              'fixed z-30 top-20 right-0 px-14': isSticky,
            })}
          />
        )}

        <div className="flex flex-col gap-y-4">
          {isCreateModeOrDraft && updatePermission && assignedToMe && (
            <div className="xl:absolute xl:top-8 xl:right-8 text-sm">
              <div className="flex flex-wrap flex-col xl:flex-row xl:items-center gap-2 xl:space-x-2">
                {isCanvassDraft && !isCreateMode && (
                  <React.Fragment>
                    <div className="flex items-center space-x-1">
                      <span className="font-bold text-[#219653]">
                        Draft Saved:
                      </span>
                      <span>
                        {draftSavedDate
                          ? formatDateToDMY(draftSavedDate)
                          : 'N/A'}
                      </span>
                      <div className="hidden xl:block">|</div>
                      <span>
                        {draftSavedDate
                          ? formatDateToLocalTime(draftSavedDate)
                          : 'N/A'}
                      </span>
                    </div>
                  </React.Fragment>
                )}
                <Button
                  variant="outline"
                  hover="outline"
                  type="button"
                  className="w-full sm:w-auto min-w-[6rem]"
                  onClick={() => openCanvassModal(MODAL_TYPES.SAVE_DRAFT)}
                  disabled={canSubmitCanvass}
                >
                  Save Draft
                </Button>
              </div>
            </div>
        )}

          <hr />
          <CanvassDetails
            canvassDetails={canvassDetails}
            isCreateMode={isCreateMode}
          />
          <hr />

          <div
            className={cn({
              'grid grid-cols-1 xl:grid-cols-6 gap-4': isStatusSubmitted,
            })}
          >
            {/* Main Section */}
            <div className="xl:col-span-5">
              <div className="flex flex-col gap-y-4">
                <div className="text-xl font-bold">Item/s for Canvassing</div>
                {/* {!isCreateMode && (
                <SearchBar
                  ref={searchBarRef}
                  className={cn('p-0 bg-transparent', {
                    'mb-4': isStatusSubmitted,
                  })}
                  searchItems={searchItems}
                  onSearch={handleSearch}
                />
              )} */}

                {steelbarItems?.length && nonSteelbarItems?.length ? (
                  <div className="my-6">
                    <Tabs activeTab={activeTab} tabs={tabs} />
                  </div>
                ) : null}

              {!isStatusSubmitted &&
                createPermission &&
                assignedToMe && (
                  <div className='flex justify-end gap-x-2 mb-2'>
                    {activeTab !== 'items' && groupedSteelbars.length > 1 && <Button
                        variant="outline"
                        hover="outline"
                        className="w-fit"
                        onClick={() => {
                        openCanvassModal(MODAL_TYPES.SCREEN_MODAL, null, itemsToShow.filter(item => 
                          groupedSteelbars.includes(item?.id)
                        )
                        )
                      }}
                      >
                        Item Group Canvass
                    </Button>}

                    <Button
                      icon={AddIcon}
                      iconPosition="L"
                      className="w-fit"
                      onClick={() =>
                        openCanvassModal(MODAL_TYPES.ADD_ITEM_CANVASS)
                      }
                    >
                      Add Item/s
                    </Button>
                  </div>
                )}
              </div>

              {activeTab === 'items' ? (
                <Pagination
                  total={nonSteelbarItems?.length || 0}
                  setPage={setPage}
                  asHOC={true}
                  maxPerPage={currentLimit}
                >
                  {({ currentPage, limit }) => (
                    <Table
                      headers={renderHeader({
                        ...permissions,
                        isCanvassApprover,
                      })}
                      data={itemsToShow}
                      tdDesign={renderTdDesign(permissions)}
                      expandedRowDesign={expandedRowDesign}
                      expandByRow={false}
                      expandedRowFunction={updateIsExpandedOnSupplier}
                      isLoading={isFetchingCanvassItems}
                      onSort={setSort}
                      currentSort={currentSort}
                      page={currentPage}
                      limit={limit}
                      dynamicHeight={true}
                    alignment='left'
                    />
                  )}
                </Pagination>
              ) : (
                <Pagination
                  total={steelbarItems?.length || 0}
                  setPage={setPage}
                  asHOC={true}
                  maxPerPage={currentLimit}
                >
                  {({ currentPage, limit }) => (
                    <Table
                      headers={renderHeader(
                        {
                          ...permissions,
                          isCanvassApprover,
                        },
                        true,
                      )}
                      data={itemsToShow}
                      tdDesign={renderTdDesign(permissions, groupedSteelbars)}
                      expandedRowDesign={expandedRowDesign}
                      expandByRow={false}
                      expandedRowFunction={updateIsExpandedOnSupplier}
                      isLoading={isFetchingCanvassItems}
                      onSort={setSort}
                      currentSort={currentSort}
                      page={currentPage}
                      limit={limit}
                      dynamicHeight={true}
                      alignment='left'
                    />
                  )}
                </Pagination>
              )}
            </div>

            {/* Status Section */}
            {isStatusSubmitted && (
              <div className="space-y-4 xl:col-span-1">
                <StatusSection status={canvassStatus} />
                <AssignedStaffSection canvassDetails={canvassDetails} />
                <ApproversSection
                  approvers={approvers}
                  dateRequired={canvassDetails?.dateRequired}
                  shouldShowApproverModal={shouldShowApproverModal}
                  onAddApprover={addApproverOnList}
                  onDeleteApprover={deleteApproverOnList}
                  assignedTo={assignedTo}
                />
              </div>
            )}
          </div>

          {(oneOfApprovers || assignedToMe) && (
            <SectionWrapper>
              <p className="font-bold text-xl mb-4">Attachments and Notes</p>

              <Form
                hasErrorSpace={false}
                onSubmit={submitCanvassForm}
                options={{
                  shouldUnregister: true,
                }}
                watchFields={['comment']}
                ref={submitFormRef}
              >
                {({ control, disabled }) => {
                  useEffect(() => {
                    setFormDisabled(disabled);
                  }, [disabled]);

                  return (
                    <div className="grid grid-cols-2 gap-x-6 mb-2">
                      <div className="col-span-1 flex flex-col gap-y-4">
                        {!isCreateMode && (
                          <Button
                            type="button"
                            onClick={() =>
                              openCanvassModal(
                                MODAL_TYPES.ATTACHMENT,
                                null,
                                'attachments',
                              )
                            }
                            icon={FileIcon}
                            iconSize="h-5 w-auto"
                            variant="outline"
                            hover="outline"
                          >
                            Check Attachments
                          </Button>
                        )}
                        <div className="flex flex-col">
                          <div className="relative">
                            {attachmentsData?.hasNewNotifications && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8] sm:absolute -top-1 right-0 text-center">
                                New Attachment/s
                              </Pill>
                            )}

                            {isStatusSubmitted ? (
                              <FileUpload
                                name={'attachments'}
                                modelType={MODELS.CANVASS}
                                ref={fileRef}
                                setIsUploading={setIsUploading}
                                onAttachmentChange={val =>
                                  setHasAttachments(!!val.length)
                                }
                              />
                            ) : (
                              <File
                                isUploading={isSubmittingCanvassItems}
                                ref={fileRef}
                                control={control}
                                name="attachments"
                                hasAttachments={setHasAttachments}
                                inputLabel={'Select Attachment/s'}
                              />
                            )}
                          </div>
                          {hasAttachments &&
                            isSubmittingCanvassItems &&
                            !isStatusSubmitted && (
                              <div className="flex items-center">
                                <div className="text-sm mr-2 font-semibold">
                                  {uploadProgress}%
                                </div>
                                <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
                                  <div
                                    className="bg-blue-500 h-full transition-all duration-200 ease-out"
                                    style={{ width: `${uploadProgress}%` }}
                                  />
                                </div>
                              </div>
                            )}

                          <div
                            className={cn(
                              'flex justify-end flex-col sm:flex-row',
                              {
                                'justify-between': !isStatusSubmitted,
                              },
                            )}
                          >
                            {!isStatusSubmitted && (
                              <span className="text-[#4F4F4F] text-[12px]">
                                The maximum size for each file is 25 MB. File
                                formats - PNG, JPG, JPEG, PDF, Excel, CSV.
                              </span>
                            )}

                            {isStatusSubmitted && (
                              <Button
                                className="w-fit self-end"
                                variant="outline"
                                hover="outline"
                                type="button"
                                onClick={() =>
                                openCanvassModal(MODAL_TYPES.SUBMIT_ATTACHMENT)
                                }
                                disabled={
                                  isUploading ||
                                  isSubmittingAttachments ||
                                  !hasAttachments
                                }
                                isLoading={isUploading}
                              >
                                Submit
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="col-span-1 flex flex-col gap-y-4">
                        {!isCreateMode && (
                          <Button
                            type="button"
                            onClick={() =>
                              openCanvassModal(MODAL_TYPES.NOTES, null, 'notes')
                            }
                            icon={CommentIcon}
                            iconSize="h-5 w-auto"
                            variant="outline"
                            hover="outline"
                          >
                            Check Notes
                          </Button>
                        )}

                        <div className="flex flex-col">
                          <div className="relative">
                            {notesData?.hasNewNotifications && (
                              <Pill className="text-[#EB5757] bg-[#FCE9D8] sm:absolute -top-1 right-0 text-center">
                                New Comment/s
                              </Pill>
                            )}

                            <TextArea
                              label="Notes"
                              name="comment"
                              control={control}
                              placeholder="Input notes here"
                              textLeft={true}
                              ref={noteRef}
                            />
                          </div>

                          {isStatusSubmitted && (
                            <Button
                              className="w-fit self-end"
                              variant="outline"
                              hover="outline"
                              type="button"
                              disabled={isSubmittingNotes}
                              isLoading={isSubmittingNotes}
                              onClick={() =>
                              openCanvassModal(MODAL_TYPES.SUBMIT_NOTES, null, {
                                    isOneOfApprovers: oneOfApprovers,
                              })
                              }
                            >
                              Submit
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                }}
              </Form>
            </SectionWrapper>
          )}

          <div className="flex flex-col gap-y-4">
            <hr />
            <div
              className={cn('flex gap-x-2 justify-between', {
                'justify-end': !isCreateModeOrDraft,
              })}
            >
            {isCreateModeOrDraft &&
              updatePermission &&
              assignedToMe && (
                <Button
                  className="w-fit min-w-24"
                  variant="outline"
                  hover="outline"
                  type="button"
                  disabled={canSubmitCanvass}
                  onClick={() => openCanvassModal(MODAL_TYPES.SAVE_DRAFT)}
                >
                  Save Draft
                </Button>
              )}

              {userCanSubmit && (
                <div className="flex gap-x-2 ml-auto self-end">
                  <Button
                    className="w-fit min-w-24"
                    variant="outline"
                    hover="outline"
                    type="button"
                    disabled={canSubmitCanvass}
                    onClick={() => openCanvassModal(MODAL_TYPES.CANCEL_SUBMIT)}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="w-fit min-w-24"
                    type="button"
                    onClick={() => openCanvassModal(MODAL_TYPES.CONFIRM_SUBMIT)}
                    disabled={isSubmittingCanvassItems}
                  >
                    Submit
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        <AttachmentAndNotesModal
          isOpen={
            canvassModalState.type === MODAL_TYPES.ATTACHMENT ||
            canvassModalState.type === MODAL_TYPES.NOTES
          }
          onClose={closeCanvassModal}
          type={canvassModalState.data}
          model={MODELS.CANVASS}
          modelId={canvassId}
        />

        <ItemGroupSelectionModal
          screenModalProps={screenModalProps}
          isTransferOfMaterials={isTOM}
          isOFM={isOFMEnabled}
          data={canvassModalState.data}
          tableProps={{
            resetPage: resetPage,
            setPage: setPage,
            currentLimit: currentLimit,
            onSort: setSort,
            currentSort: currentSort,
          }}
        />

        <ConfirmModal
          isOpen={canvassModalState.type === MODAL_TYPES.SUBMIT_ATTACHMENT}
          onClose={closeCanvassModal}
          onConfirm={() => handleSubmitAttachment()}
          isLoading={isUploading || isSubmittingAttachments}
          header="Submit Attachment"
        />

        <ConfirmModal
          isOpen={canvassModalState.type === MODAL_TYPES.SUBMIT_NOTES}
          onClose={closeCanvassModal}
          onConfirm={() => handleCreateNotes(canvassModalState.data)}
          isLoading={isSubmittingNotes}
          header="Submit Comment"
        />

        <ConfirmModal
          isOpen={canvassModalState.type === MODAL_TYPES.CONFIRM_SUBMIT}
          onClose={closeCanvassModal}
          onConfirm={() => submitFormRef.current.requestSubmit()}
          isLoading={isSubmittingCanvassItems}
          header={
            hasIncompleteCanvassQuantity
              ? 'Submit Canvass Form - Inc'
              : 'Submit Canvass Form'
          }
          message={
            hasIncompleteCanvassQuantity
              ? 'It seems that you have incomplete item quantity. Make sure all items are correct. Press submit if you still want to proceed with this action.'
              : 'You are about to submit this request. Make sure all items are correct. Press submit if you want to proceed with this action.'
          }
        />

        <ConfirmModal
          isOpen={canvassModalState.type === MODAL_TYPES.SAVE_DRAFT}
          onClose={closeCanvassModal}
          onConfirm={() => submitFormRef.current.requestSubmit({ asDraft: true })}
          isLoading={isSubmittingCanvassItems}
          header="Save Draft"
          message="You are about to save this request as a draft. Make sure all items are correct. Press save if you want to proceed with this action."
        />

        <ActionModal
          header="Item Already Fulfilled"
          onClose={closeCanvassModal}
          isOpen={canvassModalState.type === MODAL_TYPES.DELETE_ITEM_CONFIRM}
        >
          <div className="flex flex-col justify-between gap-y-4 text-sm">
            <p>
              The following item has already been fulfilled and will be removed
              from your canvass sheet:
            </p>

            <ul className="list-disc list-outside pl-8 inline-flex flex-col gap-y-2">
              {canvassModalState.type === MODAL_TYPES.DELETE_ITEM_CONFIRM &&
                canvassModalState.data?.map(item => (
                  <li key={item.id}>{item.itemName}</li>
                ))}
            </ul>

            <Button
              type="button"
              onClick={() => handleOverQtyCanvassItems(canvassModalState?.data)}
            >
              Got It!
            </Button>
          </div>
        </ActionModal>

        <ActionModal
          isOpen={canvassModalState.type === MODAL_TYPES.CANCEL_SUBMIT}
          onClose={closeCanvassModal}
          header="Cancel Canvass Form"
        >
          <div className="flex flex-col space-y-4">
            <span>
            You are about to cancel this request. Press continue if you want to
            proceed with this action.
            </span>
            <div className="grid grid-cols-2 gap-x-2">
              <Button
                type="button"
                className="w-full col-span-1"
                onClick={closeCanvassModal}
                disabled={isUploading || isSubmittingAttachments}
                isLoading={isUploading}
              >
                Cancel
              </Button>

              {!isCreateModeOrDraft && userCanSubmit && (
                <Button
                  type="button"
                  className="w-full col-span-1"
                  variant="outline"
                  hover="outline"
                  onClick={() => submitFormRef.current.requestSubmit()}
                  disabled={isSubmittingCanvassItems}
                >
                  Submit
                </Button>
              )}

              {isCreateModeOrDraft && !userCanSubmit && (
                <Button
                  type="button"
                  className="w-full col-span-1"
                  variant="outline"
                  hover="outline"
                  onClick={() =>
                    submitFormRef.current.requestSubmit({ asDraft: true })
                  }
                  disabled={canSubmitCanvass}
                  isLoading={isUploading}
                >
                  Save Draft
                </Button>
              )}

              <Button
                type="button"
                className="w-full col-span-1"
                variant="outline"
                hover="outline"
                onClick={() => window.location.reload()}
              >
                Continue
              </Button>
            </div>
          </div>
        </ActionModal>

        {!isFetchingCanvassItems && assignedToMe && (
          <AddItemModal
            isOpen={canvassModalState.type === MODAL_TYPES.ADD_ITEM_CANVASS}
            onConfirm={() => searchBarRef?.current?.clear()}
            onClose={closeCanvassModal}
            setPage={setPage}
            currentPage={currentPage}
            currentLimit={currentLimit}
            purchaseOrderIds={canvassDetails?.cancelledPurchaseOrders}
          />
        )}

        <EnterCanvassModal
          isOpen={canvassModalState.type === MODAL_TYPES.SUPPLIER_CANVASS}
          onClose={closeCanvassModal}
          mode={canvassModalState.mode}
          data={canvassModalState.data}
          permissions={permissions}
          isStatusSubmitted={isStatusSubmitted}
          isStatusRejected={isStatusRejected}
          isApprover={oneOfApprovers}
          isAssignedToMe={assignedToMe}
          isNextApproverMain={isNextApproverMain}
          isTransferOfMaterials={isTOM}
          isOFM={isOFMEnabled}
          openOnTab={canvassModalState.activeTab}
        />

        <PurchaseHistoryModal
          isOpen={canvassModalState.type === MODAL_TYPES.VIEW_PURCHASE_HISTORY}
          onClose={closeCanvassModal}
          data={canvassModalState.data}
          sortBy={sortBy}
          setSort={setSort}
          currentSort={currentSort}
          currentLimit={currentLimit}
          currentPage={currentPage}
          setPage={setPage}
          isOFM={isOFMEnabled}
        />
      </React.Fragment>
    </div>
  );
};

export { CanvassManagement };
