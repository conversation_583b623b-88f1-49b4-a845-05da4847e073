import { useQuery } from '@tanstack/react-query';
import { api } from '@lib/apiClient';

export const getCanvassItemList = async ({ id, canvassId }) => {
  return await api.get(`/v1/requisitions/${id}/canvass-item-list`, {
    params: {
      canvassRequisitionId: canvassId,
    },
  });
};

export const useGetCanvassItemList = ({ id, canvassId }, config = {}) => {
  const query = useQuery({
    queryKey: ['canvass_item_list', id],
    queryFn: () => getCanvassItemList({ id, canvassId }),
    enabled: !!id,
    staleTime: 0,
    select: data => {
      const { data: selectedData } = data;
      const tableItems = selectedData.map(item => {
        const { id, quantity, itemDetails, isSelected } = item || {};

        return {
          id,
          item:
            itemDetails?.itmDes || itemDetails?.name || itemDetails?.itemName,
          itemId: itemDetails?.id,
          accountCode: itemDetails?.acctCd,
          unit: itemDetails?.unit,
          quantity,
          isSteelbars: itemDetails?.isSteelbars,
          steelbarDetails: itemDetails?.steelbarDetails,
          status: 'new',
          selected: isSelected,
          isEligibleForCanvassing: item?.isEligibleForCanvassing,
          remainingGfq: itemDetails?.remainingGfq,
        };
      });
      return tableItems;
    },
    ...config,
  });

  return query;
};
