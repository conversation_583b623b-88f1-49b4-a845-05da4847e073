import * as z from 'zod';
import { usePermissionStore } from '@store';
import { useMemo } from 'react';

const createNavigation = permissions => {
  if (!permissions) {
    return {
      mainNavigation: [],
      adminNavigation: [],
    };
  }

  const DropdownItemSchema = z.object({
    label: z.string(),
    path: z.string(),
  });

  const NavItemSchema = z.object({
    label: z.union([z.string(), z.any()]),
    hasDropdown: z.boolean(),
    path: z.string().optional(),
    dropdownItems: z.array(DropdownItemSchema).optional(),
  });

  const NavigationSchema = z.object({
    mainNavigation: z.array(NavItemSchema),
    adminNavigation: z.array(NavItemSchema),
  });

  const {
    roles,
    users,
    companies,
    projects,
    suppliers,
    departments,
    approvers,
    dashboard,
    dashboard_history,
    canvass,
    orders,
    payments,
    delivery,
    ofm_items,
    ofm_lists,
    ofm_history,
    non_ofm_items,
    history,
  } = permissions;

  const rolesView = roles?.view;
  const usersView = users?.view;
  const companiesView = companies?.view;
  const projectsView = projects?.view;
  const suppliersView = suppliers?.view;
  const departmentsView = departments?.view;
  const approversView = approvers?.view;
  const dashboardView = dashboard?.view;
  const dashboardCreate = dashboard?.create;
  const ofmItemsView = ofm_items?.view;
  const ofmListsView = ofm_lists?.view;
  const ofmHistoryView = ofm_history?.view;
  const nonOfmItemsView = non_ofm_items?.view;
  const nonOfmItemsCreate = non_ofm_items?.create;
  const historyView = history?.view;

  const userManagementPath = '/app/admin/user-management';

  const navigationConfig = {
    mainNavigation: [
      dashboardView && {
        label: 'Dashboard',
        hasDropdown: false,
        path: '/app/dashboard',
      },
      (ofmItemsView || nonOfmItemsView || ofmListsView) && {
        label: 'Items',
        hasDropdown: true,
        dropdownItems: [
          ofmItemsView && { label: 'OFM', path: '/app/items/ofm' },
          ofmListsView && { label: 'OFM List', path: '/app/items/ofm-list' },
          nonOfmItemsView && { label: 'Non-OFM', path: '/app/items/non-ofm' },
        ].filter(Boolean),
      },
      (suppliersView || companiesView || departmentsView || projectsView) && {
        label: 'Manage',
        hasDropdown: true,
        dropdownItems: [
          suppliersView && {
            label: 'Suppliers',
            path: '/app/manage/suppliers',
          },
          companiesView && { label: 'Company', path: '/app/manage/company' },
          departmentsView && {
            label: 'Department',
            path: '/app/manage/department',
          },
          projectsView && { label: 'Project', path: '/app/manage/project' },
        ].filter(Boolean),
      },
      usersView && {
        label: 'Admin',
        hasDropdown: true,
        dropdownItems: [
          { label: 'User Management', path: userManagementPath },
        ],
      },
    ].filter(Boolean),
    adminNavigation: [
      {
        label: 'IT Admin Management',
        hasDropdown: false,
        path: userManagementPath,
      },
    ],
  };

  const parsedNavigation = NavigationSchema.safeParse(navigationConfig);

  if (!parsedNavigation.success) {
    throw new Error(
      `Invalid navigation config provided.
The following properties are missing or invalid:
${Object.entries(parsedNavigation.error.flatten().fieldErrors)
  .map(([k, v]) => `- ${k}: ${v}`)
  .join('\n')}
`,
    );
  }

  return parsedNavigation.data;
};

export const useNavigation = () => {
  const permissions = usePermissionStore(state => state.permissions);

  return useMemo(() => createNavigation(permissions), [permissions]);
};
