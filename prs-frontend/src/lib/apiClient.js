import axios from 'axios';
import { env } from '@config/env';
import { useTokenStore, useUserStore, usePermissionStore } from '@store';

function authRequestInterceptor(config) {
  const { getState } = useTokenStore;

  const token = getState().token;
  if (!config.headers.has('Authorization') && token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  if (!config.headers.Accept) {
    config.headers.Accept = 'application/json';
  }
  return config;
}

export const api = axios.create({
  baseURL: env.API_URL,
});

api.interceptors.request.use(authRequestInterceptor);
api.interceptors.response.use(
  response => {
    if (response.config.responseType === 'blob') {
      return response;
    }

    return response.data;
  },
  error => {
    const { setState } = useTokenStore;
    const { setState: setUserState } = useUserStore;
    const { setState: setPermissionState } = usePermissionStore;

    if (error.response && error.response.status === 401) {
      setState({
        token: null,
        type: null,
        refreshToken: null,
        expiredAt: null,
      });
      setUserState({ user: null, otp: null, secret: null, currentRoute: null });
      setPermissionState({ permissions: null });
      sessionStorage.removeItem('timeoutState');
    } else {
      console.error('API Error:', error);
    }

    return Promise.reject(error);
  },
);
