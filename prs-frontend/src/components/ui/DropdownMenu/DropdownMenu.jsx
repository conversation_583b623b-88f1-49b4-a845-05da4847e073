import React, { useRef, useState } from 'react';
import { cn } from '@utils/cn';
import { Button } from '@src/components/ui/Button';
import PropTypes from 'prop-types';
import { useOutsideClick } from '@src/hooks/useOutsideClick';
import { useFlipPlacement } from '@src/hooks/useFlipPlacement';
import { useMenuKeyboardNavigation } from '@src/hooks/useMenuKeyboardNavigation';
function DropdownTrigger({ triggerLabel, triggerProps, onTrigger }) {
    return (
        <Button 
            onClick={onTrigger}
            {...triggerProps}
        >
            {triggerLabel}
        </Button>
    )
}

export function DropdownMenu({ 
    className = '', 
    triggerLabel = '',
    triggerProps,
    menuOptionsDesign = [],
    dropdownConfig = {
        wrapper: 'relative inline-block',
        default: 'top-full mt-2',
        flip: 'bottom-full mb-2',
    },}) {

    const [isOpen, setIsOpen] = useState();
    const triggerRef = useRef(null);

    const onSelect = idx => {
        setIsOpen(false);
        menuOptionsDesign[idx]?.onClick();
    }

    const {
        highlightedIndex,
        containerRef,
        handleKeyDown,
        handleMouseMove,
        onItemMouseEnter,
        ariaAttrs,
        idPrefix
      } = useMenuKeyboardNavigation({
        isOpen,
        itemCount: menuOptionsDesign.length,
        onSelect,
      });

    const placement = useFlipPlacement({
        triggerRef: triggerRef,
        menuRef: containerRef,
        offset: 8,
        isOpen: isOpen
    });

    useOutsideClick(containerRef, ()=> setIsOpen(!isOpen), isOpen);

    return (
        <React.Fragment>
            <div className={cn(dropdownConfig.wrapper)}>
            <DropdownTrigger 
                triggerLabel={triggerLabel}
                triggerProps={{ ref: triggerRef, ...triggerProps }}
                onTrigger={() => setIsOpen(!isOpen)}
            />

            {isOpen && (
                <div
                    {...ariaAttrs}
                    ref={containerRef}
                    onKeyDown={handleKeyDown}
                    onMouseMove={handleMouseMove}
                    className={cn(
                        'absolute z-10 flex flex-col p-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-[30vh] overflow-y-scroll no-scrollbar',
                        placement === 'bottom' 
                        ? dropdownConfig.default 
                        : dropdownConfig.flip,
                        className,
                    )}
                >
                    {menuOptionsDesign.map((item, idx) => (
                        <div
                            key={idx}
                            title={''}
                            id={`${idPrefix}${idx}`}
                            onMouseEnter={() => onItemMouseEnter(idx)}
                            onClick={() => { 
                                item?.disabled || onSelect(idx) }}
                            className={cn(
                                'flex w-full px-4 py-2 text-sm ',
                                { 'bg-gray-200': highlightedIndex === idx && !item?.disabled },
                                item?.disabled ? 'select-none cursor-not-allowed text-gray-300' : 'cursor-pointer text-gray-700',
                                item?.css,
                            )}
                        >
                            {item?.children ?? item?.label ?? '---'}
                        </div>
                    ))}
                </div>
                )   
            }
            </div>
        </React.Fragment>
    )
}

DropdownMenu.propTypes = {
    className: PropTypes.string,
    triggerLabel: PropTypes.string,
    triggerProps: PropTypes.object,
    menuOptionsDesign: PropTypes.arrayOf(
        PropTypes.shape({
          label: PropTypes.string,
          css: PropTypes.string,
          onClick: PropTypes.func,
          children: PropTypes.node,
          disabled: PropTypes.bool,
        }),
      ),
    dropdownConfig: PropTypes.shape({
        default: PropTypes.string,
        flip: PropTypes.string,
    }),
}