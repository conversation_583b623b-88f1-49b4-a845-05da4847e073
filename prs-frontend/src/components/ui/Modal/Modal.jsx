import React from 'react';
import XIcon from '@assets/icons/xicon.svg?react';
import { Button } from '../Button';
import { cn } from '@src/utils/cn';
import { cva } from 'class-variance-authority';
import PropTypes from 'prop-types';

const sizes = {
  default: 'default',
  medium: 'medium',
  small: 'small',
};

const visibility = {
  low: 'low',
  medium: 'medium',
  high: 'high',
};

const modalVariants = cva(
  'bg-white rounded-2xl shadow-xl z-40 flex flex-col w-[70rem] min-h-64 max-h-full',
  {
    variants: {
      size: {
        [sizes.default]: 'w-[70rem]',
        [sizes.medium]: 'w-[70rem] h-auto bg-[#614A4C]',
        [sizes.small]: 'w-[35rem] min-h-40 h-auto bg-[#614A4C]',
      },
      visibility: {
        [visibility.low]: 'z-30',
        [visibility.medium]: 'z-40',
        [visibility.high]: 'z-50',
      },
    },
    defaultVariants: {
      size: sizes.default,
      visibility: visibility.medium,
    },
  },
);

const visibilityVariants = cva(
  'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 w-full h-full py-4',
  {
    variants: {
      visibility: {
        [visibility.low]: 'z-30',
        [visibility.medium]: 'z-40',
        [visibility.high]: 'z-50',
      },
    },
    defaultVariants: {
      visibility: visibility.medium,
    },
  },
);

const Modal = ({
  isOpen = true,
  onClose,
  header,
  headerClassName,
  size = 'default',
  visibility = 'medium',
  className,
  children,
  hasCloseButton = true,
}) => {
  if (!isOpen) {
    return null;
  }

  const handleBackdropClick = e => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };
  return (
    <div
      className={cn(visibilityVariants({ visibility }))}
      onClick={handleBackdropClick}
    >
      <div
        className={cn(
          modalVariants({ size }),
          { 'p-8': size === sizes.default },
          className,
        )}
      >
        {size === sizes.default ? (
          <>
            <Button
              onClick={onClose}
              className="w-fit self-end"
              icon={XIcon}
              iconSize="w-2.5 h-2.5"
              variant="noColor"
              hover="darkGlow"
            />
            <p className={cn("text-black font-bold text-2xl", headerClassName)}>{header}</p>
          </>
        ) : size === sizes.medium ? (
          <div className="relative text-center text-white font-bold text-lg">
            <p className={cn("flex-grow h-full bg-[#795C5F] p-4 rounded-t-2xl", headerClassName)}>
              {header}
            </p>
            <Button
              onClick={onClose}
              icon={XIcon}
              className={cn('w-fit absolute right-3 top-4 text-white', {
                hidden: !hasCloseButton,
              })}
              iconSize="w-2.5 h-2.5"
              variant="noColor"
              hover="darkGlow"
            />
          </div>
        ) : (
          <div className="relative text-center text-white font-bold text-lg">
            <p className={cn("flex-grow h-full bg-[#795C5F] p-4 rounded-t-2xl", headerClassName)}>
              {header}
            </p>

            <Button
              onClick={onClose}
              icon={XIcon}
              className={cn('w-fit absolute right-3 top-4 text-white', {
                hidden: !hasCloseButton,
              })}
              iconSize="w-2.5 h-2.5"
              variant="noColor"
              hover="darkGlow"
            />
          </div>
        )}
        <div
          className={cn({
            'bg-white rounded-b-2xl flex flex-col p-6 text-wrap text-left space-y-6':
              size === sizes.small || size === sizes.medium,
          })}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

Modal.propTypes = {
  isOpen: PropTypes.bool,
  onClose: PropTypes.func,
  header: PropTypes.string,
  size: PropTypes.oneOf(Object.values(sizes)),
  className: PropTypes.string,
  children: PropTypes.node,
  hasCloseButton: PropTypes.bool,
};

export { Modal };
