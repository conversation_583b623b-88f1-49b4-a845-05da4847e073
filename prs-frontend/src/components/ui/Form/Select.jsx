import React, { useEffect, useState, useRef, forwardRef, useMemo } from 'react';
import { useController } from 'react-hook-form';
import { cn } from '@utils/cn';
import ChevronDown from '@assets/icons/down.svg?react';
import Xicon from '@assets/icons/xicon.svg?react';
import { Button } from '@components/ui/Button';
import { FieldWrapper } from './FieldWrapper';
import { useOutsideClick } from '@hooks/useOutsideClick';
import { useInView } from 'react-intersection-observer';
import useKeyboardAccessibility from '@src/hooks/useKeyboardAccessibility';
import PropTypes from 'prop-types';

const Select = forwardRef(
  (
    {
      name,
      control,
      options = [],
      defaultValue,
      className,
      cva,
      label,
      error,
      defaultLabel = '',
      disabled,
      includeBlankOption = false,
      searchable = false,
      clearable = false,
      onLoadMore,
      handleChange = () => {},
      hasMore = false,
      onSearch,
      onChange,
      ...props
    },
    ref,
  ) => {
    const { field } = useController({ name, control, defaultValue });
    const [isOpen, setIsOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

    const dropdownRef = useRef(null);

    const { ref: loadMoreRef, inView } = useInView({
      threshold: 0.5,
    });

    const filteredOptions = useMemo(() => {
      if (debouncedSearchQuery.trim() === '') {
        return options; // No search query, return all options
      }

      if (onSearch) {
        return onSearch(debouncedSearchQuery); // external search function pagination
      }

      // Local filtering logic
      return options.filter(option =>
        option.key.toLowerCase().includes(debouncedSearchQuery.toLowerCase()),
      );
    }, [debouncedSearchQuery, options, onSearch]);

    const handleSearchChange = e => {
      setSearchQuery(e.target.value); // Controlled input
    };

    useEffect(() => {
      if (inView && hasMore && onLoadMore) {
        onLoadMore();
      }
    }, [inView, hasMore, onLoadMore]);

    useEffect(() => {
      if (defaultValue !== undefined) {
        field.onChange(defaultValue);
      }
    }, [defaultValue]);

    const handleSelect = option => {
      field.onChange(option);
      if (onChange) {
        onChange(option, name);
      }
      setIsOpen(false);
      handleChange(option);
    };

    useOutsideClick(dropdownRef, () => setIsOpen(false), isOpen);

    const selectedLabel =
      options.find(option => option.value === field.value)?.key ||
      defaultLabel ||
      'Select an option';

    const {
      highlightedIndex,
      setHighlightedIndex,
      handleKeyDown,
      handleSearchKeyDown,
      listRef,
      ariaId,
    } = useKeyboardAccessibility(
      filteredOptions,
      debouncedSearchQuery,
      'select-option-',
      handleSelect,
      isOpen,
      searchable,
    );

    useEffect(() => {
      const timer = setTimeout(() => setDebouncedSearchQuery(searchQuery), 500);
      setHighlightedIndex(0);
      return () => clearTimeout(timer); // Clear timer on cleanup
    }, [searchQuery]);

    useEffect(() => {
      if (!isOpen) {
        setSearchQuery('');
        setDebouncedSearchQuery('');
        setHighlightedIndex(0);
      }
    }, [isOpen]);

    useEffect(() => {
      setHighlightedIndex(0);
    }, [filteredOptions]);

    const handleToggle = e => {
      e.preventDefault();
      e.stopPropagation();
      setSearchQuery('');
      setDebouncedSearchQuery('');
      setIsOpen(prev => !prev);
      if (!isOpen) {
        onSearch?.('');
      } // Reset search term when opening
    };

    const handleClear = e => {
      e.preventDefault();
      e.stopPropagation();
      handleSelect(null);
    };

    return (
      <div className="relative" ref={dropdownRef}>
        <FieldWrapper label={label} error={error} hasErrorBelow={false}>
          <div
            className={cn(
              cva,
              'border border-input rounded-md',
              {
                'border-red-500 ring-1 ring-red-500 ring-opacity-50': error,
              },
              className,
            )}
          >
            <Button
              className={cn(
                '[&>span]:w-full [&>span]:flex [&>span]:items-center [&>span]:justify-between [&>span]:whitespace-nowrap px-3 py-2 font-semibold text-gray-800 disabled:bg-gray-200 disabled:rounded-none',
                {
                  'font-normal text-gray-400': !field.value,
                },
              )}
              onClick={handleToggle}
              ref={ref}
              variant="noColor"
              hover="darkGlow"
              disabled={disabled}
              {...props}
            >
              <span className="w-full text-wrap text-left">{selectedLabel}</span>
              <div className="">
                {field.value && !disabled && clearable ? (
                  <Xicon
                    className="h-3 w-3"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClear(e);
                    }}
                  />
                ) : (
                  <ChevronDown />
                )}
              </div>
            </Button>
          </div>
        </FieldWrapper>

        {isOpen && (
          <div className="absolute w-full bg-white border rounded-md shadow-lg z-10">
            {searchable && (
              <div className="p-2">
                <input
                  type="text"
                  onChange={handleSearchChange}
                  onKeyDown={handleSearchKeyDown}
                  placeholder="Search..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring focus:ring-indigo-200"
                />
              </div>
            )}

            <ul
              className="py-1 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200"
              role="listbox"
              tabIndex={0}
              ref={listRef}
              onKeyDown={handleKeyDown}
              aria-activedescendant={`${ariaId}${highlightedIndex}`}
            >
              {includeBlankOption && (
                <li key="no-value-option">
                  <Button
                    variant="noColor"
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 font-normal hover:bg-gray-200 rounded-sm"
                    onClick={e => {
                      e.preventDefault();
                      handleSelect(null);
                    }}
                    disabled={field.value === null}
                  >
                    {defaultLabel}
                  </Button>
                </li>
              )}
              {filteredOptions.length !== 0 ? (
                filteredOptions.map((option, index) => (
                  <li
                    key={option.value}
                    id={`${ariaId}${index}`}
                    role="option"
                    aria-selected={highlightedIndex === index}
                    onMouseEnter={() => setHighlightedIndex(index)}
                    ref={index === options.length - 2 ? loadMoreRef : null}
                  >
                    <Button
                      variant="noColor"
                      className={cn(
                        'block w-full text-left px-4 py-2 text-sm text-gray-700 font-normal hover:bg-gray-200 rounded-sm',
                        { 'bg-gray-200': highlightedIndex === index }, // Highlight styling
                      )}
                      onClick={e => {
                        e.preventDefault();
                        handleSelect(option.value);
                      }}
                      disabled={field.value === option.value}
                    >
                      {option.key}
                    </Button>
                  </li>
                ))
              ) : (
                <li key="no-value-option">
                  <Button
                    variant="noColor"
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 font-normal hover:bg-gray-200 rounded-sm"
                    onClick={e => {
                      e.preventDefault();
                      handleSelect(option.value);
                    }}
                    disabled={true}
                  >
                    No matching results
                  </Button>
                </li>
              )}

              {hasMore && (
                <li className="text-center py-2 text-sm text-gray-500">
                  Loading more...
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    );
  },
);

Select.displayName = 'Select';
Select.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object,
  options: PropTypes.array,
  defaultValue: PropTypes.any,
  className: PropTypes.string,
  cva: PropTypes.object,
  label: PropTypes.string,
  error: PropTypes.object,
  defaultLabel: PropTypes.string,
  disabled: PropTypes.bool,
  includeBlankOption: PropTypes.bool,
  searchable: PropTypes.bool,
  onLoadMore: PropTypes.func,
  hasMore: PropTypes.bool,
  onChange: PropTypes.func,
  onSearch: PropTypes.func,
};

export { Select };
