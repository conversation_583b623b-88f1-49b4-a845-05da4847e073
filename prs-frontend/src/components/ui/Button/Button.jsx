import PropTypes from 'prop-types';
import { cva } from 'class-variance-authority';
import React, { forwardRef } from 'react';

import { cn } from '@utils/cn';
import { Spinner } from '@components/ui/Spinner';
import { FieldWrapper } from '../Form';

const buttonVariants = cva(
  'w-full inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 transition duration-300 rounded-xl py-2 px-4',
  {
    variants: {
      variant: {
        submit: 'bg-[#754445] text-white',
        secondary: 'bg-[#445475] text-white hover:bg-[#4F5F8B]',
        outline: 'text-[#754445] border border-[#754445]',
        danger: 'text-[#EB5757] border border-[#EB5757]',
        back: 'flex w-10 h-10 rounded-lg bg-gray-100 shadow-sm hover:bg-gray-200',
        action: 'bg-gray-200 rounded-lg font-semibold',
        icon: 'w-auto bg-sky-500 text-white rounded-lg font-semibold',
        paginationSelected:
          'flex items-center justify-center rounded-xl bg-white text-[#306F4E] border border-[#306F4E]',
        paginationNormal:
          'flex items-center justify-center rounded-xl bg-white text-gray-500 border border-gray-200',
        noColor: 'bg-transparent text-black',
        file: 'text-gray-500 bg-white border border-gray-200 font-normal',
        link: 'text-blue-500 underline underline-offset-4 font-semibold p-0 m-0',
      },
      hover: {
        submit: 'hover:bg-[#8B4F4F]',
        action: 'hover:bg-slate-200 hover:text-gray-800',
        highlight: 'hover:bg-sky-400 hover:text-gray-100',
        danger: 'hover:text-[#F58A8A] hover:border-[#F58A8A]',
        pagination:
          'hover:border-1 hover:border-[#306F4E] hover:text-[#306F4E] hover:bg-[#f3fff8]',
        darkGlow: 'hover:text-gray-800',
        outline: 'hover:text-[#8B4F4F] hover:border-[#8B4F4F] hover:bg-rose-50',
        file: 'hover:border-gray-200 hover:bg-gray-50',
        link: 'hover:text-blue-600',
      },
    },
    defaultVariants: {
      variant: 'submit',
      hover: 'submit',
    },
  },
);

const Button = forwardRef(
  (
    {
      className,
      variant,
      hover,
      size,
      as: Component = 'button',
      children,
      isLoading,
      icon: Icon,
      iconPosition = 'R',
      iconSize,
      label = '',
      ...props
    },
    ref,
  ) => {
    const buttonContent = (
      <Component
        className={cn(buttonVariants({ variant, hover }), className)}
        ref={ref}
        {...props}
      >
        {isLoading && <Spinner size="sm" className="text-current mr-2" />}
        {Icon && iconPosition === 'L' && (
          <Icon className={cn('mr-2 h-4 w-4', iconSize)} />
        )}
        {children && (
          <span className="truncate overflow-hidden">{children}</span>
        )}
        {Icon && iconPosition !== 'L' && (
          <Icon className={cn('ml-2 h-4 w-4', iconSize)} />
        )}
      </Component>
    );

    return !!label ? (
      <FieldWrapper label={label}>{buttonContent}</FieldWrapper>
    ) : (
      buttonContent
    );
  },
);

Button.displayName = 'Button';

Button.propTypes = {
  className: PropTypes.string,
  variant: PropTypes.oneOf([
    'submit',
    'secondary',
    'outline',
    'back',
    'action',
    'icon',
    'paginationSelected',
    'paginationNormal',
    'noColor',
    'file',
    'link',
    'danger',
  ]),
  hover: PropTypes.oneOf([
    'submit',
    'action',
    'highlight',
    'pagination',
    'darkGlow',
    'outline',
    'file',
    'link',
    'danger',
  ]),
  size: PropTypes.string,
  as: PropTypes.elementType,
  children: PropTypes.node,
  isLoading: PropTypes.bool,
  icon: PropTypes.elementType,
  iconPosition: PropTypes.oneOf(['L', 'R']),
  iconSize: PropTypes.string,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
};

export { Button };
