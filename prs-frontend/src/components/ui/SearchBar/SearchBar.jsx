import React, {
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Button } from '@components/ui/Button';
import Filter from '@assets/icons/filter.svg?react';
import { SectionWrapper } from '@src/components/layouts/SectionWrapper';
import { cn } from '@src/utils/cn';
import { Form, Input, Select } from '@components/ui/Form';
import PropTypes from 'prop-types';

export const SearchBar = forwardRef(function SearchBar(
  {
    searchItems = [
      {
        type: 'text',
        label: 'Search',
        name: 'search',
        isParent: true,
      },
    ],
    onClear,
    onSearch,
    className,
    subClassName,
    setPage,
    error,
    validateInput,
  },
  ref,
) {
  const formRef = useRef(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useImperativeHandle(ref, () => ({
    clear: () => {
      if (formRef.current) {
        formRef.current.resetForm();
        onClear?.();
        onSearch({});
      }
    },
  }));

  const handleSearch = values => {
    const searchValue = values[mainSearchItem.name];
    onSearch(values);
    if (validateInput && validateInput(searchValue)) {
      return;
    }
    setPage?.(1);
  };

  const handleClear = reset => {
    onSearch({});
    reset();
    onClear?.();
    setPage?.(1);
  };

  const mainSearchItem = searchItems?.find(item => item.isParent);
  const subSearchItems = searchItems?.filter(item => !item.isParent);
  return (
    <SectionWrapper
      className={cn(
        'w-full mx-auto p-4 space-y-4 bg-white rounded-2xl',
        className,
      )}
    >
      <Form
        ref={formRef}
        onSubmit={handleSearch}
        options={{
          shouldUnregister: true,
        }}
        hasErrorSpace={false}
      >
        {({ register, control, reset, formState }) => (
          <div className="flex flex-col space-y-2">
            <div className="flex flex-col md:flex-row items-end w-full gap-x-2">
              <div className="flex-grow w-full md:w-auto">
                {mainSearchItem?.type === 'text' ? (
                  <Input
                    type="text"
                    {...mainSearchItem}
                    isSearch={true}
                    registration={register(mainSearchItem.name)}
                    className="w-full bg-white"
                    error={error ? error : null}
                    hasErrorBelow={true}
                  />
                ) : (
                  <Select
                    searchable={true}
                    key={item.name}
                    {...item}
                    control={control}
                    options={[{ key: '1', value: 1 }]}
                  />
                )}
              </div>
              <div className="flex w-full md:w-auto gap-x-2">
                {!!subSearchItems.length && (
                  <Button
                    type="button"
                    variant="action"
                    hover="action"
                    onClick={() => setIsExpanded(!isExpanded)}
                    icon={Filter}
                    iconSize={cn('ml-0 h-4 transition-transform', {
                      'rotate-180': isExpanded,
                    })}
                    className="w-fit min-w-auto sm:min-w-4 py-2.5"
                  />
                )}

                <Button
                  type="submit"
                  variant="submit"
                  hover="submit"
                  className="transition-colors w-fit py-2.5"
                >
                  Search
                </Button>
                <Button
                  type="button"
                  variant="action"
                  hover="action"
                  className="w-fit py-2.5"
                  onClick={() => handleClear(reset)}
                  disabled={!Object.values(formState.dirtyFields).length}
                >
                  Clear
                </Button>
              </div>
            </div>
            <p className="text-red-500">{error ? error : null}</p>
            {isExpanded && (
              <div
                className={cn(
                  'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2',
                  subClassName,
                )}
              >
                {subSearchItems?.map(item => {
                  if (item.type === 'select') {
                    return (
                      <Select
                        searchable={true}
                        key={item.name}
                        {...item}
                        control={control}
                        options={item.options || []}
                      />
                    );
                  }
                  return (
                    <Input
                      key={item.name}
                      {...item}
                      registration={register(item.name)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        )}
      </Form>
    </SectionWrapper>
  );
});

SearchBar.propTypes = {
  searchItems: PropTypes.arrayOf(
    PropTypes.shape({
      type: PropTypes.string,
      label: PropTypes.string,
      name: PropTypes.string,
      isParent: PropTypes.bool,
      placeholder: PropTypes.string,
      options: PropTypes.arrayOf(
        PropTypes.shape({
          key: PropTypes.string,
          value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        }),
      ),
    }),
  ),
  onClear: PropTypes.func,
  onSearch: PropTypes.func,
  className: PropTypes.string,
  subClassName: PropTypes.string,
  setPage: PropTypes.func,
  validateInput: PropTypes.func,
  error: PropTypes.string,
};
