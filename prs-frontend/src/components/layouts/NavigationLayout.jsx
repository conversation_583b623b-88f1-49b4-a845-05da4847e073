import React, { useState, createContext, useContext } from 'react';
import PropTypes from 'prop-types';
import { cn } from '@src/utils/cn';

import { useNavigate } from 'react-router-dom';
// import { Breadcrumb } from '@components/ui/Breadcrumb';
import { useLogout } from '@lib/auth';
import { useUserStore } from '@store';
import { useNavigation } from '@config/navigation';
import { useGetNotifications } from '@features/notification/api';
import { NotificationBell } from '@features/notification/components';
import { Link } from 'react-router-dom';
import Burger from '@assets/icons/burger.svg?react';
import DownNav from '@assets/icons/downnav.svg?react';
import cityLandLogo from '@assets/images/cityland-logo.png?react';
import { useQueryClient } from '@tanstack/react-query';

const NavigationContext = createContext(null);

const Logo = () => (
  <Link to="/app/dashboard">
    <div className="flex items-center justify-start w-auto">
        <img
          src={cityLandLogo}
          alt="Purchase Request System"
          className="h-8 w-auto mr-4"
        />
        <h1 className="text-xl font-semibold text-burgundy-600">
            Purchase Request System
        </h1>
    </div>
  </Link>
);

const NavItem = React.memo(
  ({
    label,
    dropdownLabel,
    hasDropdown,
    dropdownItems,
    isOpen,
    isMobile,
    onClick,
    onDropdownItemClick,
    path,
  }) => {
    const handleClick = () => {
      if (hasDropdown) {
        onClick();
      } else if (path) {
        onDropdownItemClick(path);
      }
    };

    return (
      <div className={cn('relative', { 'w-full': isMobile })}>
        <button
          className={cn(
            'px-3 py-2.5 flex items-center hover:text-burgundy-600',
            {
              'bg-[#985859] rounded-2xl': isOpen && hasDropdown,
              'justify-between': isMobile,
            },
          )}
          onClick={handleClick}
        >
          <div
            className={cn('flex flex-row items-center', {
              'space-x-4': !isMobile,
            })}
          >
            <span
              className={cn('text-[#742627] font-semibold', {
                'text-white !important': isOpen && hasDropdown,
              })}
            >
              {label}
            </span>
            {hasDropdown && (
              <DownNav
                className={cn(
                  'ml-1 w-2 h-2 cursor-pointer transition-transform fill-[#742627]',
                  {
                    'transform rotate-180 fill-white': isOpen,
                    'ml-4': isMobile,
                  },
                )}
              />
            )}
          </div>
        </button>
        {hasDropdown && isOpen && (
          <div
            className={cn(
              'text-sm bg-white shadow-md py-2 rounded-xl w-48 z-10 text-center text-[#684448]',
              {
                'h-fit absolute sm:left-1/2 sm:transform sm:-translate-x-1/2 sm:mt-6 mt-2':
                  !isMobile,
                'w-full mt-1': isMobile,
              },
            )}
          >
            <div className="font-bold mb-2 h-8 flex items-center justify-center">
              {dropdownLabel || label}
            </div>
            <hr className="mx-4" />
            <ul>
              {dropdownItems?.map((item, index) => (
                <li key={index} className="cursor-pointer">
                  <a
                    onClick={() =>
                      item?.onClick
                        ? item.onClick()
                        : onDropdownItemClick(item.path)
                    }
                    className="block px-4 py-2.5 text-sm text-[#684448] hover:bg-gray-100"
                  >
                    {item.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  },
);

NavItem.propTypes = {
  label: PropTypes.node.isRequired,
  dropdownLabel: PropTypes.node,
  hasDropdown: PropTypes.bool.isRequired,
  dropdownItems: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.node.isRequired,
      path: PropTypes.string,
      onClick: PropTypes.func,
    }),
  ),
  isOpen: PropTypes.bool.isRequired,
  isMobile: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  onDropdownItemClick: PropTypes.func.isRequired,
  path: PropTypes.string,
};

NavItem.displayName = 'NavItem';

const MainNavigation = () => {
  const {
    navigationItems,
    openDropdown,
    handleDropdownClick,
    handleNavItemClick,
  } = useContext(NavigationContext);

  return (
    <nav className="hidden lg:flex flex-grow mx-1 justify-center">
      <ul className="flex justify-center space-x-6">
        {navigationItems.map((item, index) => (
          <NavItem
            key={index}
            {...item}
            isOpen={openDropdown === index}
            onClick={() => handleDropdownClick(index)}
            onDropdownItemClick={handleNavItemClick}
          />
        ))}
      </ul>
    </nav>
  );
};

const MobileNavigation = () => {
  const {
    isMenuOpen,
    navigationItems,
    openDropdown,
    userNav,
    userDropdownOpen,
    notifications,
    notificationCount,
    handleDropdownClick,
    handleNavItemClick,
    handleUserDropdownClick,
  } = useContext(NavigationContext);

  if (!isMenuOpen) return null;

  return (
    <div className="lg:hidden mt-4 mb-2 px-4">
      <nav className="flex flex-col space-y-4">
        <ul className="flex flex-col space-y-2">
          {navigationItems.map((item, index) => (
            <NavItem
              key={index}
              {...item}
              isOpen={openDropdown === index}
              onClick={() => handleDropdownClick(index)}
              onDropdownItemClick={handleNavItemClick}
              isMobile={true}
            />
          ))}
        </ul>
        <div className="flex items-center justify-between pt-4 border-t">
          {userNav?.map((item, index) => (
            <NavItem
              key={index}
              {...item}
              dropdownLabel="Settings"
              isOpen={userDropdownOpen}
              onClick={handleUserDropdownClick}
              onDropdownItemClick={handleNavItemClick}
              isMobile={true}
            />
          ))}
          <NotificationBell
            count={notificationCount}
            notifications={notifications?.data}
          />
        </div>
      </nav>
    </div>
  );
};

function NavigationLayout({ children, paths }) {
  const [openDropdown, setOpenDropdown] = useState(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);

  const { data: notifications } = useGetNotifications({});
  const queryClient = useQueryClient();
  const navigation = useNavigation();

  const logout = useLogout();
  const { user } = useUserStore();
  const navigate = useNavigate();

  const isAdmin = user?.role?.id === 1;
  const navigationItems = isAdmin
    ? navigation.adminNavigation
    : navigation.mainNavigation;

  const handleUserDropdownClick = () => {
    setUserDropdownOpen(!userDropdownOpen);
    setOpenDropdown(null);
  };

  const handleLogout = async () => {
    try {
      await logout.mutateAsync();
      setUserDropdownOpen(false);
      navigate('/login', { replace: true });
      queryClient.clear();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleNavItemClick = pathOrCallback => {
    if (typeof pathOrCallback === 'function') {
      pathOrCallback();
    } else if (pathOrCallback) {
      navigate(pathOrCallback);
    }
    setIsMenuOpen(false);
    setUserDropdownOpen(false);
  };

  const notificationCount = notifications?.isNotViewedCount;
  const contextValue = {
    navigationItems,
    openDropdown,
    isMenuOpen,
    userDropdownOpen,
    notificationCount,
    notifications,
    userNav: [
      {
        label: (
          <div className="flex items-center text-left space-x-4 mr-6 text-xs">
            <div>
              <p
                className={cn('font-bold text-[#420001]', {
                  'text-inherit': userDropdownOpen,
                })}
              >
                {user?.firstName} {user?.lastName}
              </p>
              <p
                className={cn('font-normal text-gray-800', {
                  'text-gray-100': userDropdownOpen,
                })}
              >
                {user?.role?.name}
              </p>
            </div>
          </div>
        ),
        hasDropdown: true,
        dropdownItems: [
          { label: 'My Profile', path: '/app/profile' },
          { label: 'Logout', onClick: handleLogout },
        ],
      },
    ],
    handleDropdownClick: index => {
      setOpenDropdown(openDropdown === index ? null : index);
      setUserDropdownOpen(false);
    },
    handleNavItemClick,
    handleUserDropdownClick,
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      <div className="bg-white">
        <div className="mx-auto px-4 sm:px-6 lg:px-10 fixed w-full z-20 bg-white border-b-[1px] border-b-[#0f172a1a]">
          <div className="flex items-center justify-between py-3">
            <Logo />
            <button
              className="lg:hidden block text-gray-600 focus:outline-none"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <Burger className="w-6 h-6" />
            </button>
            <MainNavigation />
            <div className="hidden lg:flex items-center space-x-10">
              <NotificationBell
                count={notificationCount}
                notifications={notifications?.data}
              />
              {contextValue.userNav.map((item, index) => (
                <NavItem
                  key={index}
                  {...item}
                  dropdownLabel="Settings"
                  isOpen={userDropdownOpen}
                  onClick={handleUserDropdownClick}
                  onDropdownItemClick={handleNavItemClick}
                />
              ))}
            </div>
          </div>
          <MobileNavigation />
        </div>
        <div className="flex px-4 sm:px-6 lg:px-10 bg-[#F7F8FB]">
          {/* <Breadcrumb paths={paths} /> */}
        </div>
        {children}
      </div>
    </NavigationContext.Provider>
  );
}

NavigationLayout.propTypes = {
  children: PropTypes.node.isRequired,
  paths: PropTypes.arrayOf(PropTypes.string).isRequired,
};

export { NavigationLayout };
