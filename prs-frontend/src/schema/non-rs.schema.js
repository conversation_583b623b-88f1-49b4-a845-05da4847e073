import z from 'zod';
import {
  emojiRegex,
  alphanumericAllSymbolsRegex,
} from '@utils/regexUtils';
import { setDefaultDateValue } from '@utils/dateFormat';

const CATEGORIES = ['company', 'association', 'project'];
const CHARGE_TO_CATEGORIES = ['company', 'association', 'project', 'supplier'];

const DISCOUNT_TYPES = ['fixed', 'percent'];

const baseItemSchema = z.object({
  id: z
    .preprocess(
      value => {
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? value : parsed;
      },
      z
        .number()
        .nonnegative({ message: 'ID must be a positive number' })
        .optional(),
    )
    .optional(),
  name: z
    .string()
    .min(1, 'Item name is required')
    .max(100, 'Item name must not exceed 100 characters')
    .refine(
      val => {
        if (val) {
          for (const match of val.matchAll(emojiRegex)) {
            return !match;
          }
        }
        return z.NEVER;
      },
      {
        message: 'Emojis are not allowed.',
      },
    )
    .refine(
      value => alphanumericAllSymbolsRegex.test(value),
      'Item name can only contain alphanumeric characters and special characters',
    ),
  unit: z
    .string({
      required_error: 'Unit is required',
      invalid_type_error: 'Unit must be a string',
    })
    .min(1, 'Unit is required')
    .max(100, 'Unit must not exceed 100 characters')
    .refine(val => !emojiRegex.test(val || ''), {
      message: 'Emojis are not allowed.',
    })
    .refine(value => alphanumericAllSymbolsRegex.test(value || ''), {
      message: 'Unit can only contain alphanumeric characters and special characters',
    }),
    quantity: z.preprocess(
    (val) => {
      if (typeof val === 'string' && val.trim() !== '') {
        const parsed = parseFloat(val);
        return isNaN(parsed) ? val : parsed;
      }
      return val;
    },
    z.number({
        required_error: 'Quantity is required',
        invalid_type_error: 'Quantity must be a number',
      })
      .min(0, 'Quantity must be greater than or equal to 0')
      .max(99999.999, 'Quantity must not exceed 99999.999')
      .transform(val => Number(val.toFixed(3))),
    ),
  amount: z.preprocess(
    (val) => {
      if (typeof val === 'string' && val.trim() !== '') {
        const parsed = parseFloat(val);
        return isNaN(parsed) ? val : parsed;
      }
      return val;
    },
    z.number({
        required_error: 'Amount is required',
        invalid_type_error: 'Amount must be a number',
      })
      .min(0, 'Amount must be greater than or equal to 0')
      .max(9999999999.99, 'Amount must not exceed 9999999999.99')
      .transform(val => Number(val.toFixed(2))),
    ),
});

const discountFields = {
  discountType: z.enum(DISCOUNT_TYPES),
  discountValue: z.preprocess(
    (val) => {
      if (typeof val === 'string' && val.trim() !== '') {
        const parsed = parseFloat(val);
        return isNaN(parsed) ? val : parsed;
      }
      return val;
    },
    z.number({
        required_error: 'Discount is required',
        invalid_type_error: 'Discount must be a number',
      })
      .min(0, 'Discount must be greater than or equal to 0')
      .transform(val => Number(val.toFixed(2))),
    ),
};

const discountSchema = z.object(discountFields).superRefine((data, ctx) => {
  if (data.discountType === 'percent' && data.discountValue > 100) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Percentage discount cannot exceed 100%',
      path: ['discountValue'],
    });
  }
});

const nonRSItemSchema = z
  .object({
    ...baseItemSchema.shape,
    ...discountFields,
  })
  .superRefine((data, ctx) => {
    if (data.discountType === 'percent' && data.discountValue > 100) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Percentage discount cannot exceed 100%',
        path: ['discountValue'],
      });
    }
  });

const attachmentSchema = z.object({
  attachmentIds: z.array(
    z
      .number({ required_error: 'Attachment not found' })
      .nonnegative({ message: 'Invalid attachments, please reupload' }),
  ),
  invoiceAttachmentIds: z.array(
    z
      .number({ required_error: 'Invoice Attachment not found' })
      .nonnegative({ message: 'Invalid invoice attachments, please reupload' }),
  ),
});

const nonRSCreationSchema = z
  .object({
    id: z
      .preprocess(
        value => {
          const parsed = parseInt(value, 10);
          return isNaN(parsed) ? value : parsed;
        },
        z
          .number()
          .nonnegative({ message: 'ID must be a positive number' })
          .optional(),
      )
      .optional(),

    category: z.enum(CATEGORIES, {
      message: 'Category is required',
      required_error: 'Category is required',
    }),

    companyId: z.preprocess(
      (val) => (val === null || val === undefined ? '' : val),
      z.string()
        .min(1, 'Company is required')
        .max(10, 'Company must not exceed 10 characters')
    ),

    projectId: z
      .string()
      .max(50, 'Project must not exceed 50 characters')
      .nullable()
      .optional(),

    departmentId: z.preprocess(
      (val) => (val === null || val === undefined ? '' : val),
      z.string()
        .min(1, 'Department is required')
        .max(10, 'Department must not exceed 10 characters')
    ),

    supplierId: z.preprocess(
      (val) => (val === null || val === undefined ? '' : val),
      z.string()
        .min(1, 'Supplier is required')
        .max(10, 'Supplier must not exceed 10 characters')
    ),

    payableTo: z
      .string()
      .min(1, 'Payable To is required')
      .max(100, 'Payable To must not exceed 100 characters')
      .regex(
        alphanumericAllSymbolsRegex,
        "Payable To must contain only alphanumeric and symbol characters.",
      )
      .refine(
        val => {
          if (val) {
            for (const match of val.matchAll(emojiRegex)) {
              return !match;
            }
          }

          return z.NEVER;
        },
        {
          message: 'Emojis are not allowed.',
        },
      ),

    invoiceNo: z
      .string()
      .min(1, 'Invoice Number is required')
      .max(100, 'Invoice Number must not exceed 100 characters')
      .regex(
        alphanumericAllSymbolsRegex,
        'Invoice Number must contain only alphanumeric and symbol characters.',
      ).refine(
        val => {
          if (val) {
            for (const match of val.matchAll(emojiRegex)) {
              return !match;
            }
          }

          return z.NEVER;
        },
        {
          message: 'Emojis are not allowed.',
        },
      ),

    invoiceDate: z.preprocess(
      value => {
        if (value === null || value === undefined || String(value).trim() === '') {
          return undefined;
        }
        const parsed = new Date(value);
        return isNaN(parsed.getTime()) ? value : parsed;
      },
      z
      .date({
        required_error: 'Invoice date is required',
        invalid_type_error: 'Invoice date must be a valid date',
      })
      .refine(
        date => {
          const today = new Date().setHours(0, 0, 0, 0);
          const inputDate = new Date(date.toISOString().split('T')[0]).setHours(0, 0, 0, 0);
          return inputDate <= today;
        },  
        { message: 'Invoice date must be today or a previous date' },
      )
      .transform(value => setDefaultDateValue(value)),
    ),

    supplierInvoiceAmount: z.preprocess(
      value => {
        if (value === null || value === undefined || String(value).trim() === '') {
          return undefined;
        }
        const parsed = parseFloat(value); 
        return isNaN(parsed) ? undefined : parsed;
      },
      z
        .number({
          required_error: 'Supplier Invoice Amount is required',
          invalid_type_error: 'Supplier Invoice Amount must be a number',
        })
        .min(0, 'Supplier Invoice Amount must be greater than or equal to 0')
        .max(9999999999.99, 'Supplier Invoice Amount must not exceed 9999999999.99')
        .nonnegative({ message: 'Supplier Invoice Amount must be a positive number' }),
    ),

    groupDiscountType: z.preprocess(
      (val) => (val === "" || val === null ? undefined : val),
      z
        .enum(DISCOUNT_TYPES, { invalid_type_error: "Invalid discount type" })
        .optional()
    ),

    groupDiscountPrice: z.preprocess(
      value => {
        if (value === null || String(value).trim() === "") return undefined;
        const parsed = +parseFloat(value, 10);
        return isNaN(parsed) ? undefined : parsed;
      },
      z
        .number({ invalid_type_error: 'Group discount must be a number' })
        .min(0, 'Group discount must be greater than or equal to 0')
        .transform(val => (val != null ? Number(val.toFixed(2)) : val))
        .nullable()
        .optional(),
    ),

    chargeTo: z.preprocess(
      (val) => (val === "" || val === null ? undefined : val),
      z
        .enum(CHARGE_TO_CATEGORIES, { invalid_type_error: "Invalid charge to category" })
        .optional()
    ),

    chargeToId: z.preprocess(
      value => {
        if (value === null || String(value).trim() === "") return undefined;
        const parsed = +parseFloat(value, 10);
        return isNaN(parsed) ? undefined : parsed;
      },
      z
        .number({ invalid_type_error: 'Charge to id must be a number' })
        .min(0, 'Charge to id must be greater than or equal to 0')
        .transform(val => (val != null ? Number(val.toFixed(2)) : val))
        .nullable()
        .optional(),
    ),

    notes: z
      .string()
      .max(100, 'Notes must not exceed 100 characters')
      .refine(
        val => {
          if (val) {
            for (const match of val.matchAll(emojiRegex)) {
              return !match;
            }
          }

          return z.NEVER;
        },
        {
          message: 'Emojis are not allowed.',
        },
      )
      .optional()
      .transform(val => (val?.trim() === '' ? undefined : val)),

    invoiceNotes: z
      .string()
      .max(100, 'Invoice Notes must not exceed 100 characters')
      .refine(
        val => {
          if (val) {
            for (const match of val.matchAll(emojiRegex)) {
              return !match;
            }
          }

          return z.NEVER;
        },
        {
          message: 'Emojis are not allowed.',
        },
      )
      .optional()
      .transform(val => (val?.trim() === '' ? undefined : val)),
  })
  .extend(attachmentSchema.shape)
  .refine(
    (data) => {
      if (data.category === 'project') {
        return !!data.projectId;
      }
      return true;
    },
    {
      message: "Project is required when category is 'Project'",
      path: ['projectId'], 
    }
  );

const itemListCreationSchema = z.object({
  itemList: z
    .array(nonRSItemSchema)
    .min(1, 'At least one (1) item is required for submission'),
});

const draftItemListCreationSchema = z.object({
  itemList: z.array(nonRSItemSchema),
});

export {
  nonRSCreationSchema,
  nonRSItemSchema,
  itemListCreationSchema,
  draftItemListCreationSchema,
  discountSchema,
};
