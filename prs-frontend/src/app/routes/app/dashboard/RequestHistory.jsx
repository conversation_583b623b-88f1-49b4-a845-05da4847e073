import React from 'react';
import { RequestHistory } from '@features/dashboard/components/RequestHistory';
import AppLayout from '@components/layouts/AppLayout';
import { TableLayout } from '@components/layouts/TableLayout';
import { useNavigate } from 'react-router-dom';

export const RequestHistoryRoute = () => {
  const navigate = useNavigate();
  const customHandleBack = () => {
    navigate('/app/dashboard', { replace: true });
    clearRequisitionItemsStore();
  };

  return (
    <AppLayout
      header="Request History"
      paths={['Home', 'Page', 'Page', 'Page Location']}
      hasBackButton={true}
      customHandleBack={customHandleBack}
      className="relative"
    >
      <TableLayout>
        {paginationProps => <RequestHistory {...paginationProps} />}
      </TableLayout>
    </AppLayout>
  );
};
