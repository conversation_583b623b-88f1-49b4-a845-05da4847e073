import React from 'react';
import { Dashboard_v2 as Dashboard } from '@features/dashboard/components';
import AppLayout from '@components/layouts/AppLayout';
import { TableLayout } from '@components/layouts/TableLayout';
import { withPermissions } from '@hoc';

export const DashboardComponent = ({ perm }) => {
  const { dashboard: dashboardPermission } = perm;
  return (
    <AppLayout
      header="Dashboard"
      text="Welcome to Cityland Purchase Request System (PRS)"
      paths={['Home', 'Page', 'Page', 'Page Location']}
    >
      <TableLayout>
        {paginationProps => (
          <Dashboard {...paginationProps} permissions={dashboardPermission} />
        )}
      </TableLayout>
    </AppLayout>
  );
};

export const DashboardRoute = withPermissions(DashboardComponent);
