import React from 'react';
import { RequisitionSlip } from '@features/dashboard/components/RequisitionSlip';
import AppLayout from '@components/layouts/AppLayout';
import { TableLayout } from '@components/layouts/TableLayout';
import { useNavigate } from 'react-router-dom';
import { useRequisitionItemsStore } from '@src/store/requisitionItemsStore';

export const RequisitionSlipRoute = () => {
  const { clearRequisitionItemsStore } = useRequisitionItemsStore();
  const navigate = useNavigate();
  const customHandleBack = () => {
    navigate('/app/dashboard', { replace: true });
  };

  return (
    <AppLayout
      header="Requisition Slip"
      paths={['Home', 'Page', 'Page', 'Page Location']}
      hasBackButton={true}
      customHandleBack={customHandleBack}
      className="relative"
    >
      <TableLayout>
        {paginationProps => <RequisitionSlip {...paginationProps} />}
      </TableLayout>
    </AppLayout>
  );
};
