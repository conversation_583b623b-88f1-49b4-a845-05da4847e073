import React from 'react';
import { useNavigate } from 'react-router-dom';
import AppLayout from '@components/layouts/AppLayout';
import { TableLayout } from '@components/layouts/TableLayout';
import { NonRSManagement } from '@features/non-rs/components';
import { withPermissions } from '@hoc';

const NonRSManagementComponent = ({ perm }) => {
  // const { canvass: canvassPermissions } = perm;
  const navigate = useNavigate();

  const customHandleBack = () => {
    navigate('/app/dashboard');
  };

  return (
    <AppLayout
      header="Non-RS Payment Request"
      paths={['Home', 'Page', 'Page', 'Page Location']}
      hasBackButton={true}
      className="relative"
      customHandleBack={customHandleBack}
    >
      <TableLayout>
        {paginationProps => (
          <NonRSManagement
            {...paginationProps}
            // permissions={canvassPermissions}
          />
        )}
      </TableLayout>
    </AppLayout>
  );
};

export const NonRSManagementRoute = withPermissions(NonRSManagementComponent);
