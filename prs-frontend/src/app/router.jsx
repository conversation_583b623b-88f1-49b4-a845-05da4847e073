import React, { useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  Navigate,
  RouterProvider,
  createBrowserRouter,
} from 'react-router-dom';
import { ProtectedRoute } from '@lib/auth';
import { AppRoot } from './routes/app/Root';
import { AuthFlowWrapper } from '@components/layouts/AuthFlowWrapper';
import { useUserStore, usePermissionStore } from '@store';
import { hasPermission } from '@utils/permissions';
import { element } from 'prop-types';

const notificationPage = {
  path: 'notifications',
  lazy: async () => {
    const { NotificationRoute } = await import('./routes/app/notification');
    return { Component: NotificationRoute };
  },
};

const adminRoutes = [
  {
    path: '',
    index: true,
    element: <Navigate to="/app/admin/user-management" replace />,
  },
  {
    path: 'profile',
    lazy: async () => {
      const { ProfileManagementRoute } = await import('./routes/app/profile');
      return { Component: ProfileManagementRoute };
    },
  },
  // Admin
  {
    path: 'admin',
    children: [
      {
        path: '',
        index: true,
        element: <Navigate to="/app/admin/user-management" replace />,
      },
      {
        path: 'user-management',
        lazy: async () => {
          const { UserManagementRoute } = await import('./routes/app/user');
          return { Component: UserManagementRoute };
        },
      },
    ],
  },
  notificationPage,
];

const renderRoutes = permissions => {
  if (!permissions) {
    return [{}];
  }

  const {
    roles,
    users,
    companies,
    projects,
    suppliers,
    departments,
    approvers,
    dashboard,
    dashboard_history,
    canvass,
    orders,
    payments,
    delivery,
    ofm_items,
    ofm_lists,
    ofm_history,
    non_ofm_items,
    history,
    invoice,
  } = permissions || {};

  const rolesView = roles?.view;
  const usersView = users?.view;
  const companiesView = companies?.view;
  const projectsView = projects?.view;
  const suppliersView = suppliers?.view;
  const departmentsView = departments?.view;
  const approversView = approvers?.view;
  const dashboardView = dashboard?.view;
  const dashboardHistoryView = dashboard_history?.view;
  const dashboardCreate = dashboard?.create;
  const canvassView = canvass?.view;
  const ordersView = orders?.view;
  const paymentsView = payments?.view;
  const deliveryView = delivery?.view;
  const ofmItemsView = ofm_items?.view;
  const ofmListsView = ofm_lists?.view;
  const ofmHistoryView = ofm_history?.view;
  const nonOfmItemsView = non_ofm_items?.view;
  const nonOfmItemsCreate = non_ofm_items?.create;
  const historyView = history?.view;
  const invoiceView = invoice?.view;

  let manageRedirect = '/app/manage';
  if (suppliersView) {
    manageRedirect += '/suppliers';
  } else if (companiesView) {
    manageRedirect += '/company';
  } else if (departmentsView) {
    manageRedirect += '/department';
  } else if (projectsView) {
    manageRedirect += '/project';
  }

  let nonOfmRedirect = '/app/items';
  if (nonOfmItemsView) {
    nonOfmRedirect += '/non-ofm';
  } else if (ofmItemsView) {
    nonOfmRedirect += '/ofm';
  }

  const userRoutes = [
    {
      path: '',
      children: [
        {
          path: '',
          index: true,
          element: <Navigate to="/app/dashboard" replace />,
        },
        hasPermission(
          {
            path: 'dashboard',
            children: [
              {
                path: '',
                lazy: async () => {
                  const { DashboardRoute } = await import(
                    './routes/app/dashboard'
                  );
                  return { Component: DashboardRoute };
                },
              },
              hasPermission(
                {
                  path: 'create',
                  lazy: async () => {
                    const { CreateRSRoute } = await import(
                      './routes/app/dashboard/CreateRS'
                    );
                    return { Component: CreateRSRoute };
                  },
                },
                dashboardCreate,
              ),
              hasPermission(
                {
                  path: 'create/:id',
                  lazy: async () => {
                    const { CreateRSRoute } = await import(
                      './routes/app/dashboard/CreateRS'
                    );
                    return { Component: CreateRSRoute };
                  },
                },
                dashboardCreate || dashboardView,
              ),
            ],
          },
          dashboardView,
        ),
      ],
    },
    {
      path: 'non-requisition-slip',
      children: [
        {
          path: '',
          index: true,
          element: (
            <Navigate to="/app/dashboard" replace />
          ),
        },
        hasPermission(
          {
            path: 'create',
            lazy: async () => {
              const { NonRSManagementRoute } = await import(
                './routes/app/non-rs'
              );
              return { Component: NonRSManagementRoute };
            },
          },
          dashboardView,
        ),
        hasPermission(
          {
            path: ':id',
            children: [
              {
                path: '',
                lazy: async () => {
                  const { NonRSManagementRoute } = await import(
                    './routes/app/non-rs'
                  );
                  return { Component: NonRSManagementRoute };
                },
              },
              hasPermission(
                {
                  path: 'history',
                  lazy: async () => {
                    const { NonRSHistoryRoute } = await import(
                      './routes/app/non-rs'
                    );
                    return { Component: NonRSHistoryRoute };
                  },
                },
                dashboardView,
              ),
            ],
          },
          dashboardView,
        ),
      ],
    },
    {
      path: 'profile',
      lazy: async () => {
        const { ProfileManagementRoute } = await import('./routes/app/profile');
        return { Component: ProfileManagementRoute };
      },
    },
    {
      path: 'affected-workflow',
      children: [
        {
          path: '',
          index: true,
          element: <Navigate to="/app/dashboard" replace />,
        },
        {
          path: ':id',
          children: [
            {
              path: '',
              lazy: async () => {
                const { AffectedWorkflowRoute } = await import(
                  './routes/app/affected-workflow'
                );
                return { Component: AffectedWorkflowRoute };
              },
            },
          ],
        },
      ],
    },
    notificationPage,
    // Admin
    hasPermission(
      {
        path: 'admin',
        children: [
          {
            path: '',
            index: true,
            element: <Navigate to="/app/admin/user-management" replace />,
          },
          {
            path: 'user-management',
            lazy: async () => {
              const { UserManagementRoute } = await import('./routes/app/user');
              return { Component: UserManagementRoute };
            },
          },
          {
            path: 'approval-management',
            lazy: async () => {
              const { ApprovalManagementRoute } = await import(
                './routes/app/admin/approval-management'
              );
              return { Component: ApprovalManagementRoute };
            },
          },
        ],
      },
      usersView,
    ),
    hasPermission(
      {
        path: 'receiving-report',
        children: [
          {
            path: '',
            index: true,
            lazy: async () => {
              const { DeliveryReceiptRoute } = await import(
                './routes/app/delivery-receipt'
              );
              return { Component: DeliveryReceiptRoute };
            },
          },
          {
            path: 'create/:id', // New path for creating DR with RS number
            lazy: async () => {
              const { DeliveryReceiptRoute } = await import(
                './routes/app/delivery-receipt'
              );
              return {
                Component: props => <DeliveryReceiptRoute {...props} />,
              };
            },
          },
          {
            path: 'create/:id/delivery/:deliveryReceiptId', // double check if in use
            lazy: async () => {
              const { DeliveryReceiptRoute } = await import(
                './routes/app/delivery-receipt'
              );
              return {
                Component: DeliveryReceiptRoute,
              };
            },
          },
          {
            path: ':deliveryReceiptId/requisition-slip/:id',
            lazy: async () => {
              const { DeliveryReceiptRoute } = await import(
                './routes/app/delivery-receipt'
              );
              return {
                Component: DeliveryReceiptRoute,
              };
            },
          },
          {
            path: ':deliveryReceiptId/requisition-slip/:id/history/:itemId',
            lazy: async () => {
              const { ItemHistoryRoute } = await import(
                './routes/app/delivery-receipt'
              );
              return { Component: ItemHistoryRoute };
            },
          },
        ],
      },
      deliveryView,
    ),
    hasPermission(
      {
        path: 'purchase-order',
        children: [
          {
            path: ':id/requisition-slip/:rsId',
            lazy: async () => {
              const { PurchaseOrderLandingRoute } = await import(
                './routes/app/purchase-order'
              );
              return { Component: PurchaseOrderLandingRoute };
            },
          },
        ],
      },
      ordersView,
    ),

    // Manage
    hasPermission(
      {
        path: 'manage',
        children: [
          {
            path: '',
            index: true,
            element: <Navigate to={manageRedirect} replace />,
          },
          hasPermission(
            {
              path: 'suppliers',
              children: [
                {
                  path: '',
                  index: true,
                  lazy: async () => {
                    const { SupplierManagementRoute } = await import(
                      './routes/app/supplier'
                    );
                    return { Component: SupplierManagementRoute };
                  },
                },
                {
                  path: ':id',
                  lazy: async () => {
                    const { SupplierDetailRoute } = await import(
                      './routes/app/supplier'
                    );
                    return { Component: SupplierDetailRoute };
                  },
                },
              ],
            },
            suppliersView,
          ),
          hasPermission(
            {
              path: 'company',
              children: [
                {
                  path: '',
                  index: true,
                  lazy: async () => {
                    const { CompanyManagementRoute } = await import(
                      './routes/app/company'
                    );
                    return { Component: CompanyManagementRoute };
                  },
                },
                {
                  path: ':id',
                  lazy: async () => {
                    const { CompanyDetailsRoute } = await import(
                      './routes/app/company'
                    );
                    return { Component: CompanyDetailsRoute };
                  },
                },
                {
                  path: 'create',
                  lazy: async () => {
                    const { CreateAssociationRoute } = await import(
                      './routes/app/company'
                    );
                    return { Component: CreateAssociationRoute };
                  },
                },
                {
                  path: 'association/:id',
                  lazy: async () => {
                    const { AssociationDetailsRoute } = await import(
                      './routes/app/company'
                    );
                    return { Component: AssociationDetailsRoute };
                  },
                },
                {
                  path: 'association/:id/edit',
                  lazy: async () => {
                    const { AssociationDetailsRoute } = await import(
                      './routes/app/company'
                    );
                    return { Component: AssociationDetailsRoute };
                  },
                },
              ],
            },
            companiesView,
          ),
          hasPermission(
            {
              path: 'department',
              children: [
                {
                  path: '',
                  index: true,
                  lazy: async () => {
                    const { DepartmentManagementRoute } = await import(
                      './routes/app/department'
                    );
                    return { Component: DepartmentManagementRoute };
                  },
                },
                {
                  path: ':id',
                  lazy: async () => {
                    const { DepartmentDetailsRoute } = await import(
                      './routes/app/department'
                    );
                    return { Component: DepartmentDetailsRoute };
                  },
                },
              ],
            },
            departmentsView,
          ),
          hasPermission(
            {
              path: 'project',
              children: [
                {
                  path: '',
                  index: true,
                  lazy: async () => {
                    const { ProjectManagementRoute } = await import(
                      './routes/app/project'
                    );
                    return { Component: ProjectManagementRoute };
                  },
                },
                {
                  path: ':id',
                  lazy: async () => {
                    const { ProjectDetailsRoute } = await import(
                      './routes/app/project'
                    );
                    return { Component: ProjectDetailsRoute };
                  },
                },
              ],
            },
            projectsView,
          ),
        ],
      },
      suppliersView || companiesView || departmentsView || projectsView,
    ),
    // Requisition Slip
    hasPermission(
      {
        path: 'requisition-slip',
        children: [
          {
            path: '',
            index: true,
            element: <Navigate to="/app/dashboard" replace />,
          },
          {
            path: ':id',
            children: [
              {
                path: '',
                index: true,
                lazy: async () => {
                  const { RequisitionSlipRoute } = await import(
                    './routes/app/dashboard/RequisitionSlip'
                  );
                  return { Component: RequisitionSlipRoute };
                },
              },
              // Requisition Slip Canvass
              hasPermission(
                {
                  path: 'canvass',
                  children: [
                    {
                      path: '',
                      index: true,
                      element: <Navigate to="/app/requisition-slip" replace />,
                    },
                    {
                      path: 'create',
                      lazy: async () => {
                        const { CanvassManagementRoute } = await import(
                          './routes/app/canvass'
                        );
                        return { Component: CanvassManagementRoute };
                      },
                    },
                    {
                      path: ':canvassId',
                      lazy: async () => {
                        const { CanvassManagementRoute } = await import(
                          './routes/app/canvass'
                        );
                        return { Component: CanvassManagementRoute };
                      },
                    },
                  ],
                },
                canvassView,
              ),

              hasPermission(
                {
                  path: 'invoice',
                  children: [
                    {
                      path: '',
                      index: true,
                      element: <Navigate to="/app/dashboard" replace />,
                    },
                    {
                      path: 'create',
                      lazy: async () => {
                        const { InvoiceRoute } = await import(
                          './routes/app/invoice'
                        );
                        return { Component: InvoiceRoute };
                      },
                      children: [
                        {
                          path: ':invoiceID',
                          index: true,
                          lazy: async () => {
                            const { InvoiceRoute } = await import(
                              './routes/app/invoice'
                            );
                            return { Component: InvoiceRoute };
                          },
                        },
                      ],
                    },
                    {
                      path: ':invoiceID',
                      index: true,
                      lazy: async () => {
                        const { InvoiceRoute } = await import(
                          './routes/app/invoice'
                        );
                        return { Component: InvoiceRoute };
                      },
                    },
                  ],
                },
                invoiceView,
              ),
              hasPermission(
                {
                  path: 'payment-request',
                  children: [
                    {
                      path: '',
                      index: true,
                      element: <Navigate to="/app/dashboard" replace />,
                    },
                    {
                      path: ':prId',
                      lazy: async () => {
                        const { PaymentRequestRoute } = await import(
                          './routes/app/payment-request'
                        );
                        return { Component: PaymentRequestRoute };
                      },
                    },
                    {
                      path: 'create',
                      lazy: async () => {
                        const { PaymentRequestCreateRoute } = await import(
                          './routes/app/payment-request'
                        );
                        return { Component: PaymentRequestCreateRoute };
                      },
                      children: [
                        {
                          path: ':prId',
                          lazy: async () => {
                            const { PaymentRequestCreateRoute } = await import(
                              './routes/app/payment-request'
                            );
                            return { Component: PaymentRequestCreateRoute };
                          },
                        },
                      ],
                    },
                  ],
                },
                paymentsView,
              ),
            ],
          },
        ],
      },
      dashboardView,
    ),
    hasPermission(
      {
        path: 'request-history/:id',
        lazy: async () => {
          const { RequestHistoryRoute } = await import(
            './routes/app/dashboard/RequestHistory'
          );
          return { Component: RequestHistoryRoute };
        },
      },
      dashboardView && dashboardHistoryView,
    ),
    // Items
    hasPermission(
      {
        path: 'items',
        children: [
          {
            path: '',
            index: true,
            element: <Navigate to={nonOfmRedirect} replace />,
          },
          hasPermission(
            {
              path: 'non-ofm',
              children: [
                {
                  path: '',
                  index: true,
                  lazy: async () => {
                    const { NonOFMManagementRoute } = await import(
                      './routes/app/non-ofm'
                    );
                    return { Component: NonOFMManagementRoute };
                  },
                },
                hasPermission(
                  {
                    path: 'create',
                    lazy: async () => {
                      const { NonOFMCreationRoute } = await import(
                        './routes/app/non-ofm'
                      );
                      return { Component: NonOFMCreationRoute };
                    },
                  },
                  nonOfmItemsCreate,
                ),
                {
                  path: ':id',
                  lazy: async () => {
                    const { NonOFMDetailsRoute } = await import(
                      './routes/app/non-ofm'
                    );
                    return { Component: NonOFMDetailsRoute };
                  },
                },
              ],
            },
            nonOfmItemsView,
          ),
          hasPermission(
            {
              path: 'ofm',
              children: [
                {
                  path: '',
                  index: true,
                  lazy: async () => {
                    const { OFMManagementRoute } = await import(
                      './routes/app/ofm'
                    );
                    return { Component: OFMManagementRoute };
                  },
                },
                {
                  path: ':id',
                  lazy: async () => {
                    const { OFMDetailsRoute } = await import(
                      './routes/app/ofm'
                    );
                    return { Component: OFMDetailsRoute };
                  },
                },
              ],
            },
            ofmItemsView,
          ),
          hasPermission(
            {
              path: 'ofm-list',
              children: [
                {
                  path: '',
                  index: true,
                  lazy: async () => {
                    const { OFMListRoute } = await import(
                      './routes/app/ofm-list'
                    );
                    return { Component: OFMListRoute };
                  },
                },
                {
                  path: ':id',
                  lazy: async () => {
                    const { OFMListDetailsRoute } = await import(
                      './routes/app/ofm-list'
                    );
                    return { Component: OFMListDetailsRoute };
                  },
                },
              ],
            },
            ofmListsView,
          ),
        ],
      },
      ofmItemsView || nonOfmItemsView || ofmListsView,
    ),
  ];

  return userRoutes;
};

export const createAppRouter = (queryClient, availableRoutes) =>
  createBrowserRouter([
    {
      path: '/',
      lazy: async () => {
        const { LandingRoute } = await import('./routes/Landing');
        return { Component: LandingRoute };
      },
    },
    {
      path: 'login',
      lazy: async () => {
        const { LoginRoute } = await import('./routes/auth');
        return { Component: LoginRoute };
      },
    },
    {
      path: '/auth',
      element: (
        <AuthFlowWrapper>
          <AppRoot />
        </AuthFlowWrapper>
      ),
      children: [
        {
          path: '',
          index: true,
          element: <Navigate to="/" replace />,
        },
        {
          path: 'create-password',
          lazy: async () => {
            const { RegisterRoute } = await import('./routes/auth');
            return { Component: RegisterRoute };
          },
        },
        {
          path: 'setup-otp',
          lazy: async () => {
            const { OtpRoute } = await import('./routes/auth');
            return { Component: OtpRoute };
          },
        },
        {
          path: 'verify-otp',
          lazy: async () => {
            const { OtpRoute } = await import('./routes/auth');
            return { Component: OtpRoute };
          },
        },
      ],
    },
    {
      path: '/app',
      element: (
        <ProtectedRoute>
          <AppRoot />
        </ProtectedRoute>
      ),
      children: [...availableRoutes],
    },
    {
      path: '*',
      lazy: async () => {
        const { NotFoundRoute } = await import('./routes/NotFound');
        return { Component: NotFoundRoute };
      },
    },
  ]);

export const AppRouter = () => {
  const queryClient = useQueryClient();
  const { user } = useUserStore();
  const { permissions } = usePermissionStore();
  const availableRoutes = useMemo(
    () => (user?.role?.id === 1 ? adminRoutes : renderRoutes(permissions)),
    [user, permissions],
  );

  const router = useMemo(
    () => createAppRouter(queryClient, availableRoutes),
    [queryClient, availableRoutes],
  );

  return <RouterProvider router={router} />;
};
