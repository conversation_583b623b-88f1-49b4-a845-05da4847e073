export const remapStatus = status =>
    ({
      draft: 'cs_draft',
      for_approval: 'cs_approval',
      partially_canvassed: 'cs_approval',
      approved: 'cs_approved',
      rejected: 'cs_rejected',
    })[status];

export const remapDocType = doc_type => // deprecated, transferred to backend
  ({
    requisition: 'R.S.',
    canvass: 'Canvass',
    purchase_order: 'Order',
    delivery_receipt: 'Delivery',
    invoice: 'Invoice',
    payment_request: 'Voucher',
    non_requisition: 'Non-R.S.',
  })[doc_type];


export const remapRefNumber = (doc_type, parent_id, module_id, status) => {
  switch (doc_type) {
    case 'R.S.':
      if (status === 'draft') return `/app/dashboard/create/${parent_id}`;
      else return `/app/requisition-slip/${parent_id}`;
    case 'Canvass':
      return `/app/requisition-slip/${parent_id}/canvass/${module_id}`;
    case 'Order':
      return `/app/purchase-order/${module_id}/requisition-slip/${parent_id}`;
    case 'Delivery':
      return `/app/receiving-report/${module_id}/requisition-slip/${parent_id}`;
    case 'Invoice':
      return `/app/requisition-slip/${parent_id}/invoice/${module_id}`;
    case 'Voucher':
      if (status === 'draft') return `/app/requisition-slip/${parent_id}/payment-request/create/${module_id}`;
      else return `/app/requisition-slip/${parent_id}/payment-request/${module_id}`
    case 'Non-R.S.':
      return `/app/non-requisition-slip/${module_id}`;
    default:
      return `/app/dashboard`;
  }
}