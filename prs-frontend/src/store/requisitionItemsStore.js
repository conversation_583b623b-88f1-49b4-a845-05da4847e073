import { create } from 'zustand';

const useRequisitionItemsStore = create(
  set => ({
    requisitionItems: [],
    steelbarItems: [],
    addItemMode: false,
    setAddItemMode: mode => {
      set({ addItemMode: mode });
    },
    addRequisitionItem: items => {
      set(state => {
        const updatedItems = items.reduce(
          (acc, newItem) => {
            if (!acc.find(item => item.id === newItem.id)) {
              acc.push(newItem);
            }
            return acc;
          },
          state.requisitionItems.filter(item =>
            items.some(newItem => newItem.id === item.id),
          ),
        );

        return {
          requisitionItems: updatedItems,
        };
      });
    },
    addSteelbarItem: items => {
      set(state => {
        const updatedItems = items.reduce(
          (acc, newItem) => {
            if (!acc.find(item => item.id === newItem.id)) {
              acc.push(newItem);
            }
            return acc;
          },
          state.steelbarItems.filter(item =>
            items.some(newItem => newItem.id === item.id),
          ),
        );

        return {
          steelbarItems: updatedItems,
        };
      });
    },
    addSubmittedRequisitionItem: item => {
      set(state => ({
        requisitionItems: [...state.requisitionItems, item],
      }));
    },
    updateRequisitionQuantity: (id, value) => {
      console.log('updateRequisitionQuantity', id, value);
      set(state => ({
        requisitionItems: state.requisitionItems.map(item =>
          item.id === id ? { ...item, quantity: value } : item,
        ),
      }));
    },
    updateSteelbarQuantity: (id, value) => {
      set(state => ({
        steelbarItems: state.steelbarItems.map(item =>
          item.id === id ? { ...item, quantity: value } : item,
        ),
      }));
    },
    updateSteelbarNote: (id, notes) => {
      set(state => ({
        steelbarItems: state.steelbarItems.map(item =>
          item.acctCd === id ? { ...item, notes } : item,
        ),
      }));
    },
    updateRequisitionNote: (id, notes) => {
      set(state => ({
        requisitionItems: state.requisitionItems.map(item =>
          item.id === id ? { ...item, notes } : item,
        ),
      }));
    },
    removeRequisitionItem: id => {
      set(state => ({
        requisitionItems: state.requisitionItems.filter(item =>
          item.id ? item.id !== id : item.itemId !== id,
        ),
      }));
    },
    removeSubmittedRequisitionItem: id => {
      set(state => ({
        requisitionItems: state.requisitionItems.filter(
          item => item.itemId !== id,
        ),
      }));
    },
    removeSteelbarItem: id => {
      set(state => ({
        steelbarItems: state.steelbarItems.filter(item => item.id !== id),
      }));
    },
    clearRequisitionItemsStore: () =>
      set({ requisitionItems: [], steelbarItems: [] }),
  }),
  {
    name: 'requisition-items-storage',
  },
);

export { useRequisitionItemsStore };
