import { useState } from "react";

const DEFAULT = [
    {
        key: 'items',
        value: 'Items',
    }, 
    {
        key: 'steel-bars',
        value: 'Steel Bars',
    }
]

export default function useTableTabs(setPage, currentPage = 1, tabConfigs = DEFAULT, defaultTab = 0) {
  // Default to the first tab provided in the config array
  const [activeTab, setActiveTab] = useState(tabConfigs[defaultTab] && tabConfigs[defaultTab].key);

  // Maintain page numbers for each tab; initialize all tabs to 1.
  const [pages, setPages] = useState(() =>
    tabConfigs.reduce((acc, tab) => {
      acc[tab.key] = 1;
      return acc;
    }, {})
  );

  // Handles switching tabs:
  // 1. Save the current page of the active tab.
  // 2. Retrieve the stored page for the target tab (defaulting to 1).
  // 3. Update the parent page and active tab.
  const handleTabChange = (targetTabKey) => {
    setPages((prevPages) => ({
      ...prevPages,
      [activeTab]: currentPage, // Save current page for the active tab
    }));
    // Get the saved page for the target tab or default to 1
    const newPage = pages[targetTabKey] || 1;
    setPage?.(newPage);
    setActiveTab(targetTabKey);
  };

  return {
    activeTab,
    setActiveTab,
    handleTabChange,
    pages,
    setPages,
    tabs: tabConfigs.map(tab => ({
      ...tab,
      onClick: () => handleTabChange(tab.key)
    }))
  };
}
