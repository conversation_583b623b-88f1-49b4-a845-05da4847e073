import { useMutation } from '@tanstack/react-query';
import { api } from '@lib/apiClient';
import { useNotification } from './useNotification';

const prefix = {
  requisition: 'RS',
  'non-requisitions': 'NRS',
  'delivery-receipt': 'RR',
  'purchase-order': 'PO',
  'invoice-report': 'IR',
  dashboard: 'DASHBOARD',
  // add more prefixes as needed
};

export const getTemplatePdf = ({ type, id }) => {
  return api.get(`/v1/generate-template/${type}/${id}`, {
    headers: {
      Accept: 'application/pdf',
    },
    responseType: 'blob',
  });
};

export const getDashboardPdf = ({ requestType, limit }) => {
  return api.get(`/v1/generate-template/dashboard`, {
    headers: {
      Accept: 'application/pdf',
    },
    params: {
      requestType,
      limit
    },
    responseType: 'blob',
  });
}

export const useDownloadTemplatePdf = (isDashboard = false, config = {}) => {
  const { showNotification } = useNotification();

  return useMutation({
    mutationFn: isDashboard ? getDashboardPdf : getTemplatePdf,
    onSuccess: (response, variables) => {
      try {
        const pdfBlob = response.data;

        if (!pdfBlob || !(pdfBlob instanceof Blob)) {
          throw new Error('Invalid response format - no PDF blob received');
        }

        const url = window.URL.createObjectURL(pdfBlob);

        const link = document.createElement('a');
        link.href = url;

        const contentDisposition = response.headers['content-disposition'];
        const match = contentDisposition?.match(
          /attachment; filename="([^"]+)"/,
        );

        const filename =
          match?.[1] ||
          `${prefix[variables.type] || 'DOC'}-${variables.id}.pdf`;

        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();

        link.remove();
        window.URL.revokeObjectURL(url);

        showNotification({
          type: 'success',
          message: 'PDF downloaded successfully',
        });
      } catch (error) {
        console.error('PDF download processing error:', error);
        throw error;
      }
    },
    onError: error => {
      console.error('PDF download failed:', error);
      showNotification({
        type: 'error',
        message: 'Failed to download PDF',
      });
    },
    ...config,
  });
};
