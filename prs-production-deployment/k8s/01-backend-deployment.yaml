apiVersion: apps/v1
kind: Deployment
metadata:
  name: prs-backend
  namespace: prs-production
  labels:
    app: prs-backend
    version: v1
    component: backend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: prs-backend
  template:
    metadata:
      labels:
        app: prs-backend
        version: v1
        component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "4000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: backend
        image: prs-backend:latest
        ports:
        - containerPort: 4000
          name: http
          protocol: TCP
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: encryption-key
        - name: OTP_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: otp-key
        - name: PASS_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: pass-secret
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: access-key
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: secret-key
        - name: CITYLAND_API_KEY
          valueFrom:
            secretKeyRef:
              name: external-api-secrets
              key: cityland-api-key
              optional: true
        - name: CITYLAND_ACCOUNTING_KEY
          valueFrom:
            secretKeyRef:
              name: external-api-secrets
              key: cityland-accounting-key
              optional: true
        envFrom:
        - configMapRef:
            name: backend-config
        volumeMounts:
        - name: upload-storage
          mountPath: /usr/app/upload
        - name: logs
          mountPath: /usr/app/logs
        - name: ssl-certs
          mountPath: /etc/ssl/certs
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 4000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 4000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 4000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          capabilities:
            drop:
            - ALL
      volumes:
      - name: upload-storage
        persistentVolumeClaim:
          claimName: upload-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: logs-pvc
      - name: ssl-certs
        secret:
          secretName: postgres-ssl-certs
      serviceAccountName: prs-backend
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      imagePullSecrets:
      - name: registry-secret
      nodeSelector:
        tier: backend
      tolerations:
      - key: "tier"
        operator: "Equal"
        value: "backend"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - prs-backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: prs-backend
  namespace: prs-production
  labels:
    app: prs-backend
    component: backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "4000"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: prs-backend
  ports:
  - port: 4000
    targetPort: 4000
    name: http
    protocol: TCP
  type: ClusterIP
  sessionAffinity: None
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prs-backend
  namespace: prs-production
  labels:
    app: prs-backend
    component: backend
automountServiceAccountToken: false
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: prs-backend
  namespace: prs-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: prs-backend
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: prs-backend
  namespace: prs-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: prs-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
