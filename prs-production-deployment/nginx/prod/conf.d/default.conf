# SSL Configuration and Server Blocks
# This file should be included in the main nginx.conf

# Force HTTPS redirect
server {
    listen 80;
    server_name ${DOMAIN} www.${DOMAIN} api.${DOMAIN} monitoring.${DOMAIN};
    return 301 https://$server_name$request_uri;
}

# Main Application Server
server {
    listen 443 ssl http2;
    server_name ${DOMAIN} www.${DOMAIN};

    # SSL Certificate
    ssl_certificate /run/secrets/ssl_certificate;
    ssl_certificate_key /run/secrets/ssl_private_key;

    # Security Headers
    include /etc/nginx/conf.d/security.conf;

    # Rate Limiting
    limit_req zone=api burst=20 nodelay;
    limit_conn conn_limit_per_ip 20;

    # Frontend Application
    location / {
        root /usr/share/nginx/html/static;
        index index.html;
        try_files $uri $uri/ /index.html;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status $upstream_cache_status;
        }

        # Security headers for HTML
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public";
            add_header X-Content-Type-Options nosniff;
            add_header X-Frame-Options DENY;
            add_header X-XSS-Protection "1; mode=block";
        }
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# API Server
server {
    listen 443 ssl http2;
    server_name api.${DOMAIN};

    # SSL Certificate
    ssl_certificate /run/secrets/ssl_certificate;
    ssl_certificate_key /run/secrets/ssl_private_key;

    # Security Headers
    include /etc/nginx/conf.d/security.conf;

    # Rate Limiting
    limit_req zone=api burst=50 nodelay;
    limit_conn conn_limit_per_ip 50;

    # Backend API
    location / {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Buffering
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;

        # Cache API responses
        proxy_cache api;
        proxy_cache_valid 200 5m;
        proxy_cache_valid 404 1m;
        proxy_cache_bypass $http_cache_control;
        add_header X-Cache-Status $upstream_cache_status;
    }

    # File uploads (no caching)
    location /upload {
        client_max_body_size 100m;
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Extended timeouts for file uploads
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # Authentication endpoints (stricter rate limiting)
    location ~ ^/(auth|login|register) {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Monitoring Dashboard
server {
    listen 443 ssl http2;
    server_name monitoring.${DOMAIN};

    # SSL Certificate
    ssl_certificate /run/secrets/ssl_certificate;
    ssl_certificate_key /run/secrets/ssl_private_key;

    # Security Headers
    include /etc/nginx/conf.d/security.conf;

    # Basic Authentication (create .htpasswd file)
    auth_basic "Monitoring Dashboard";
    auth_basic_user_file /etc/nginx/.htpasswd;

    # Grafana
    location /grafana/ {
        proxy_pass http://grafana:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Prometheus
    location /prometheus/ {
        proxy_pass http://prometheus:9090/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # AlertManager
    location /alertmanager/ {
        proxy_pass http://alertmanager:9093/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
