{"id": null, "title": "PRS Local Development Overview", "tags": ["prs", "local", "development"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "Backend API Health", "type": "stat", "targets": [{"expr": "up{job=\"prs-backend\"}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Database Health", "type": "stat", "targets": [{"expr": "up{job=\"postgres\"}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 27, "version": 1}