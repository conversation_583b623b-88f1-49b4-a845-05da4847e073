#!/bin/bash

# Script to manually import just the users data from your dump
# This will focus on getting your user accounts working

set -e

DUMP_FILE="dump_file_20250526_broken.sql"
DB_CONTAINER="prs-local-postgres"
DB_NAME="prs_local"
DB_USER="prs_user"
DB_PASSWORD="localdev123"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SQL in the database
execute_sql() {
    local sql="$1"
    docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "$sql"
}

log_info "Starting manual users import..."

# Step 1: Extract users data manually
log_info "Step 1: Extracting users data from dump..."

# Find the line number where users COPY starts
USERS_START=$(grep -n "^COPY public.users" "$DUMP_FILE" | cut -d: -f1)
if [ -z "$USERS_START" ]; then
    log_error "Could not find users COPY statement in dump file"
    exit 1
fi

log_info "Found users data starting at line $USERS_START"

# Extract users data until the \. terminator
USERS_END=$(tail -n +$((USERS_START + 1)) "$DUMP_FILE" | grep -n "^\\\\\\.$" | head -1 | cut -d: -f1)
if [ -z "$USERS_END" ]; then
    log_error "Could not find end of users data"
    exit 1
fi

USERS_END=$((USERS_START + USERS_END))
log_info "Users data ends at line $USERS_END"

# Create a clean users import file
USERS_FILE="users_import.sql"
cat > "$USERS_FILE" << 'EOF'
-- Import users data only
BEGIN;

-- Clear existing users
TRUNCATE TABLE users RESTART IDENTITY CASCADE;

EOF

# Add the COPY statement
sed -n "${USERS_START}p" "$DUMP_FILE" >> "$USERS_FILE"

# Add the data lines, but clean them up
log_info "Extracting and cleaning user data..."
sed -n "$((USERS_START + 1)),$((USERS_END - 1))p" "$DUMP_FILE" | while IFS= read -r line; do
    # Skip empty lines and lines that look problematic
    if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*$ && ! "$line" =~ ^[^0-9] ]]; then
        echo "$line" >> "$USERS_FILE"
    fi
done

# Add the terminator and commit
cat >> "$USERS_FILE" << 'EOF'
\.

-- Update the users sequence
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));

COMMIT;
EOF

log_success "Created clean users import file: $USERS_FILE"

# Step 2: Import the users
log_info "Step 2: Importing users into database..."

# Copy the file into the container
docker cp "$USERS_FILE" "$DB_CONTAINER:/tmp/users_import.sql"

# Import the users
log_info "Executing users import..."
if docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -f "/tmp/users_import.sql"; then
    log_success "Users import completed!"
else
    log_warning "Users import had some issues, checking what was imported..."
fi

# Step 3: Verify the import
log_info "Step 3: Verifying imported users..."

echo ""
log_info "Total users imported:"
execute_sql "SELECT COUNT(*) FROM users;"

echo ""
log_info "Sample of imported users:"
execute_sql "SELECT id, username, email, first_name, last_name FROM users ORDER BY id LIMIT 10;" || log_warning "Could not retrieve user data"

echo ""
log_info "Looking for your rootuser account:"
execute_sql "SELECT id, username, email, first_name, last_name FROM users WHERE username = 'rootuser' OR email LIKE '%stratpoint%' LIMIT 5;" || log_warning "Could not find rootuser"

# Clean up
rm -f "$USERS_FILE"
docker exec "$DB_CONTAINER" rm -f "/tmp/users_import.sql"

log_success "Users import process completed!"
echo ""
log_info "If you see users above, you can now login with those credentials."
log_info "The passwords from your original system should still work."
echo ""
