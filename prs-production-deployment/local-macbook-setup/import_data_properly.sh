#!/bin/bash

# Script to properly import your SQL dump data
# This will clear existing data and import your real data

set -e

DUMP_FILE="dump_file_20250526_broken.sql"
DB_CONTAINER="prs-local-postgres"
DB_NAME="prs_local"
DB_USER="prs_user"
DB_PASSWORD="localdev123"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SQL in the database
execute_sql() {
    local sql="$1"
    docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "$sql"
}

# Function to execute SQL file in the database
execute_sql_file() {
    local file="$1"
    docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -f "$file"
}

log_info "Starting proper data import process..."

# Step 1: Create a clean data-only SQL file
log_info "Step 1: Extracting clean data from dump file..."

DATA_FILE="clean_data_import.sql"

cat > "$DATA_FILE" << 'EOF'
-- Clean data import for PRS
-- This file contains only data imports without schema conflicts

BEGIN;

-- Disable triggers and constraints temporarily for faster import
SET session_replication_role = replica;

-- Clear existing data (keep schema)
TRUNCATE TABLE users RESTART IDENTITY CASCADE;
TRUNCATE TABLE companies RESTART IDENTITY CASCADE;
TRUNCATE TABLE departments RESTART IDENTITY CASCADE;
TRUNCATE TABLE roles RESTART IDENTITY CASCADE;
TRUNCATE TABLE approval_types RESTART IDENTITY CASCADE;

EOF

# Extract only the COPY statements and data from the original dump
log_info "Extracting COPY statements..."

# Use a more careful approach to extract COPY blocks
awk '
BEGIN { in_copy = 0 }
/^COPY public\./ { 
    in_copy = 1
    print $0
    next
}
in_copy && /^\\.$/ {
    print $0
    in_copy = 0
    print ""
    next
}
in_copy {
    print $0
    next
}
' "$DUMP_FILE" >> "$DATA_FILE"

# Add sequence updates
log_info "Adding sequence updates..."
echo "" >> "$DATA_FILE"
echo "-- Update sequences to correct values" >> "$DATA_FILE"
grep "SELECT pg_catalog.setval" "$DUMP_FILE" >> "$DATA_FILE" || true

# Re-enable constraints and commit
cat >> "$DATA_FILE" << 'EOF'

-- Re-enable triggers and constraints
SET session_replication_role = DEFAULT;

COMMIT;
EOF

log_success "Clean data file created: $DATA_FILE"

# Step 2: Import the clean data
log_info "Step 2: Importing clean data into database..."

# Copy the file into the container
docker cp "$DATA_FILE" "$DB_CONTAINER:/tmp/clean_data_import.sql"

# Import the data
log_info "Executing data import..."
if docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -f "/tmp/clean_data_import.sql"; then
    log_success "Data import completed successfully!"
else
    log_error "Data import failed. Checking for partial success..."
fi

# Step 3: Verify the import
log_info "Step 3: Verifying imported data..."

echo ""
log_info "Checking imported data counts:"

echo -n "Users: "
execute_sql "SELECT COUNT(*) FROM users;" | grep -E '^[0-9]+$' || echo "Error counting users"

echo -n "Companies: "
execute_sql "SELECT COUNT(*) FROM companies;" | grep -E '^[0-9]+$' || echo "Error counting companies"

echo -n "Departments: "
execute_sql "SELECT COUNT(*) FROM departments;" | grep -E '^[0-9]+$' || echo "Error counting departments"

echo ""
log_info "Sample users imported:"
execute_sql "SELECT id, username, email, first_name, last_name FROM users ORDER BY id LIMIT 5;" || log_warning "Could not retrieve user data"

# Clean up
rm -f "$DATA_FILE"
docker exec "$DB_CONTAINER" rm -f "/tmp/clean_data_import.sql"

log_success "Data import process completed!"
echo ""
log_info "You can now login with any of the imported users."
log_info "Try logging in with username: rootuser"
echo ""
