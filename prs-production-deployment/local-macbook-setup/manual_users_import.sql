-- Manual import of your users data
-- Based on the data I extracted from your dump file

BEGIN;

-- Clear existing users
TRUNCATE TABLE users RESTART IDENTITY CASCADE;

-- Import your users
COPY public.users (id, username, email, password, first_name, last_name, role_id, otp_secret, status, is_password_temporary, created_at, updated_at, deleted_at, department_id, temp_pass, supervisor_id) FROM stdin;
1	rootuser	<EMAIL>	$2a$10$kimZB/q/ps1ccSTBMlOcCOOLZSnMROWiFbJTBNWY1KkDR87KC485q	Root	User	1	7f3b22e97025cf235d8a8e9d40c13e37:c575283c41265633fc0d9c2712ae047ee595286bf58b119d6b788b2d63a4ef51c161c368f91e27bb203d594071718c51	active	f	2024-12-19 10:23:42.891+00	2025-04-14 05:30:48.147+00	\N	\N	\N	\N
4	kurt_stg	\N	$2a$08$2ONzJsLxj7Yu0p1k5t3z.O.5ZigDZOe371tqBTr4QV2PjJoBYjLfG	Kurt	Test	2	1e8f9c7164f9f82897df26cccf76aa01:4b0539401c1c697603539ed610feba728c537bc50bbe521842cb620349bf304da89573a33033cd1df32e9234df02dff9	active	f	2025-01-02 05:46:30.722+00	2025-01-08 11:04:39.936+00	\N	\N	\N	\N
6	cherry_stg_Engineers	\N	$2a$08$eyHaX/Gj8UItCnO8am/3rehgdmxuNdB1dAg5eJbTWPdFGUHJBDLpq	Cherry	Engineers	3	\N	active	t	2025-01-03 02:14:17.406+00	2025-01-03 02:18:04.135+00	\N	6	3006#w6UR	5
8	cherry_stg_DepartmentHead	\N	$2a$08$y9uyDGRrldSo9Bm1p/dpP.SJQwMsme.xUPOz0Gwtq.01wlsqZt7mm	Cherry	Department Head	6	\N	active	f	2025-01-03 02:16:45.173+00	2025-01-30 00:59:42.5+00	\N	10	\N	5
9	kams_stg_dept_head	\N	$2a$08$6pgHr0Vn9iW6oUWoNkym8.PrJ6RIM.oRl45EikyK.7PWRN9/Pa2y.	Kams	Dept Head	6	\N	active	t	2025-01-03 08:10:45.097+00	2025-01-03 08:11:10.789+00	\N	10	196#6yY7c	\N
13	kams_stg_purchasing_staff	\N	$2a$08$QpYT6EGPp0ZJMeqR/dXYn.o6YXFmY/u1Hn/jNmElM1BLEGBKvfARS	Kams	Purchasing Staff	2	27f51d91f6ce2007cf2e3a8bbccdf82b:d23e877b282fcd45143d17935af5f853458b0d1f223c35bb76fdd3cfc63393e18cfa466591ce3f927f52b84229019c87	active	f	2025-01-06 10:09:12.402+00	2025-01-06 10:14:07.02+00	\N	24	\N	10
15	mau_stg_admin	\N	$2a$08$Y9kTmWyQ9Tu4vu.j7zi07.aPphBWPhjL1kd7cRj6p5zNV5OURU/cu	Mau	Admin	2	\N	active	t	2025-01-08 03:02:42.86+00	2025-01-08 03:02:42.86+00	\N	\N	6993#h27A	\N
21	akm_123	\N	$2a$08$mAEgHMskem45wzWwqBFBZesfCFiwxHeCXNNoefwKEw2u3EfSTWphO	aas	a a	4	\N	active	t	2025-01-08 12:29:11.071+00	2025-01-08 12:31:32.275+00	\N	19	888#Aoo8h	\N
26	Gela_assistant_manager	\N	$2a$08$lMCto5nMXFyxQRBMYkyENeuSPPFk729/I/XD6AsQMCI5412g7kBYe	Gela	Assistant manager	5	\N	active	t	2025-01-21 05:56:41.526+00	2025-05-22 05:22:45.451+00	\N	19	4568#sA54	24
141	justine_stg_engineers	\N	$2a$08$YQursjtGKHa1oTmu7E6eKOmiw5bvj4z/f.lcR9scVGmaWbhCzb612	Justine	Zamora	3	\N	active	t	2025-04-24 01:25:26.294+00	2025-04-24 01:25:26.294+00	\N	27	9814#jYC3	5
144	mreyes	<EMAIL>	$2a$08$RJroFAFjOj4Dl.HXYkjp8.9bkxQQCawv0deJ7C0QATRi7wjRASsF2	Maureen	Reyes	2	\N	active	t	2025-04-29 04:57:09.282+00	2025-04-29 04:57:09.282+00	\N	12	7906#on7/	95
\.

-- Update the users sequence
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));

COMMIT;
