#!/bin/bash

# <PERSON>ript to fix your dump file and import EVERYTHING
# This will get your complete production data working

set -e

DUMP_FILE="dump_file_20250526_broken.sql"
FIXED_DUMP="dump_file_fixed.sql"
DB_CONTAINER="prs-local-postgres"
DB_NAME="prs_local"
DB_USER="prs_user"
DB_PASSWORD="localdev123"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "🔧 Starting comprehensive dump file fix and import..."
echo "This will import your ENTIRE production database!"
echo ""

# Step 1: Fix the dump file
log_info "Step 1: Fixing dump file issues..."

log_info "Creating fixed dump file..."
cat > "$FIXED_DUMP" << 'EOF'
-- Fixed dump file for complete data import
-- All ownership and constraint issues resolved

BEGIN;

-- Disable triggers and constraints for faster import
SET session_replication_role = replica;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- Drop and recreate the database schema to ensure clean state
DROP SCHEMA IF EXISTS public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO prs_user;
GRANT ALL ON SCHEMA public TO public;

EOF

log_info "Extracting and fixing schema from original dump..."

# Extract schema (CREATE statements) and fix ownership
grep -E "^(CREATE|ALTER TABLE|ALTER SEQUENCE|CREATE INDEX|CREATE UNIQUE INDEX)" "$DUMP_FILE" | \
sed 's/OWNER TO admin/OWNER TO prs_user/g' | \
sed 's/ALTER TABLE ONLY public\./ALTER TABLE public\./g' >> "$FIXED_DUMP"

log_info "Adding custom types and enums..."

# Extract and fix custom types
grep -A 20 "CREATE TYPE" "$DUMP_FILE" | \
sed 's/OWNER TO admin/OWNER TO prs_user/g' >> "$FIXED_DUMP"

echo "" >> "$FIXED_DUMP"
echo "-- Re-enable constraints after schema creation" >> "$FIXED_DUMP"
echo "SET session_replication_role = DEFAULT;" >> "$FIXED_DUMP"
echo "" >> "$FIXED_DUMP"

log_info "Extracting and cleaning data..."

# Extract only COPY statements and their data, fixing format issues
awk '
BEGIN { 
    in_copy = 0
    copy_table = ""
}
/^COPY public\./ { 
    in_copy = 1
    copy_table = $2
    print "-- Importing " copy_table
    print "SET session_replication_role = replica;"
    print $0
    next
}
in_copy && /^\\.$/ {
    print $0
    print "SET session_replication_role = DEFAULT;"
    print ""
    in_copy = 0
    copy_table = ""
    next
}
in_copy {
    # Clean up problematic characters and formatting
    gsub(/\r/, "", $0)  # Remove carriage returns
    if (length($0) > 0 && $0 !~ /^[[:space:]]*$/) {
        print $0
    }
    next
}
' "$DUMP_FILE" >> "$FIXED_DUMP"

# Add sequence updates
echo "" >> "$FIXED_DUMP"
echo "-- Update all sequences to correct values" >> "$FIXED_DUMP"
grep "SELECT pg_catalog.setval" "$DUMP_FILE" | \
sed 's/public\./public\./g' >> "$FIXED_DUMP" || true

# Finalize the script
cat >> "$FIXED_DUMP" << 'EOF'

-- Final cleanup and commit
SET session_replication_role = DEFAULT;
COMMIT;

-- Analyze tables for better performance
ANALYZE;
EOF

log_success "Fixed dump file created: $FIXED_DUMP"

# Step 2: Reset database to clean state
log_info "Step 2: Resetting database to clean state..."

docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "
DROP SCHEMA IF EXISTS public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO prs_user;
GRANT ALL ON SCHEMA public TO public;
"

log_success "Database reset complete"

# Step 3: Import the fixed dump
log_info "Step 3: Importing your complete production data..."

# Copy the fixed file to container
docker cp "$FIXED_DUMP" "$DB_CONTAINER:/tmp/dump_fixed.sql"

# Import with error handling
log_info "Starting import process (this may take a few minutes)..."

if docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -f "/tmp/dump_fixed.sql" 2>&1 | tee import_log.txt; then
    log_success "Import completed!"
else
    log_warning "Import completed with some warnings - checking results..."
fi

# Step 4: Verify the import
log_info "Step 4: Verifying your imported data..."

echo ""
log_info "📊 Data Summary:"

# Check major tables
for table in users companies departments roles requisitions projects suppliers; do
    count=$(docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ' || echo "0")
    echo "  $table: $count records"
done

echo ""
log_info "🔍 Sample Data Check:"

echo "Users:"
docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT id, username, email, first_name, last_name FROM users ORDER BY id LIMIT 5;" 2>/dev/null || echo "  No users found"

echo ""
echo "Companies:"
docker exec -i -e PGPASSWORD="$DB_PASSWORD" "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT id, name FROM companies ORDER BY id LIMIT 5;" 2>/dev/null || echo "  No companies found"

# Clean up
rm -f "$FIXED_DUMP"
docker exec "$DB_CONTAINER" rm -f "/tmp/dump_fixed.sql" 2>/dev/null || true

echo ""
if [ -f "import_log.txt" ]; then
    error_count=$(grep -c "ERROR" import_log.txt || echo "0")
    if [ "$error_count" -gt "0" ]; then
        log_warning "Import had $error_count errors - check import_log.txt for details"
    else
        log_success "Import completed successfully with no errors!"
        rm -f import_log.txt
    fi
fi

log_success "🎉 Complete database import finished!"
echo ""
log_info "Your entire production database should now be available at:"
echo "  🌐 Main App: https://localhost:8444"
echo "  🗄️  Database: https://localhost:8444/adminer/"
echo ""
log_info "Login with any of your production user accounts!"
