#!/bin/bash

# <PERSON><PERSON>t to fix SQL dump for proper import
# This script extracts only the data (COPY statements) and sequence updates

set -e

INPUT_FILE="dump_file_20250526_broken.sql"
OUTPUT_FILE="dump_file_20250526_fixed.sql"

echo "[INFO] Fixing SQL dump file..."

# Create a clean data-only dump
echo "-- Fixed SQL dump - Data only" > "$OUTPUT_FILE"
echo "-- Generated on $(date)" >> "$OUTPUT_FILE"
echo "-- This file contains only data imports and sequence updates" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

# Add transaction wrapper
echo "BEGIN;" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

# Add settings for better import
echo "SET statement_timeout = 0;" >> "$OUTPUT_FILE"
echo "SET lock_timeout = 0;" >> "$OUTPUT_FILE"
echo "SET client_encoding = 'UTF8';" >> "$OUTPUT_FILE"
echo "SET standard_conforming_strings = on;" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

# Extract COPY statements and their data using a more reliable method
echo "[INFO] Extracting data from COPY statements..."

# Use sed to extract COPY blocks
sed -n '/^COPY /,/^\\\.$/p' "$INPUT_FILE" >> "$OUTPUT_FILE"

# Extract sequence value updates
echo "[INFO] Extracting sequence updates..."
echo "" >> "$OUTPUT_FILE"
echo "-- Sequence value updates" >> "$OUTPUT_FILE"
grep "SELECT pg_catalog.setval" "$INPUT_FILE" >> "$OUTPUT_FILE" || true

echo "" >> "$OUTPUT_FILE"
echo "COMMIT;" >> "$OUTPUT_FILE"

echo "[SUCCESS] Fixed SQL dump created: $OUTPUT_FILE"

# Show statistics
COPY_COUNT=$(grep -c "^COPY" "$OUTPUT_FILE" || echo "0")
SETVAL_COUNT=$(grep -c "setval" "$OUTPUT_FILE" || echo "0")

echo "[INFO] Statistics:"
echo "  - COPY statements: $COPY_COUNT"
echo "  - Sequence updates: $SETVAL_COUNT"
echo ""
echo "You can now import this with:"
echo "  ./scripts/deploy-local.sh import-db $OUTPUT_FILE"
