version: '3.8'

services:
  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.external-url=https://monitoring.${DOMAIN}/prometheus'
    volumes:
      - ../monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ../monitoring/prometheus/alerts:/etc/prometheus/alerts:ro
      - ../monitoring/prometheus/recording-rules:/etc/prometheus/recording-rules:ro
      - prometheus_data:/prometheus
    networks:
      - monitoring_network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.tier == monitoring
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AlertManager for Alert Handling
  alertmanager:
    image: prom/alertmanager:latest
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=https://monitoring.${DOMAIN}/alertmanager'
      - '--cluster.listen-address=0.0.0.0:9094'
    volumes:
      - ../monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - ../monitoring/alertmanager/templates:/etc/alertmanager/templates:ro
      - alertmanager_data:/alertmanager
    secrets:
      - alertmanager_slack_webhook
      - alertmanager_email_password
    networks:
      - monitoring_network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.tier == monitoring
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD__FILE=/run/secrets/grafana_password
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=https://monitoring.${DOMAIN}/grafana
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_AUTH_ANONYMOUS_ENABLED=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_SMTP_ENABLED=true
      - GF_SMTP_HOST=smtp.${DOMAIN}:587
      - GF_SMTP_USER=grafana@${DOMAIN}
      - GF_SMTP_PASSWORD__FILE=/run/secrets/grafana_smtp_password
    volumes:
      - grafana_data:/var/lib/grafana
      - ../monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ../monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    secrets:
      - grafana_password
      - grafana_smtp_password
    networks:
      - monitoring_network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.tier == monitoring
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    depends_on:
      - prometheus
      - loki

  # Loki for Log Aggregation
  loki:
    image: grafana/loki:latest
    command: -config.file=/etc/loki/loki-config.yaml
    volumes:
      - ../monitoring/loki/loki-config.yaml:/etc/loki/loki-config.yaml:ro
      - loki_data:/loki
    networks:
      - monitoring_network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.labels.tier == monitoring
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Promtail for Log Collection
  promtail:
    image: grafana/promtail:latest
    command: -config.file=/etc/promtail/promtail-config.yaml
    volumes:
      - ../monitoring/promtail/promtail-config.yaml:/etc/promtail/promtail-config.yaml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - backend_logs:/var/log/backend:ro
    networks:
      - monitoring_network
    deploy:
      mode: global
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Node Exporter for System Metrics
  node-exporter:
    image: prom/node-exporter:latest
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - monitoring_network
    deploy:
      mode: global
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # cAdvisor for Container Metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - monitoring_network
    deploy:
      mode: global
      resources:
        limits:
          memory: 256M
          cpus: '0.2'

networks:
  monitoring_network:
    external: true

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  alertmanager_data:
    driver: local
  backend_logs:
    external: true

secrets:
  grafana_password:
    external: true
  grafana_smtp_password:
    external: true
  alertmanager_slack_webhook:
    external: true
  alertmanager_email_password:
    external: true
