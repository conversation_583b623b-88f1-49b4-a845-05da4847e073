# Production Environment Variables
# Copy this file to .env.production and customize for your environment

# Domain Configuration
DOMAIN=your-domain.com
SUBDOMAIN_API=api.your-domain.com
SUBDOMAIN_MONITOR=monitoring.your-domain.com

# SSL Configuration
SSL_EMAIL=<EMAIL>
SSL_STAGING=false

# Database Configuration (Use strong passwords)
POSTGRES_DB=prs_production
POSTGRES_USER=prs_user
POSTGRES_REPLICA_USER=replica_user

# Redis Configuration
REDIS_CLUSTER_ENABLED=true

# MinIO Configuration
MINIO_ROOT_USER=minio_admin
MINIO_CLUSTER_ENABLED=true

# Monitoring
PROMETHEUS_RETENTION=30d
LOKI_RETENTION=14d

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30

# Performance
BACKEND_REPLICAS=3
FRONTEND_REPLICAS=2
POSTGRES_MAX_CONNECTIONS=200
REDIS_MAX_MEMORY=2gb

# Storage Configuration (NFS)
NFS_SERVER=your-nfs-server.com
NFS_PATH=/exports/prs-production
BACKUP_NFS_SERVER=your-backup-nfs-server.com
BACKUP_NFS_PATH=/exports/prs-backups

# Application Version
VERSION=latest

# Docker Swarm Configuration
STACK_NAME=prs-production
