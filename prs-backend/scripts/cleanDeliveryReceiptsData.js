// scripts/truncateTables.js
'use strict';

const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');
const fs = require('fs');
const path = require('path');

async function truncateTables() {
  try {
    const sequelize = await getConnection();
    console.log('Connection has been established successfully.');

    const tables = [
      'delivery_receipt_invoices',
      'delivery_receipt_items_history',
      'delivery_receipt_items',
      'delivery_receipts',
    ];

    for (const table of tables) {
      await sequelize.query(`TRUNCATE TABLE ${table} RESTART IDENTITY CASCADE;`);
      console.log(`Truncated table: ${table}`);
    }

    const modelsInAttachment = [
      'delivery_receipt_invoice',
      'delivery_receipt',
    ];

    await sequelize.query(`
      DELETE FROM attachments
      WHERE model IN ('${modelsInAttachment.join('\',\'')}');
    `);
    
    for (const model of modelsInAttachment) {
      const directory = path.join('upload', model);
      if (fs.existsSync(directory)) {
        fs.readdirSync(directory).forEach((file) => {
          const filePath = path.join(directory, file);
          fs.unlinkSync(filePath);
        });
      }
    }
    console.log('Delivery receipts data cleaned successfully.');
    
    await sequelize.close();
    console.log('Connection closed.');
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  }
}

if(process.env.NODE_ENV === 'local') {
  truncateTables();
} else {
  console.log('This script is only for local environment');
}