// scripts/resetDeliveryReceipts.js
'use strict';

const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');

async function resetDeliveryReceipts(requisitionId) {
  if (!requisitionId) {
    console.error('Error: requisition_id parameter is required');
    process.exit(1);
  }

  try {
    const sequelize = await getConnection();
    console.log('Database connection established successfully.');
    
    console.log(`Starting reset process for requisition_id: ${requisitionId}`);
    
    // Begin transaction
    const transaction = await sequelize.transaction();
    
    try {
      // 1. Delete from requisition_delivery_histories
      const deletedHistories = await sequelize.query(
        `DELETE FROM requisition_delivery_histories WHERE requisition_id = :requisitionId RETURNING id`,
        { 
          replacements: { requisitionId },
          type: Sequelize.QueryTypes.DELETE,
          transaction
        }
      );
      console.log(`Deleted ${deletedHistories[0].length} records from requisition_delivery_histories`);
      
      // 2. Get delivery_receipt IDs for this requisition
      const drIds = await sequelize.query(
        `SELECT id FROM delivery_receipts WHERE requisition_id = :requisitionId`,
        {
          replacements: { requisitionId },
          type: Sequelize.QueryTypes.SELECT,
          transaction
        }
      );
      
      if (drIds.length > 0) {
        const drIdList = drIds.map(dr => dr.id);
        console.log(`Found ${drIdList.length} delivery receipts to process`);
        
        // 3. Delete from delivery_receipt_items_history
        const deletedItemHistories = await sequelize.query(
          `DELETE FROM delivery_receipt_items_history 
           WHERE delivery_receipt_item_id IN (
             SELECT id FROM delivery_receipt_items WHERE dr_id IN (:drIdList)
           ) RETURNING id`,
          {
            replacements: { drIdList },
            type: Sequelize.QueryTypes.DELETE,
            transaction
          }
        );
        console.log(`Deleted ${deletedItemHistories[0].length} records from delivery_receipt_items_history`);
        
        // 4. Delete from delivery_receipt_items
        const deletedItems = await sequelize.query(
          `DELETE FROM delivery_receipt_items WHERE dr_id IN (:drIdList) RETURNING id`,
          {
            replacements: { drIdList },
            type: Sequelize.QueryTypes.DELETE,
            transaction
          }
        );
        console.log(`Deleted ${deletedItems[0].length} records from delivery_receipt_items`);
        
        // 5. Delete from delivery_receipts
        const deletedReceipts = await sequelize.query(
          `DELETE FROM delivery_receipts WHERE id IN (:drIdList) RETURNING id`,
          {
            replacements: { drIdList },
            type: Sequelize.QueryTypes.DELETE,
            transaction
          }
        );
        console.log(`Deleted ${deletedReceipts[0].length} records from delivery_receipts`);
        
        // 6. Delete from attachments related to delivery_receipts
        const deletedAttachments = await sequelize.query(
          `DELETE FROM attachments WHERE model = 'delivery_receipt' AND model_id IN (:drIdList) RETURNING id`,
          {
            replacements: { drIdList },
            type: Sequelize.QueryTypes.DELETE,
            transaction
          }
        );
        console.log(`Deleted ${deletedAttachments[0].length} attachment records`);
      } else {
        console.log('No delivery receipts found for this requisition');
      }
      
      // Commit transaction
      await transaction.commit();
      console.log(`Reset process completed successfully for requisition_id: ${requisitionId}`);
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      console.error('Error during reset process:', error);
      throw error;
    } finally {
      await sequelize.close();
      console.log('Database connection closed.');
    }
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  }
}

// Check if running directly
if (require.main === module) {
  const requisitionId = process.argv[2];
  if (!requisitionId) {
    console.error('Usage: node resetDeliveryReceipts.js <requisition_id>');
    process.exit(1);
  }
  
  resetDeliveryReceipts(requisitionId);
}

module.exports = resetDeliveryReceipts;