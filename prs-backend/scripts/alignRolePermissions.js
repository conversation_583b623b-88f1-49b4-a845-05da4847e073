const {
  USER_TYPES,
  USER_STATUS,
} = require('../src/domain/constants/userConstants');
const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');

class RoleManager {
  constructor() {
    this.sequelize = null;
    this.transaction = null;
  }

  async init() {
    this.sequelize = await getConnection();
  }

  async cleanup() {
    await this.sequelize?.close();
  }

  async cleanupRoles(options = {}) {
    try {
      await this.init();

      this.transaction = await this.sequelize.transaction();

      await this._performCleanup(options);
      await this.transaction.commit();

      console.log(
        '\nRoles cleanup completed successfully, please restart the server to apply changes',
      );
    } catch (error) {
      await this.transaction?.rollback();

      throw error;
    } finally {
      await this.cleanup();
    }
  }

  async _performCleanup({ defaultRoleType = USER_TYPES.DEPARTMENT_SECRETARY }) {
    const rolesToRemove = await this._getRolesToRemove();
    const defaultRole = await this._getDefaultRole(defaultRoleType);

    console.log('rolesToRemove', rolesToRemove);

    if (!defaultRole) {
      throw new Error('Default role not found for reassignment');
    }

    console.log('\n=== Reassigning Users ===');
    for (const role of rolesToRemove) {
      await this._reassignUsers(role.id, defaultRole.id);
    }

    console.log('\n=== Cleaning Up Permissions ===');
    await this._deletePermissions();

    if (rolesToRemove.length) {
      console.log('\n=== Cleaning Up Roles ===');
      await this._deleteRoles(rolesToRemove);
    } else {
      console.log('\n=== No roles to remove ===');
    }
  }

  async _getRolesToRemove() {
    const allowedRoles = Object.values(USER_TYPES);
    const roles = await this.sequelize.query(
      `SELECT id, name FROM roles WHERE name NOT IN (:allowedRoles)`,
      {
        replacements: { allowedRoles },
        type: Sequelize.QueryTypes.SELECT,
        transaction: this.transaction,
      },
    );

    console.log(
      '\nRoles to be removed:',
      roles.map((r) => r.name),
    );

    return roles;
  }

  async _getDefaultRole(roleType) {
    const [role] = await this.sequelize.query(
      `SELECT id FROM roles WHERE name = :name`,
      {
        replacements: { name: roleType },
        type: Sequelize.QueryTypes.SELECT,
        transaction: this.transaction,
      },
    );

    return role;
  }

  async _reassignUsers(oldRoleId, newRoleId) {
    await this.sequelize.query(
      `UPDATE users 
       SET role_id = :newRoleId,
           status = :inactiveStatus,
           updated_at = NOW()
       WHERE role_id = :oldRoleId`,
      {
        replacements: {
          newRoleId,
          oldRoleId,
          inactiveStatus: USER_STATUS.INACTIVE,
        },
        type: Sequelize.QueryTypes.UPDATE,
        transaction: this.transaction,
      },
    );
  }

  async _deleteRolePermissions() {
    await this.sequelize.query(`DELETE FROM role_permissions`, {
      type: Sequelize.QueryTypes.DELETE,
      transaction: this.transaction,
    });

    console.log('Role permissions deleted');
  }

  async _deleteRoles(roles) {
    await this.sequelize.query(`DELETE FROM roles WHERE id IN (:roleIds)`, {
      replacements: { roleIds: roles.map((r) => r.id) },
      type: Sequelize.QueryTypes.DELETE,
      transaction: this.transaction,
    });

    console.log(`Roles deleted ${roles.length} - ${roles.map((r) => r.name)}`);
  }

  async _deletePermissions() {
    await this.sequelize.query(`DELETE FROM permissions`, {
      type: Sequelize.QueryTypes.DELETE,
      transaction: this.transaction,
    });

    console.log('Permissions deleted');
  }
}

// Script execution
if (require.main === module) {
  const manager = new RoleManager();

  manager
    .cleanupRoles()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = RoleManager;
