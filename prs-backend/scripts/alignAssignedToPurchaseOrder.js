const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');
const models = require('../src/infra/database/models');
const { assertInteger } = require('pdf-lib');
const { assign } = require('nodemailer/lib/shared');

async function alignAssignedToPurchaseOrder() {
  const sequelize = await getConnection();
  const transaction = await sequelize.transaction();

  try {
    //
    const purchaseOrders = await models.purchaseOrderModel.findAll({
      // include requisition with assignedTo as attribute
      include: [
        {
          model: models.requisitionModel,
          as: 'requisition',
        },
      ],
    });

    for (const po of purchaseOrders) {
      // loop through each PO and do an update to assignedTo based on po.requisition.assignedTo
      await po.update(
        {
          assignedTo: po.requisition.assignedTo,
        },
        { transaction },
      );
    }

    await transaction.commit();
    console.log('PO assignee aligned successfully.');
  } catch (error) {
    await transaction.rollback();
    console.error('Error aligning PO assignee:', error);
  } finally {
    sequelize.close();
  }
}

alignAssignedToPurchaseOrder();
