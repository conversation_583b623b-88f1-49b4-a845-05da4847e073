# Stage 1: Build
FROM node:20.18.0-alpine AS build

# Set working directory
WORKDIR /usr/app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install all dependencies for development
RUN npm install

# Copy the application source code
COPY . .

# Stage 2: Production
FROM node:20.18.0-alpine AS production

# Set working directory
WORKDIR /usr/app

# Copy only package.json and package-lock.json from the build stage
COPY --from=build /usr/app/package*.json ./

# Install only production dependencies
RUN npm ci --only=production

RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ttf-freefont

RUN chromium --version

# Copy the application source code from the build stage
COPY --from=build /usr/app .

# Expose the application port
EXPOSE 4000

# Command to run the application
CMD ["npm", "start"]
