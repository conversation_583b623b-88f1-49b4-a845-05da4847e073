meta {
  name: Update Invoice
  type: http
  seq: 5
}

put {
  url: {{base_url}}/v1/invoice-reports/:id
  body: json
  auth: bearer
}

params:path {
  id: 16
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "requisitionId": "156",
    "purchaseOrderId": "45",
    "isDraft": "true",
    "supplierInvoice": {
      "number": "ABC123",
      "invoiceDate": "2025-03-02",
      "amount": "12345.12"
    },
    "note": "This is a note.",
    "deliveryReceiptIds": [18],
    "attachmentIds": []
  }
}
