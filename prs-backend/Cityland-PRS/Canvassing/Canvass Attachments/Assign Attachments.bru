meta {
  name: Assign Attachments
  type: http
  seq: 2
}

post {
  url: http://localhost:4000/v1/attachments/assign
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "attachmentIds": [24],
      "model": "canvass_item_suppliers",
      "modelId": 1
  
  }
}

tests {
  // Parse the response JSON
  let jsonData = res.getBody();
  
  // Check if accessToken exists in the response
  if (jsonData.accessToken) {
      // Set the accessToken as an environment variable
      bru.setEnvVar("accessToken", jsonData.accessToken);
      console.log("Access Token saved to environment variable");
  } else {
      console.warn("accessToken not found in the response");
  }
      
}
