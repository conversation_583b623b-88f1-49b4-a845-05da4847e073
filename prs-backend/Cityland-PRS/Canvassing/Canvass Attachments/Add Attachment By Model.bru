meta {
  name: Add Attachment By Model
  type: http
  seq: 4
}

post {
  url: {{baseUrl}}/v1/attachments
  body: multipartForm
  auth: none
}

body:multipart-form {
  model: canvass_item_suppliers
  attachments: @file(/Users/<USER>/Documents/footer-logo.png)
}

tests {
  // const response = res.getBody();
  
  // if (res.getStatus() === 200 && response.length > 0) {
  //     const ids = response.map(item => item.id.toString());
  //     bru.setEnvVar("attachmentIds", JSON.stringify(ids));
  // } else {
  //     console.log("No IDs to extract");
  // }
}
