meta {
  name: Create canvass
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/v1/canvass
  body: multipartForm
  auth: none
}

body:multipart-form {
  notes: new comment
  requisitionId: 1
  canvassItems[0][requisitionItemListId]: 8
  canvassItems[0][suppliers][0][supplierId]: 1
  canvassItems[0][suppliers][0][term]: term
  canvassItems[0][suppliers][0][quantity]: 1
  canvassItems[0][suppliers][0][order]: 1
  canvassItems[0][suppliers][0][unitPrice]: 0.1
  canvassItems[0][suppliers][0][discountType]: fixed
  isDraft: false
  canvassItems[0][suppliers][0][discountValue]: 0
  canvassItems[0][suppliers][0][supplierType]: company
  ~canvassItems[0][suppliers][0][isSelected]: true
  ~attachments: @file(/Users/<USER>/Documents/image 5.png)
  ~canvassItems[0][suppliers][0][attachments]: @file()
  ~: 
}
