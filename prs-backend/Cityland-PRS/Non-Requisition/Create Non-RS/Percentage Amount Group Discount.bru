meta {
  name: Percentage Amount Group Discount
  type: http
  seq: 2
}

post {
  url: {{base_url}}/v1/non-requisitions
  body: json
  auth: inherit
}

headers {
  Content-Type: application/json
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzQ0NjgxMDcwLCJleHAiOjE3NDQ2ODQ2NzB9.xdbzJln1YwW6W3yYMTPoCE6p6L_KmHyYe76yRI25nSg
}

body:json {
  {
      "chargeTo": "company",
      "chargeToId": 11,
      "companyId": "1",
      "projectId": "1",
      "supplierId": "1",
      "departmentId": "1",
      "category": "company",
      "invoiceNo": "12312312321",
      "payableTo": "Project",
      "invoiceDate": "2025-04-12",
      "groupDiscountType":"percent",
      "groupDiscountPrice": 10.00,
      "supplierInvoiceAmount": 129.6,
      "attachmentIds": [],
      "invoiceNotes":"I am invoice notes",
      "itemList": [
          {
              "name": "commod",
              "quantity": 12,
              "amount": 12,
              "unit":"gal",
              "discountType": "fixed",
              "discountValue": 12
          }
      ]
  }
}
