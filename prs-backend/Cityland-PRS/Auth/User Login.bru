meta {
  name: User Login
  type: http
  seq: 4
}

post {
  url: {{baseUrl}}/v1/auth/login
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "username": "purstaff",
      "password": "12345"
  }
}

tests {
  const response = res.getBody();
  const { accessToken, otpSecret } = response
  
  if (accessToken) {
      bru.setVar('accessToken', accessToken)
  }
  
  if (otpSecret) {
      bru.setVar('otpSecret', otpSecret)
  }
  
}
