meta {
  name: Refresh Token
  type: http
  seq: 8
}

post {
  url: {{baseUrl}}/v1/auth/token
  body: none
  auth: bearer
}

auth:bearer {
  token: {{refreshToken}}
}

tests {
  const response = res.getBody();
  const {accessToken, refreshToken} = response;
  
  if (accessToken) {
      bru.setVar('accessToken', accessToken)
  }
  
  if (refreshToken) {
      bru.setVar('refreshToken', refreshToken)
  }
  
}
