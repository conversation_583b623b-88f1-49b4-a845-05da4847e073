meta {
  name: Setup OTP
  type: http
  seq: 5
}

post {
  url: {{baseUrl}}/v1/auth/setup-otp
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "otpSecret": "{{otpSecret}}",
      "otp": "740687"
  }
}

tests {
  const response = res.getBody();
  const accessToken = response.accessToken;
  
  if (accessToken) {
      bru.setVar('accessToken', accessToken)
  }
  
  
  
}
