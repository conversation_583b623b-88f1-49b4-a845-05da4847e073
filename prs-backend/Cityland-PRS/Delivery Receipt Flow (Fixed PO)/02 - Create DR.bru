meta {
  name: 02 - Create DR
  type: http
  seq: 5
}

post {
  url: http://localhost:4000/v1/delivery-receipts
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "requisitionId": "{{requisitionId}}",
    "poId": "{{poId}}",
    "supplier": "{{supplier}}",
    "isDraft": "true",
    "note": "",
    "invoiceNumber": "PS12345",
    "issuedDate": "2025-04-23",
    "supplierDeliveryIssuedDate": "2025-04-23",
    "items": {{itemsArray}},
    "attachmentIds": {{attachmentIds}}
  } 
}

tests {
  let jsonData = res.getBody();
  let itemsArray = JSON.parse(bru.getEnvVar("itemsArray") || "[]");
  
  if (jsonData.id) {
      bru.setEnvVar("drId", jsonData.id);
      console.log("Access Token saved to environment variable");
  }
  
  if (jsonData.items && Array.isArray(jsonData.items)) {
      itemsArray.forEach(item => {
          let matchedItem = jsonData.items.find(i => i.itemId === item.itemId);
          if (matchedItem) {
              item.id = matchedItem.id;  // Add or update "id"
          }
      });
  
      bru.setEnvVar("itemsArray", JSON.stringify(itemsArray));
      console.log("Updated ItemsArray with IDs:", itemsArray);
  }
}
