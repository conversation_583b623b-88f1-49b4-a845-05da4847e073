meta {
  name: 11 - <PERSON>move Attachment ID from Submitted DR
  type: http
  seq: 11
}

delete {
  url: http://localhost:4000/v1/attachments
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "attachmentIds": {{attachmentIds}},
      "model": "delivery_receipt",
      "modelId": "{{drId}}"
  }
}

tests {
  // Parse the response JSON
  let jsonData = res.getBody();
  
  // Check if accessToken exists in the response
  if (jsonData.accessToken) {
      // Set the accessToken as an environment variable
      bru.setEnvVar("accessToken", jsonData.accessToken);
      console.log("Access Token saved to environment variable");
  } else {
      console.warn("accessToken not found in the response");
  }
      
}
