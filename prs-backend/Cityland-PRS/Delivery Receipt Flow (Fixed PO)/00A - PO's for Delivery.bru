meta {
  name: 00A - PO's for Delivery
  type: http
  seq: 1
}

get {
  url: http://localhost:4000/v1/requisitions/{{requisitionId}}/purchase-order-for-delivery
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

vars:post-response {
  : 
}

script:post-response {
  bru.setEnvVar('poId', res.body[0].id);
}

tests {
  const response = res.getBody();
  if (response.length > 0) {
      const poId = response[0].id;
      bru.setEnvVar("poId", poId);
  } else {
      console.log("No items in response");
  }
}
