meta {
  name: 12 - Update Delivery Item from Submitted DR
  type: http
  seq: 12
}

put {
  url: http://localhost:4000/v1/delivery-receipt-items/{{itemId}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "qtyOrdered": 100,
      "qtyDelivered": 100,
      "qtyReturned": 0,
      "dateDelivered": "Jan 08, 2025",
      "notes": "notes"
  }
}

tests {
  // Parse the response JSON
  let jsonData = res.getBody();
  
  // Check if accessToken exists in the response
  if (jsonData.accessToken) {
      // Set the accessToken as an environment variable
      bru.setEnvVar("accessToken", jsonData.accessToken);
      console.log("Access Token saved to environment variable");
  } else {
      console.warn("accessToken not found in the response");
  }
      
}
