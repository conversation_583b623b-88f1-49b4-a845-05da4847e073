meta {
  name: 01A - Add DR Attachment
  type: http
  seq: 4
}

post {
  url: http://localhost:4000/v1/attachments
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:multipart-form {
  model: delivery_receipt
  attachments: @file(/Users/<USER>/Downloads/488823531_1179843577005093_1170045077995700908_n.jpg)
}

tests {
  const response = res.getBody();
  
  if (res.getStatus() === 200 && response.length > 0) {
      const ids = response.map(item => item.id);
      bru.setEnvVar("attachmentIds", JSON.stringify(ids));
  } else {
      console.log("No IDs to extract");
  }
}
