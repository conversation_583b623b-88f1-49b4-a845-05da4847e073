const { z } = require('zod');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');

const createCanvassItemSchema = z.object({
    requisitionCanvassId: z.number({
      required_error: 'Requisition Canvass ID is required',
    }),
    supplierId: z.number({
      required_error: 'Supplier ID is required',
    }),
    itemId: z.number({
      required_error: 'Item ID is required',
    }),
    terms: z.string().optional(),
    quantity: z.string().optional(),
    unitPrice: z.string().optional(),
    discount: z.string().optional(),
    discountedUnitPrice: z.string().optional(),
    notes: z.string().optional(),
    attachments: z.array(
      z.object({
        filePath: z.string(),
        originalname: z.string(),
      })
    ).optional(),
  });
  
  const getAllCanvassItemsSchema = z.object({
    search: z.string().optional(),
    sort: z.enum(['asc', 'desc']).optional(),
    sortBy: sortSchema(sort.CANVASS_ITEM_SORT_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  }).strict();
  
  const canvassItemSortSchema = sortSchema(sort.CANVASS_ITEM_SORT_COLUMNS);

  module.exports = {
    createCanvassItemSchema,
    getAllCanvassItemsSchema,
    canvassItemSortSchema,
  };