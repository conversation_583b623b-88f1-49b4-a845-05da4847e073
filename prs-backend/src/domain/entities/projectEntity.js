const { z } = require('zod');
const { sort, filter } = require('../constants');
const { sortSchemaV2 } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const { createNumberSchema, createIdParamsSchema } = require('../../app/utils');

const projectSortSchema = sortSchemaV2(sort.PROJECT_SORT_COLUMNS);
const projectFilterSchema = filterSchema(filter.PROJECT_FILTER_COLUMNS);

const createDateValidator = (fieldName) =>
  z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format. Use YYYY-MM-DD')
    .refine(
      (date) => {
        const inputDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return inputDate >= today;
      },
      {
        message: `${fieldName} must be greater than current date`,
      },
    )
    .optional();

const projectIdParams = z
  .object({
    projectId: createIdParamsSchema('Project ID'),
  })
  .strict();

const updateProjectSchema = z
  .object({
    // startDate: createDateValidator('Start date'),
    // endDate: createDateValidator('End date'),
  })
  .strict();

const updateProjectBusinessSchema = z
  .object({
    ...updateProjectSchema.shape,
  })
  .extend({
    existingStartDate: z.instanceof(Date).nullable().optional(),
    existingEndDate: z.instanceof(Date).nullable().optional(),
  })
  .superRefine((data, ctx) => {
    const { startDate, endDate, existingStartDate, existingEndDate } = data;
    const parsedStartDate = startDate ? new Date(startDate) : null;
    const parsedEndDate = endDate ? new Date(endDate) : null;
    const parsedExistingStartDate = existingStartDate || null;
    const parsedExistingEndDate = existingEndDate || null;

    /* Start date without end date (without existing dates) */
    if (parsedStartDate && !parsedEndDate && !parsedExistingEndDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date is required when setting start date',
        path: ['endDate'],
      });
    }

    /* End date without start date (without existing dates) */
    if (parsedEndDate && !parsedStartDate && !parsedExistingStartDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date is required when setting end date',
        path: ['startDate'],
      });
    }

    /* Date order validation */
    const effectiveStartDate = parsedStartDate || parsedExistingStartDate;
    const effectiveEndDate = parsedEndDate || parsedExistingEndDate;
    const isStartDateGreaterThanEndDate =
      effectiveStartDate &&
      effectiveEndDate &&
      effectiveStartDate >= effectiveEndDate;

    if (isStartDateGreaterThanEndDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Start date must be less than end date',
        path: [startDate ? 'startDate' : 'endDate'],
      });
    }
  });

const projectEngineerParams = z
  .object({
    projectId: createIdParamsSchema('Project ID'),
    tradeId: createIdParamsSchema('Trade ID'),
  })
  .strict();

const projectEngineerSchema = z
  .object({
    engineerIds: z
      .array(createNumberSchema('Engineer ID'))
      .min(1, 'At least one engineer must be selected')
      .refine(
        (ids) => new Set(ids).size === ids.length,
        'Duplicate engineer IDs are not allowed',
      ),
  })
  .strict();

const deleteEngineerParams = z
  .object({
    engineerId: createIdParamsSchema('Project Trade Engineer'),
  })
  .extend(projectEngineerParams.shape)
  .strict();

module.exports = {
  projectSortSchema,
  updateProjectSchema,
  projectIdParams,
  updateProjectBusinessSchema,
  projectEngineerSchema,
  projectEngineerParams,
  deleteEngineerParams,
  projectFilterSchema,
};
