const { z } = require('zod');
const { sort, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');

const getAllDepartmentsSchema = z
  .object({
    name: z.string().optional(),
    code: z.number().int().positive().optional(),
    companyId: z.number().int().positive().optional(),
    deptSupervisor: z.number().int().positive().optional(),
    sort: z.enum(['asc', 'desc']).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    paginate: z.string().optional(),
    sortBy: sortSchema(sort.DEPARTMENT_SORT_COLUMNS).optional(),
  })
  .strict();

const getDepartmentByIdSchema = z
  .object({
    id: z.string().refine((val) => !isNaN(Number(val)), {
      message: 'Must be a valid Department ID',
    }),
  })
  .strict();

const departmentSortSchema = sortSchema(sort.DEPARTMENT_SORT_COLUMNS);
const departmentFilterSchema = filterSchema(filter.DEPARTMENT_FILTER_COLUMNS);

module.exports = {
  getAllDepartmentsSchema,
  getDepartmentByIdSchema,
  departmentSortSchema,
  departmentFilterSchema,
};
