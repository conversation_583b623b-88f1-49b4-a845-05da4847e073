const { z } = require('zod');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');

const getTradesSchema = z.object({
  search: z.string().optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  sortBy: z.string().optional()
});

const tradeSortSchema = sortSchema(sort.TRADE_SORT_COLUMNS);

module.exports = {
  getTradesSchema,
  tradeSortSchema
};