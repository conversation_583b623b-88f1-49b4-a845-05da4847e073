const { z } = require('zod');

const tomItemIdSchema = z.object({
  id: z
    .string()
    .min(1, 'Tom item id is required')
    .refine((val) => val !== ':id', {
      message: 'No requisition id provided',
    }),
});

const updateTomItemSchema = z
  .object({
    quantity: z.number().int().positive().optional(),
    note: z.string().optional(),
  })
  .refine((data) => data.quantity !== undefined || data.comment !== undefined, {
    message: 'At least one of quantity or comment must be provided',
  });

module.exports = {
  tomItemIdSchema,
  updateTomItemSchema,
};
