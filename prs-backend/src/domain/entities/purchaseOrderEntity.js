const { z } = require('zod');
const { sort, requisition, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const {
  createIdParamsSchema,
  createOptionalIdParamsSchema,
  createNumberSchema,
} = require('../../app/utils');

const getPurchaseOrderByIdParams = z.object({
  purchaseOrderId: createIdParamsSchema('Purchase Order ID'),
});

const getPurchaseOrderPDFByIdParams = z.object({
  id: createIdParamsSchema('Purchase Order ID'),
});

const getAllPurchaseOrderParamsSchema = z.object({
  requisitionId: createIdParamsSchema('Requisition ID'),
});

const getPurchaseOrderApproverEntityParams = z.object({
  purchaseOrderId: createIdParamsSchema('Requisition ID'),
});

const getAllPurchaseOrderQuerySchema = z.object({
  sort: z.enum(['asc', 'desc']).optional(),
  sortBy: sortSchema(sort.PURCHASE_ORDER_SORT_COLUMNS).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

const updatePurchaseOrderParams = z.array(
  z.object({
    quantityPurchased: z.preprocess(
      (value) => {
        return parseFloat(value);
      },
      z.number().min(1, 'Quantity Purchased must be greater than 0'),
    ),

    purchaseOrderItemId: createIdParamsSchema('Purchase Order Item ID'),
  }),
);

const submitPurchaseOrderBodySchema = z
  .object({
    warrantyId: createOptionalIdParamsSchema('Warranty ID'),
    isNewDeliveryAddress: z.boolean(),
    newDeliveryAddress: z
      .string()
      .max(100, 'New delivery address must not exceed 100 characters')
      .nullable(),
    addedDiscount: z
      .number()
      .nonnegative()
      .optional()
      .refine(
        (val) => {
          if (val === undefined || val === '') return true; // optional
          return /^\d+(\.\d{1,2})?$/.test(val);
        },
        {
          message:
            'Discount must be a valid number with up to 2 decimal places',
        },
      ),
    isAddedDiscountFixedAmount: z.boolean().optional(),
    isAddedDiscountPercentage: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.isNewDeliveryAddress && !data.newDeliveryAddress) {
      ctx.addIssue({
        path: ['newDeliveryAddress'],
        code: z.ZodIssueCode.custom,
        message: 'New delivery address is required',
      });
    }

    const val = data.addedDiscount;
    const isFixed = data.isAddedDiscountFixedAmount;
    const isPercentage = data.isAddedDiscountPercentage;

    if (val !== undefined && val !== null) {
      if (isFixed) {
        const maxFixedAmount = 99999999999999999999; // 20 digits
        if (val > maxFixedAmount) {
          ctx.addIssue({
            path: ['addedDiscount'],
            code: z.ZodIssueCode.too_big,
            message: 'Discount must not exceed 20 digits',
            maximum: maxFixedAmount,
            type: 'number',
            inclusive: true,
          });
        }
      }

      if (isPercentage) {
        if (val > 100) {
          ctx.addIssue({
            path: ['addedDiscount'],
            code: z.ZodIssueCode.too_big,
            message: 'Percentage discount must not exceed 100%',
            maximum: 100,
            type: 'number',
            inclusive: true,
          });
        }
      }

      if (!isFixed && !isPercentage && val > 0) {
        ctx.addIssue({
          path: ['isAddedDiscountFixedAmount'],
          code: z.ZodIssueCode.custom,
          message:
            'Either fixed amount or percentage must be selected if a discount is provided',
        });
        ctx.addIssue({
          path: ['isAddedDiscountPercentage'],
          code: z.ZodIssueCode.custom,
          message:
            'Either fixed amount or percentage must be selected if a discount is provided',
        });
      }
    }
  });

const addPurchaseOrderAdhocApproverSchema = z
  .object({
    approverId: createNumberSchema('Approver Id'),
  })
  .strict();

module.exports = {
  getPurchaseOrderByIdParams,
  getAllPurchaseOrderParamsSchema,
  getAllPurchaseOrderQuerySchema,
  updatePurchaseOrderParams,
  submitPurchaseOrderBodySchema,
  getPurchaseOrderApproverEntityParams,
  addPurchaseOrderAdhocApproverSchema,
  getPurchaseOrderPDFByIdParams,
};
