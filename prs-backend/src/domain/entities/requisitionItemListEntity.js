const { z } = require('zod');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { createIdParamsSchema } = require('../../app/utils');

const requisitionItemListSortSchema = sortSchema(
  sort.REQUISITION_ITEM_LIST_SORT_COLUMNS,
);

const createRequisitionItemListSchema = z.array(
  z.object({
    itemId: z.number().int().positive(),
    quantity: z
      .number()
      .positive('Quantity must be greater than 0')
      .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
    notes: z.string().optional(),
    accountCode: z.string().optional(),
  }),
);

const requisitionItemListIdSchema = z.object({
  id: z
    .string()
    .min(1, 'Requisition item list id is required')
    .refine((val) => val !== ':id', {
      message: 'No requisition item list id provided',
    }),
});

const updateRequisitionItemListSchema = z
  .object({
    quantity: z
      .number()
      .positive('Quantity must be greater than 0')
      .multipleOf(0.001, 'Quantity must have at most 3 decimal places')
      .optional(),
    notes: z.string().optional(),
  })
  .refine((data) => data.quantity !== undefined || data.notes !== undefined, {
    message: 'At least one of quantity or notes must be provided',
  });

const deleteRequisitionItemListSchema = z.object({
  itemId: createIdParamsSchema('Item'),
  requisitionId: createIdParamsSchema('Requisition'),
});

module.exports = {
  requisitionItemListSortSchema,
  createRequisitionItemListSchema,
  requisitionItemListIdSchema,
  updateRequisitionItemListSchema,
  deleteRequisitionItemListSchema,
};
