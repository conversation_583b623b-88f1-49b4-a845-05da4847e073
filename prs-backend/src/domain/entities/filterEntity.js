const { z } = require('zod');

const filterSchema = (filterColumnTypes = {}) => {
  return z
    .string()
    .transform((str) => {
      try {
        return JSON.parse(str);
      } catch {
        return null;
      }
    })
    .transform((value) => {
      if (typeof value !== 'object' || value === null) {
        return null;
      }

      const validPairs = Object.entries(value).filter(([key, value]) => {
        if (!filterColumnTypes.hasOwnProperty(key)) {
          return false;
        }

        const expectedType = filterColumnTypes[key];

        switch (expectedType) {
          case 'numberArray':
            return (
              Array.isArray(value) &&
              value.every((item) => typeof item === 'number')
            );
          case 'stringArray':
            return (
              Array.isArray(value) &&
              value.every((item) => typeof item === 'string')
            );
          case 'string':
            return typeof value === 'string';
          case 'number':
            return typeof value === 'number';
          case 'boolean':
            return typeof value === 'boolean';
          default:
            return false;
        }
      });

      if (validPairs.length === 0) {
        return null;
      }

      return Object.fromEntries(validPairs);
    })
    .nullable()
    .optional();
};

module.exports = {
  filterSchema,
};
