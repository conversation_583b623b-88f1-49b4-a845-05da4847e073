const { z } = require('zod');
const {
  createIdParamsSchema,
  stringFieldError,
  createNumberSchema,
} = require('../../app/utils');
const { sortSchema, sortSchemaV2 } = require('./sortEntity');
const { sort } = require('../constants');

const decimalFormat = (fieldName) =>
  z.string().refine(
    (val) => {
      const numberValue = Number(val);
      return (
        !isNaN(numberValue) &&
        numberValue >= 0 &&
        val.indexOf('.') !== -1 &&
        val.split('.')[1].length === 2
      );
    },
    {
      message: `${fieldName} must be a valid value with 2 decimal places.`,
    },
  );

const paymentRequestIdParams = z
  .object({
    id: createIdParamsSchema('Purchase Request ID'),
  })
  .strict();

const addCommentSchema = z
  .object({
    notes: z
      .string(stringFieldError('Note'))
      .min(1, { message: 'Note must be 1 or more characters long.' })
      .max(100, { message: 'Note must not exceed 100 characters' })
      .regex(
        /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
        'RS Payment Request note contains invalid characters.',
      ),
  })
  .strict();

const createRsPaymentRequestSchema = z.object({
  isDraft: z
    .enum(['true', 'false'], {
      required_error: 'Draft status is required',
      invalid_type_error: 'Invalid draft status',
    })
    .transform((value) => value === 'true'),
  comments: z
    .string(stringFieldError('RS Payment Request Comment'))
    .trim()
    .max(100, 'Comment must not exceed 100 characters')
    .regex(
      /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
      'RS Payment Request note contains invalid characters.',
    )
    .optional(),
  requisitionId: createIdParamsSchema('Requisition ID'),
  purchaseOrderId: createIdParamsSchema('Purchase Order ID'),
  terms: z.enum(
    [
      'COD',
      '10% DP, Balance upon delivery',
      '20% DP, Balance upon delivery',
      '30% DP, Balance upon delivery',
      '50% DP, Balance upon delivery',
      '80% DP, Balance upon delivery',
      '10% DP, PB, 10% RETENTION',
      '20% DP, PB, 10% RETENTION',
      '30% DP, PB, 10% RETENTION',
      'CIA',
      'NET 15',
      'NET 30',
    ],
    {
      message:
        'Please input only COD, 10% DP, Balance upon delivery, 20% DP, Balance upon delivery, 30% DP, Balance upon delivery, 50% DP, Balance upon delivery, 80% DP, Balance upon delivery, 10% DP, PB, 10% RETENTION, 20% DP, PB, 10% RETENTION, 30% DP, PB, 10% RETENTION, CIA, NET 15, NET 30',
    },
  ),
  payableDate: z
    .string()
    .date('Date Format: YYYY-MM-DD')
    .refine((data) => data >= new Date().toJSON().slice(0, 10), {
      message: 'Payable Date must be in the Future or Today.',
      path: ['payableDate'],
    })
    .optional(),
  discountIn: z
    .enum(['Percentage', 'Fixed Amount'], {
      message: 'Please input ony Percentage, Fixed Amount',
    })
    .optional(),
  discountAmount: decimalFormat('Discount Amount').optional(),
  termsData: z
    .object({
      employeeId: z
        .number()
        .positive()
        .optional()
        .or(z.string().transform(Number).optional()),
    })
    .optional(),
  discountPercentage: decimalFormat('Discount Percentage').optional(),
  withholdingTaxDeduction: decimalFormat('Withholding Tax').optional(),
  deliveryFee: decimalFormat('Delivery Fee').optional(),
  tip: decimalFormat('Tip').optional(),
  extraCharges: decimalFormat('Extra Charges').optional(),
  status: z.string().optional(),
  totalAmount: decimalFormat('Total Amount').optional(),
});

const submitRsPaymentRequestSchema = z.object({
  isDraft: z
    .enum(['true', 'false'], {
      required_error: 'Draft status is required',
      invalid_type_error: 'Invalid draft status',
    })
    .transform((value) => value === 'true'),
  comments: z
    .string(stringFieldError('RS Payment Request Comment'))
    .trim()
    .max(100, 'Comment must not exceed 100 characters')
    .regex(
      /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
      'RS Payment Request note contains invalid characters.',
    )
    .optional(),
  requisitionId: createIdParamsSchema('Requisition ID'),
  purchaseOrderId: createIdParamsSchema('Purchase Order ID'),
  terms: z.enum(
    [
      'COD',
      '10% DP, Balance upon delivery',
      '20% DP, Balance upon delivery',
      '30% DP, Balance upon delivery',
      '50% DP, Balance upon delivery',
      '80% DP, Balance upon delivery',
      '10% DP, PB, 10% RETENTION',
      '20% DP, PB, 10% RETENTION',
      '30% DP, PB, 10% RETENTION',
      'CIA',
      'NET 15',
      'NET 30',
    ],
    {
      message:
        'Please input only COD, 10% DP, Balance upon delivery, 20% DP, Balance upon delivery, 30% DP, Balance upon delivery, 50% DP, Balance upon delivery, 80% DP, Balance upon delivery, 10% DP, PB, 10% RETENTION, 20% DP, PB, 10% RETENTION, 30% DP, PB, 10% RETENTION, CIA, NET 15, NET 30',
    },
  ),
  payableDate: z
    .string()
    .date('Date Format: YYYY-MM-DD')
    .refine((data) => data >= new Date().toJSON().slice(0, 10), {
      message: 'Payable Date must be in the Future or Today.',
      path: ['payableDate'],
    })
    .optional(),
  discountIn: z
    .enum(['Percentage', 'Fixed Amount'], {
      message: 'Please input ony Percentage, Fixed Amount',
    })
    .optional(),
  discountAmount: decimalFormat('Discount Amount').optional(),
  termsData: z
    .object({
      employeeId: z
        .number()
        .positive()
        .optional()
        .or(z.string().transform(Number).optional()),
    })
    .optional(),
  discountPercentage: decimalFormat('Discount Percentage').optional(),
  withholdingTaxDeduction: decimalFormat('Withholding Tax').optional(),
  deliveryFee: decimalFormat('Delivery Fee').optional(),
  tip: decimalFormat('Tip').optional(),
  extraCharges: decimalFormat('Extra Charges').optional(),
  status: z.string().optional(),
  totalAmount: decimalFormat('Total Amount').optional(),
  id: z
    .number()
    .positive()
    .optional()
    .or(z.string().transform(Number).optional()),
});

const purchaseOrderIdParams = z
  .object({
    purchaseOrderId: createIdParamsSchema('Purchase Order ID'),
  })
  .strict();

const addAdhocApproverSchema = z
  .object({
    approverId: createNumberSchema('Approver'),
  })
  .strict();

const rejectPRSchema = z
  .object({
    rejectReason: z
      .string(stringFieldError('Reject reason'))
      .min(1, { message: 'Reason must be 1 or more characters long.' })
      .max(100, { message: 'Reason must not exceed 100 characters' })
      .regex(
        /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
        'RS Payment Request Reject Not contains invalid characters.',
      ),
  })
  .strict();

const prSortSchema = sortSchemaV2(sort.PR_ITEMS_COLUMNS);

const getPaymentRequestsFromRequisitionParamsSchema = z.object({
  requisitionId: createIdParamsSchema('Requisition ID'),
});

const getPaymentRequestsFromRequisitionQuerySchema = z.object({
  search: z.string().optional(),
  sort: z.enum(['asc', 'desc']).optional(),
  sortBy: sortSchema(sort.PAYMENT_REQUEST_SORT_COLUMNS).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

module.exports = {
  paymentRequestIdParams,
  createRsPaymentRequestSchema,
  submitRsPaymentRequestSchema,
  purchaseOrderIdParams,
  addCommentSchema,
  addAdhocApproverSchema,
  rejectPRSchema,
  prSortSchema,
  getPaymentRequestsFromRequisitionParamsSchema,
  getPaymentRequestsFromRequisitionQuerySchema,
};
