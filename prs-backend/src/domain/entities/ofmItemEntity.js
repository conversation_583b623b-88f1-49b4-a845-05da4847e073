const { z } = require('zod');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');

// Schema for OFM Item List
const ofmItemListSchema = z.object({
  id: z.number().optional(),
  listName: z.string().max(100),
  companyCode: z.number().nullable().optional(),
  projectCode: z.string().max(20).nullable().optional(),
  tradeCode: z.number().nullable().optional()
});

const ofmItemListSortSchema = sortSchema(sort.OFM_ITEM_LIST_SORT_COLUMNS);
const ofmListItemSortSchema = sortSchema(sort.OFM_LIST_ITEM_SORT_COLUMNS);

const getOfmItemListsSchema = z.object({
  search: z.string().optional(),
  sort: z.enum(['asc', 'desc']).optional(),
  sortBy: sortSchema(sort.OFM_ITEM_LIST_SORT_COLUMNS),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  companyCode: z.number().optional(),
  projectCode: z.string().optional(),
  tradeCode: z.number().optional(),
  paginate: z.union([z.boolean(), z.enum(['true', 'false'])]).optional()
});

// Schema for OFM List Item
const ofmListItemSchema = z.object({
  id: z.number().optional(),
  ofmListId: z.number(),
  ofmItemId: z.number()
});

const getOfmListItemsSchema = z.object({
    search: z.string().optional(),
    sort: z.enum(['asc', 'desc']).optional(),
    sortBy: sortSchema(sort.OFM_LIST_ITEM_SORT_COLUMNS),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    ofmListId: z.number().optional(),
    companyCode: z.number().optional(),
    projectCode: z.string().optional(),
    tradeCode: z.number().optional(),
    paginate: z.union([z.boolean(), z.enum(['true', 'false'])]).optional()
  });

module.exports = {
  ofmItemListSchema,
  getOfmItemListsSchema,
  ofmItemListSortSchema,
  ofmListItemSchema,
  getOfmListItemsSchema,
  ofmListItemSortSchema
};