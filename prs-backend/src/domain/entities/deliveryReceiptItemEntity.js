const { z } = require('zod');

const getDeliveryReceiptItemByIdSchema = z.object({
  id: z
    .number()
    .min(1, 'Delivery receipt item ID is required')
    .refine((val) => val != ':id', {
      message: 'No delivery receipt item ID provided',
    }),
});

const updateDeliveryReceiptItemSchema = z
  .object({
    qtyOrdered: z
      .number()
      .min(1, 'Quantity ordered must be more than 0')
      .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
    qtyDelivered: z
      .number()
      .min(0, 'Quantity delivered must be at least 0')
      .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
    qtyReturned: z
      .number()
      .min(0, 'Quantity returned must be at least 0')
      .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
    dateDelivered: z.string(),
    notes: z
      .string()
      .regex(
        /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
        'Delivery Receipt Item note contains invalid characters.',
      ),
  })
  .strict()
  .refine((data) => data.qtyReturned <= data.qtyOrdered - data.qtyDelivered, {
    message: 'Quantity returned is invalid.',
  })
  .refine((data) => data.qtyDelivered <= data.qtyOrdered, {
    message:
      'Quantity delivered must be less than or equal to the quantity ordered.',
  });

module.exports = {
  getDeliveryReceiptItemByIdSchema,
  updateDeliveryReceiptItemSchema,
};
