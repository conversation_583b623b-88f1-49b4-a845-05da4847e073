const { z } = require('zod');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');

const getAllAuditLogsSchema = z.object({
  actionType: z.string().optional(),
  module: z.string().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  sort: z.enum(['asc', 'desc']).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  paginate: z.string().optional(),
  sortBy: sortSchema(sort.AUDIT_LOG_SORT_COLUMNS).optional(),
}).strict();

const auditLogSortSchema = sortSchema(sort.AUDIT_LOG_SORT_COLUMNS);

module.exports = {
  getAllAuditLogsSchema,
  auditLogSortSchema,
};