const { z } = require('zod');

const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');

const leaveSortSchema = sortSchema(sort.LEAVE_SORT_COLUMNS);

const getLeaveByIdSchema = z.object({
  id: z
    .number()
    .min(1, 'Leave id is required')
    .positive()
    .or(z.string().min(1, 'Leave id is required').transform(Number))
    .refine((val) => val !== ':id', {
      message: 'No leave id provided',
    }),
});

const getUserIdSchema = z.object({
  userId: z
    .number()
    .min(1, 'User id is required')
    .positive()
    .or(z.string().min(1, 'User id is required').transform(Number))
    .refine((val) => val !== ':id', {
      message: 'No User id provided',
    }),
});

const createLeaveSchema = z
  .object({
    startDate: z
      .string()
      .date('Date Format: YYYY-MM-DD')
      .refine((data) => data > new Date().toJSON().slice(0, 10), {
        message: 'Start Date must be in the Future.',
        path: ['startDate'],
      }),
    endDate: z.string().date('Date Format: YYYY-MM-DD'),
  })
  .refine((data) => data.endDate >= data.startDate, {
    message: 'End Date cannot be earlier than start date.',
    path: ['endDate'],
  });

const updateLeaveSchema = z
  .object({
    startDate: z
      .string()
      .date('Date Format: YYYY-MM-DD')
      .refine((data) => data > new Date().toJSON().slice(0, 10), {
        message: 'Leave cannot be Edited Start Date must be in the Future.',
        path: ['startDate'],
      })
      .optional(),
    endDate: z.string().date('Date Format: YYYY-MM-DD').optional(),
  })
  .refine((data) => data.endDate >= data.startDate, {
    message: 'End Date cannot be earlier than start date.',
    path: ['endDate'],
  });

const addAltApproverSchema = z.object({
  id: z.array(
    z
      .number()
      .min(1, 'Leave id is required')
      .positive()
      .or(z.string().min(1, 'Leave id is required').transform(Number))
      .refine((val) => val !== ':id', {
        message: 'No leave id provided',
      }),
  ),
  userId: z
    .number()
    .min(1, 'Leave id is required')
    .positive()
    .or(z.string().min(1, 'Leave id is required').transform(Number))
    .refine((val) => val !== ':id', {
      message: 'No leave id provided',
    })
    .nullable(),
  toChange: z.enum(['requisition', 'canvass', 'po', 'delivery', 'pr'], {
    message:
      'Please provide toChange Value: requisition, canvass, po, delivery',
  }),
});

module.exports = {
  createLeaveSchema,
  leaveSortSchema,
  updateLeaveSchema,
  getLeaveByIdSchema,
  getUserIdSchema,
  addAltApproverSchema,
};
