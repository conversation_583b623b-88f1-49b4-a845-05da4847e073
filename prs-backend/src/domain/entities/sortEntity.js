const { z } = require('zod');
const { sort } = require('../constants');

const sortSchema = (sortColumns = []) => {
  return z
    .string()
    .transform((str) => {
      try {
        return JSON.parse(str);
      } catch {
        return null;
      }
    })
    .transform((value) => {
      if (typeof value !== 'object' || value === null) return null;

      const validPairs = Object.entries(value).filter(
        ([key, direction]) =>
          sortColumns.includes(key) &&
          sort.SORT_DIRECTIONS.includes(direction?.toLowerCase()),
      );

      if (validPairs.length === 0) return null;

      return validPairs.map(([key, direction]) => [
        key,
        direction.toUpperCase(),
      ]);
    })
    .nullable()
    .optional();
};

const sortSchemaV2 = (sortColumns = []) => {
  return z
    .string()
    .transform((str) => {
      try {
        const parsed = JSON.parse(str);
        const filtered = Object.keys(parsed).reduce((acc, key) => {
          const direction = parsed[key];
          const isValid =
            sortColumns.includes(key) &&
            sort.SORT_DIRECTIONS.includes(direction?.toLowerCase());

          if (isValid) {
            acc[key] = direction;
          }

          return acc;
        }, {});
        return filtered;
      } catch {
        return null;
      }
    })
    .nullable()
    .optional();
};

module.exports = {
  sortSchema,
  sortSchemaV2,
};
