const { z } = require('zod');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');

const rsHistorySortSchema = sortSchema(sort.RS_HISTORY_SORT_COLUMNS);

const getRequisitionHistorySchema = z
  .object({
    search: z.string().optional(),
    sortBy: rsHistorySortSchema.optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    type: z
      .enum([
        'canvass',
        'order',
        'delivery',
        'payment',
        'return',
        'item',
        'invoice',
      ])
      .optional(),
  })
  .strict();

module.exports = {
  getRequisitionHistorySchema,
  rsHistorySortSchema,
};
