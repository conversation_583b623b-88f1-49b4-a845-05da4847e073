const { z } = require('zod');
const { sort, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const { stringFieldError } = require('../../app/utils');
const { USER_STATUS } = require('../constants/userConstants');

const excludeIdsSchema = z
  .string()
  .optional()
  .default('[]')
  .transform((str) => {
    try {
      return JSON.parse(str);
    } catch {
      return [];
    }
  })
  .transform((value) => {
    const isInvalidArray =
      !Array.isArray(value) || !value.every((item) => typeof item === 'number');

    if (isInvalidArray) {
      return [];
    }

    return value;
  });

const otpSchema = z
  .string(stringFieldError('OTP'))
  .length(6, 'OTP must be 6 digits string');

const emailSchema = z
  .string(stringFieldError('Email'))
  .trim()
  .email('Invalid email format')
  .min(1, 'Email must be at least 1 character long')
  .max(100, 'Email must be at most 100 characters')
  .or(z.literal(''))
  .transform((value) => {
    if (!value) return null;

    return value;
  })
  .nullable()
  .optional();

const passwordSchema = z
  .string(stringFieldError('Password'))
  .min(8, 'Password must be at least 8 characters long')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(
    /[^A-Za-z0-9]/,
    'Password must contain at least one special character',
  );

const usernameSchema = z
  .string(stringFieldError('Username'))
  .trim()
  .min(5, 'Username must be at least 5 characters long')
  .max(50, 'Username must be at most 50 characters long');

const userSchema = z
  .object({
    firstName: z
      .string(stringFieldError('First name'))
      .trim()
      .max(100, 'First name must be at most 100 characters')
      .regex(
        /^[A-Za-z'\s.-Ññ]+$/,
        'First name can only contain letters and specific special characters: -\'."',
      ),

    lastName: z
      .string(stringFieldError('Last name'))
      .trim()
      .max(100, 'Last name must be at most 100 characters')
      .regex(
        /^[A-Za-z'\s.-Ññ]+$/,
        'Last name can only contain letters and specific special characters: -\'."',
      ),

    roleId: z.number({
      required_error: `Role ID is required`,
      invalid_type_error: `Must be a valid role ID`,
    }),

    departmentId: z
      .number({
        invalid_type_error: `Must be a valid Department ID`,
      })
      .optional(),

    supervisorId: z
      .number({
        invalid_type_error: `Must be a valid Supervisor ID`,
      })
      .optional(),

    username: usernameSchema,
    email: emailSchema,
    password: passwordSchema,
  })
  .strict();

const addUserRequest = userSchema
  .omit({
    password: true,
  })
  .strict();

const loginRequest = z
  .object({
    username: z.string(stringFieldError('Username')),
    password: z.string(stringFieldError('Password')),
  })
  .strict();

const verifyOTPRequest = z
  .object({
    otp: otpSchema,
  })
  .strict();

const setupOTPRequest = z
  .object({
    otpSecret: z.string(stringFieldError('OTP secret')),
    otp: otpSchema,
  })
  .strict();

const updatePassRequest = z
  .object({
    password: passwordSchema,
  })
  .strict();

const updateUserRequest = z
  .object({
    firstName: userSchema.shape.firstName.optional(),
    lastName: userSchema.shape.lastName.optional(),
    roleId: userSchema.shape.roleId.optional(),
    supervisorId: userSchema.shape.supervisorId.optional(),
    departmentId: userSchema.shape.departmentId.optional(),
    email: userSchema.shape.email.optional(),
    username: userSchema.shape.username.optional(),
    status: z
      .enum(Object.values(USER_STATUS), {
        errorMap: () => ({ message: 'Invalid status' }),
      })
      .optional(),
  })
  .strict();

const updateUserParams = z
  .object({
    id: z
      .string({
        required_error: 'User ID is required',
      })
      .refine((val) => !isNaN(Number(val)), {
        message: 'Invalid User ID',
      })
      .transform((val) => Number(val)),
  })
  .strict();

const resetPasswordRequest = z
  .object({
    userId: z.number({
      required_error: `User ID is required`,
      invalid_type_error: `Must be a valid User ID`,
    }),
  })
  .strict();

const forgotPasswordRequest = z
  .object({
    username: usernameSchema,
  })
  .strict();

const userSortSchema = sortSchema(sort.USER_SORT_COLUMNS);
const userFilterSchema = filterSchema(filter.USER_FILTER_COLUMNS);

module.exports = {
  userSchema,
  loginRequest,
  addUserRequest,
  userSortSchema,
  verifyOTPRequest,
  setupOTPRequest,
  excludeIdsSchema,
  updatePassRequest,
  updateUserRequest,
  updateUserParams,
  userFilterSchema,
  forgotPasswordRequest,
  resetPasswordRequest,
};
