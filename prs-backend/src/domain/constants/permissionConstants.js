const MODULES = {
  ROLES: 'roles',
  USERS: 'users',
  COMPANIES: 'companies',
  PROJECTS: 'projects',
  DEPARTMENTS: 'departments',
  SUPPLIERS: 'suppliers',
  OFM_ITEMS: 'ofm_items',
  OFM_HISTORY: 'ofm_history',
  NON_OFM_ITEMS: 'non_ofm_items',
  OFM_LISTS: 'ofm_lists',
  DASHBOARD: 'dashboard', // Requisition Slip
  DASHBOARD_HISTORY: 'dashboard_history',
  CANVASS: 'canvass',
  ORDERS: 'orders',
  DELIVERY: 'delivery',
  PAYMENTS: 'payments',
  NON_RS_PAYMENTS: 'non_rs_payments',
  AUDIT_LOGS: 'audit_logs',
};

const ACTIONS = {
  VIEW: 'view',
  GET: 'get',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  SYNC: 'sync',
  APPROVAL: 'approval',
};

/**
 * * This is the list of permissions that are available in the system
 * ! Adding an entry here will automatically
 * ! generate the permission in the database via seeder
 */
const PERMISSIONS = {
  /* Roles */
  GET_ROLES: { module: MODULES.ROLES, action: ACTIONS.GET },

  /* Users */
  VIEW_USERS: { module: MODULES.USERS, action: ACTIONS.VIEW },
  GET_USERS: { module: MODULES.USERS, action: ACTIONS.GET },
  CREATE_USERS: { module: MODULES.USERS, action: ACTIONS.CREATE },
  UPDATE_USERS: { module: MODULES.USERS, action: ACTIONS.UPDATE },
  DELETE_USERS: { module: MODULES.USERS, action: ACTIONS.DELETE },

  /* Companies and Company Association */
  CREATE_COMPANIES: { module: MODULES.COMPANIES, action: ACTIONS.CREATE },
  VIEW_COMPANIES: { module: MODULES.COMPANIES, action: ACTIONS.VIEW },
  GET_COMPANIES: { module: MODULES.COMPANIES, action: ACTIONS.GET },
  UPDATE_COMPANIES: { module: MODULES.COMPANIES, action: ACTIONS.UPDATE },
  SYNC_COMPANIES: { module: MODULES.COMPANIES, action: ACTIONS.SYNC },
  DELETE_COMPANIES: { module: MODULES.COMPANIES, action: ACTIONS.DELETE },

  /* Projects */
  VIEW_PROJECTS: { module: MODULES.PROJECTS, action: ACTIONS.VIEW },
  GET_PROJECTS: { module: MODULES.PROJECTS, action: ACTIONS.GET },
  SYNC_PROJECTS: { module: MODULES.PROJECTS, action: ACTIONS.SYNC },
  UPDATE_PROJECTS: { module: MODULES.PROJECTS, action: ACTIONS.UPDATE },
  APPROVAL_PROJECTS: { module: MODULES.PROJECTS, action: ACTIONS.APPROVAL },
  DELETE_PROJECTS: { module: MODULES.PROJECTS, action: ACTIONS.DELETE },

  /* Departments */
  VIEW_DEPARTMENTS: { module: MODULES.DEPARTMENTS, action: ACTIONS.VIEW },
  GET_DEPARTMENTS: { module: MODULES.DEPARTMENTS, action: ACTIONS.GET },
  UPDATE_DEPARTMENTS: { module: MODULES.DEPARTMENTS, action: ACTIONS.UPDATE },
  SYNC_DEPARTMENTS: { module: MODULES.DEPARTMENTS, action: ACTIONS.SYNC },
  APPROVAL_DEPARTMENTS: {
    module: MODULES.DEPARTMENTS,
    action: ACTIONS.APPROVAL,
  },

  /* Suppliers */
  VIEW_SUPPLIERS: { module: MODULES.SUPPLIERS, action: ACTIONS.VIEW },
  GET_SUPPLIERS: { module: MODULES.SUPPLIERS, action: ACTIONS.GET },
  UPDATE_SUPPLIERS: { module: MODULES.SUPPLIERS, action: ACTIONS.UPDATE },
  SYNC_SUPPLIERS: { module: MODULES.SUPPLIERS, action: ACTIONS.SYNC },
  DELETE_SUPPLIERS: { module: MODULES.SUPPLIERS, action: ACTIONS.DELETE },

  /* OFM Items */
  VIEW_OFM_ITEMS: { module: MODULES.OFM_ITEMS, action: ACTIONS.VIEW },
  GET_OFM_ITEMS: { module: MODULES.OFM_ITEMS, action: ACTIONS.GET },
  CREATE_OFM_ITEMS: { module: MODULES.OFM_ITEMS, action: ACTIONS.CREATE },
  UPDATE_OFM_ITEMS: { module: MODULES.OFM_ITEMS, action: ACTIONS.UPDATE },
  VIEW_OFM_HISTORY: { module: MODULES.OFM_HISTORY, action: ACTIONS.VIEW },
  GET_OFM_HISTORY: { module: MODULES.OFM_HISTORY, action: ACTIONS.GET },
  SYNC_OFM_ITEMS: { module: MODULES.OFM_ITEMS, action: ACTIONS.SYNC },

  /* OFM Lists */
  VIEW_OFM_LISTS: { module: MODULES.OFM_LISTS, action: ACTIONS.VIEW },
  GET_OFM_LISTS: { module: MODULES.OFM_LISTS, action: ACTIONS.GET },
  CATEGORIZE_OFM_LISTS: { module: MODULES.OFM_LISTS, action: ACTIONS.UPDATE },

  /* Non-OFM Items */
  VIEW_NON_OFM_ITEMS: { module: MODULES.NON_OFM_ITEMS, action: ACTIONS.VIEW },
  GET_NON_OFM_ITEMS: { module: MODULES.NON_OFM_ITEMS, action: ACTIONS.GET },
  CREATE_NON_OFM_ITEMS: {
    module: MODULES.NON_OFM_ITEMS,
    action: ACTIONS.CREATE,
  },
  UPDATE_NON_OFM_ITEMS: {
    module: MODULES.NON_OFM_ITEMS,
    action: ACTIONS.UPDATE,
  },
  DELETE_NON_OFM_ITEMS: {
    module: MODULES.NON_OFM_ITEMS,
    action: ACTIONS.DELETE,
  },

  /* Dashboard - (Requisition Slip) */
  VIEW_DASHBOARD: { module: MODULES.DASHBOARD, action: ACTIONS.VIEW },
  GET_DASHBOARD: { module: MODULES.DASHBOARD, action: ACTIONS.GET },
  UPDATE_DASHBOARD: { module: MODULES.DASHBOARD, action: ACTIONS.UPDATE },
  DELETE_DASHBOARD: { module: MODULES.DASHBOARD, action: ACTIONS.DELETE },
  CREATE_DASHBOARD: { module: MODULES.DASHBOARD, action: ACTIONS.CREATE },
  APPROVAL_DASHBOARD: { module: MODULES.DASHBOARD, action: ACTIONS.APPROVAL },
  VIEW_DASHBOARD_HISTORY: {
    module: MODULES.DASHBOARD_HISTORY,
    action: ACTIONS.VIEW,
  },
  GET_DASHBOARD_HISTORY: {
    module: MODULES.DASHBOARD_HISTORY,
    action: ACTIONS.GET,
  },

  /* Canvass */
  VIEW_CANVASS: { module: MODULES.CANVASS, action: ACTIONS.VIEW },
  GET_CANVASS: { module: MODULES.CANVASS, action: ACTIONS.GET },
  CREATE_CANVASS: { module: MODULES.CANVASS, action: ACTIONS.CREATE },
  UPDATE_CANVASS: { module: MODULES.CANVASS, action: ACTIONS.UPDATE },
  DELETE_CANVASS: { module: MODULES.CANVASS, action: ACTIONS.DELETE },
  APPROVAL_CANVASS: { module: MODULES.CANVASS, action: ACTIONS.APPROVAL },

  /* Orders */
  VIEW_ORDERS: { module: MODULES.ORDERS, action: ACTIONS.VIEW },
  GET_ORDERS: { module: MODULES.ORDERS, action: ACTIONS.GET },
  CREATE_ORDERS: { module: MODULES.ORDERS, action: ACTIONS.CREATE },
  UPDATE_ORDERS: { module: MODULES.ORDERS, action: ACTIONS.UPDATE },
  DELETE_ORDERS: { module: MODULES.ORDERS, action: ACTIONS.DELETE },
  APPROVAL_ORDERS: { module: MODULES.ORDERS, action: ACTIONS.APPROVAL },

  /* Delivery */
  VIEW_DELIVERY: { module: MODULES.DELIVERY, action: ACTIONS.VIEW },
  GET_DELIVERY: { module: MODULES.DELIVERY, action: ACTIONS.GET },
  CREATE_DELIVERY: { module: MODULES.DELIVERY, action: ACTIONS.CREATE },
  UPDATE_DELIVERY: { module: MODULES.DELIVERY, action: ACTIONS.UPDATE },
  DELETE_DELIVERY: { module: MODULES.DELIVERY, action: ACTIONS.DELETE },

  /* Payments */
  VIEW_PAYMENTS: { module: MODULES.PAYMENTS, action: ACTIONS.VIEW },
  GET_PAYMENTS: { module: MODULES.PAYMENTS, action: ACTIONS.GET },
  CREATE_PAYMENTS: { module: MODULES.PAYMENTS, action: ACTIONS.CREATE },
  UPDATE_PAYMENTS: { module: MODULES.PAYMENTS, action: ACTIONS.UPDATE },
  DELETE_PAYMENTS: { module: MODULES.PAYMENTS, action: ACTIONS.DELETE },
  APPROVAL_PAYMENTS: { module: MODULES.PAYMENTS, action: ACTIONS.APPROVAL },

  /* Non-RS Payments */
  VIEW_NON_RS_PAYMENTS: {
    module: MODULES.NON_RS_PAYMENTS,
    action: ACTIONS.VIEW,
  },
  GET_NON_RS_PAYMENTS: { module: MODULES.NON_RS_PAYMENTS, action: ACTIONS.GET },
  CREATE_NON_RS_PAYMENTS: {
    module: MODULES.NON_RS_PAYMENTS,
    action: ACTIONS.CREATE,
  },
  UPDATE_NON_RS_PAYMENTS: {
    module: MODULES.NON_RS_PAYMENTS,
    action: ACTIONS.UPDATE,
  },
  DELETE_NON_RS_PAYMENTS: {
    module: MODULES.NON_RS_PAYMENTS,
    action: ACTIONS.DELETE,
  },
  APPROVAL_NON_RS_PAYMENTS: {
    module: MODULES.NON_RS_PAYMENTS,
    action: ACTIONS.APPROVAL,
  },

  /* Audit Logs */
  VIEW_AUDIT_LOGS: { module: MODULES.AUDIT_LOGS, action: ACTIONS.VIEW },
  GET_AUDIT_LOGS: { module: MODULES.AUDIT_LOGS, action: ACTIONS.GET },
};

module.exports = {
  MODULES,
  ACTIONS,
  PERMISSIONS,
};
