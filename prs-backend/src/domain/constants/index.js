/* -------------------------------------------------------------------------- */
/*                          Constants Usage Guidelines                          */
/* -------------------------------------------------------------------------- */

/**
 * How to create and use constants:
 * 1. Create a new file in this directory named {EntityName}Constants.js
 * 2. Define your constants using Object.freeze() for immutability
 * 3. Export the constants
 * 4. They will be automatically imported here and available through this index
 *
 * Example:
 *  - ExampleConstants.js
 * const EXAMPLE_TYPES = Object.freeze({
 *   TYPE_ONE: 'Type One',
 *   TYPE_TWO: 'Type Two'
 * });
 *
 * module.exports = {
 *   EXAMPLE_TYPES
 * };
 */

const fs = require('fs');
const path = require('path');

const constants = {};
const constantFiles = fs
  .readdirSync(__dirname)
  .filter(
    (file) =>
      file.indexOf('.') !== 0 &&
      file !== 'index.js' &&
      file.endsWith('Constants.js'),
  );

constantFiles.forEach((file) => {
  const constantName = file.replace('Constants.js', '');
  constants[constantName] = require(path.join(__dirname, file));
});

module.exports = constants;
