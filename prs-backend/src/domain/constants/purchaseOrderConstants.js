const PO_STATUS = Object.freeze({
  FOR_PO_REVIEW: 'for_po_review', // Cascade to RS
  FOR_PO_APPROVAL: 'for_po_approval', // After review (assigned purchase staff to submit)
  FOR_SENDING: 'for_sending',
  FOR_DELIVERY: 'for_delivery', // After approval of (supervisor of purchase staff and purchase head)
  CLOSED_PO: 'closed_po',
  CANCELLED_PO: 'cancelled_po', // Can only cancel if status is from PO_REVIEW
  REJECT_PO: 'reject_po', // Cascaded from PO_APPROVER_STATUS (if one of the approvers reject the PO)
});

const PO_APPROVER_STATUS = Object.freeze({
  PENDING: 'pending',
  REJECTED: 'rejected',
  APPROVED: 'approved',
});

const PO_ITEM_STATUS = Object.freeze({
  NEW: 'new',
  FOR_APPROVAL: 'for_approval',
  APPROVED: 'approved',
});

const TERMS = {
  NET_15: 'NET 15',
  NET_30: 'NET 30',
  CIA: 'Cash in Advance (CIA)',
  COD: 'Cash on Delivery (COD)',
  DP_10: '10% DP, Balance upon delivery',
  DP_20: '20% DP, Balance upon delivery',
  DP_30: '30% DP, Balance upon delivery',
  DP_50: '50% DP, Balance upon delivery',
  DP_80: '80% DP, Balance upon delivery',
  DP_10_RETENTION_10: '10% DP, PB, 10% RETENTION',
  DP_20_RETENTION_10: '20% DP, PB, 10% RETENTION',
  DP_30_RETENTION_10: '30% DP, PB, 10% RETENTION',
};

module.exports = {
  PO_STATUS,
  PO_APPROVER_STATUS,
  PO_ITEM_STATUS,
  TERMS,
};
