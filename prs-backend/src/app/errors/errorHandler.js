const HttpError = require('./httpError');
const { Sequelize } = require('sequelize');
const serverErrors = require('./serverErrors');

const replyHttpError = (httpError, reply) => {
  const statusCode = httpError.status;
  return reply.status(statusCode).send(httpError);
};

const errorhandler = function (error, _request, reply) {
  const errorType =
    error instanceof Sequelize.Error
      ? 'DATABASE ERROR'
      : error?.errorCode || 'INTERNAL SERVER ERROR';

  this.log.error({
    errorType,
    'x-request-id': _request.id,
    message: error?.message,
    stack: error?.stack,
    timestamp: new Date().toISOString(),
  });

  if (error instanceof HttpError) {
    return replyHttpError(error, reply);
  }

  return replyHttpError(serverErrors.INTERNAL_SERVER_ERROR(), reply);
};

module.exports = errorhandler;
