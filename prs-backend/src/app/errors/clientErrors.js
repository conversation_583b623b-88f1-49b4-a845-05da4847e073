const HttpError = require('./httpError');

const UNAUTHORIZED = (payload = {}) => {
  const { message = 'Unauthorized', description } = payload;

  return new HttpError({
    message,
    status: 401,
    description,
    errorCode: 'UNAUTHORIZED',
  });
};

const FORBIDDEN = (payload = {}) => {
  const { message = 'Forbidden', description } = payload;

  return new HttpError({
    message,
    status: 403,
    description,
    errorCode: 'FORBIDDEN',
  });
};

const BAD_REQUEST = (payload = {}) => {
  const {
    message = 'Bad request',
    errorCode = 'BAD_REQUEST',
    description,
  } = payload;

  return new HttpError({
    message,
    status: 400,
    description,
    errorCode,
  });
};

const VALIDATION_ERROR = (payload = {}) => {
  const { message = 'Validation failed', description } = payload;

  return new HttpError({
    message,
    status: 422,
    description,
    errorCode: 'VALIDATION_ERROR',
  });
};

const NOT_FOUND = (payload = {}) => {
  const { message = 'Record not found', description } = payload;

  return new HttpError({
    message,
    status: 404,
    description,
    errorCode: 'NOT_FOUND',
  });
};

const UNPROCESSABLE_ENTITY = (payload = {}) => {
  const { message = 'Unprocessable entity', description } = payload;

  return new HttpError({
    message,
    status: 422,
    description,
    errorCode: 'UNPROCESSABLE_ENTITY',
  });
};

const CUSTOM_ERROR = (payload = {}) => {
  const {
    message = 'An unexpected error occurred.',
    status = 500,
    description,
    errorCode = 'INTERNAL_SERVER_ERROR',
  } = payload;

  return new HttpError({
    message,
    status,
    description,
    errorCode,
  });
};

module.exports = {
  UNAUTHORIZED,
  FORBIDDEN,
  BAD_REQUEST,
  VALIDATION_ERROR,
  NOT_FOUND,
  UNPROCESSABLE_ENTITY,
  CUSTOM_ERROR,
};
