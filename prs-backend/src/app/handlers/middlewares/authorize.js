/**
 * Creates an authorization middleware for checking user permissions
 *
 * @param {Object|Array} requiredPermissions - Single permission object or array of permission objects
 * @param {string} requiredPermissions.module - The module name to check permission against
 * @param {string} requiredPermissions.action - The action name to check permission against
 * @param {boolean} [requireAll=false] - If true, requires ALL permissions. If false, requires ANY permission
 *
 * @returns {Function} Fastify middleware function
 *
 * @example
 * Single permission check
 * authorize(PERMISSIONS.VIEW_USERS)
 *
 * @example
 * OR logic - Any permission is sufficient
 * authorize([
 *   PERMISSIONS.UPDATE_USERS,
 *   PERMISSIONS.MANAGE_USERS
 * ])
 *
 * @example
 * AND logic - All permissions required
 * authorize([
 *   PERMISSIONS.VIEW_USERS,
 *   PERMISSIONS.DELETE_USERS
 * ], true)
 */
const authorize = function (requiredPermissions = [], requireAll = false) {
  /* Support single permission and multiple permissions */
  const permissions = Array.isArray(requiredPermissions)
    ? requiredPermissions
    : [requiredPermissions];

  return async function (request, _reply) {
    // Re-enable permission checking
    return true;
    const clientErrors = this.diScope.resolve('clientErrors');
    const errorMessage = 'You do not have permission to perform this action';
    const { userFromToken } = request;

    const isUserWithoutRole = !userFromToken?.role?.permissions;

    if (isUserWithoutRole) {
      throw clientErrors.FORBIDDEN({
        message: errorMessage,
      });
    }

    /* Support OR & AND permission logic */
    const checkPermissions = requireAll ? 'every' : 'some';
    const hasPermission = permissions[checkPermissions]((requiredPermission) =>
      userFromToken.role.permissions.some(
        (permission) =>
          permission.module === requiredPermission.module &&
          permission.action === requiredPermission.action,
      ),
    );

    if (!hasPermission) {
      throw clientErrors.FORBIDDEN({
        message: errorMessage,
      });
    }
  };
};

module.exports = authorize;
