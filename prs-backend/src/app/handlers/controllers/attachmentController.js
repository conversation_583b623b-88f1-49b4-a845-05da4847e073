class AttachmentController {
  constructor({ utils, entities, clientErrors, attachmentService }) {
    this.utils = utils;
    this.entities = entities;
    this.clientErrors = clientErrors;
    this.attachmentService = attachmentService;
  }

  async createAttachment(request, reply) {
    const { createAttachmentSchema } = this.entities.attachment;
    const { userFromToken, body } = request;
    const { attachments, ...restBody } = { ...body };
    const parsedBody = this.utils.parseDomain(createAttachmentSchema, restBody);
    const { model, modelId } = parsedBody;

    const results = await this.attachmentService.createAttachments({
      model,
      userId: userFromToken.id,
      modelId: modelId || 0,
      parentPath: model,
      attachments,
    });

    return reply.status(200).send(results);
  }

  async getAttachments(request, reply) {
    const { model, modelId } = request.params;
    const { search } = request.query;

    const results = await this.attachmentService.getAttachments({
      model,
      search,
      modelId,
    });

    return reply.status(200).send(results);
  }

  async assignAttachments(request, reply) {
    const { userFromToken, body } = request;
    const { attachmentIds, model, modelId } = body;

    const results = await this.attachmentService.assignAttachmentsToModelId(
      attachmentIds,
      model,
      modelId,
      userFromToken.id,
    );

    return reply
      .status(200)
      .send({ message: 'Attachments assigned successfully', results });
  }

  async removeAttachments(request, reply) {
    const { userFromToken, body } = request;
    const { attachmentIds, model, modelId } = body;

    await this.attachmentService.removeAttachments(
      attachmentIds,
      model,
      modelId,
      userFromToken.id,
    );

    return reply
      .status(200)
      .send({ message: 'Attachment removed successfully' });
  }

  async removeAttachmentById(request, reply) {
    const { id: attachmentId } = request.params;
    await this.attachmentService.deleteSingleAttachment(parseInt(attachmentId));

    return reply.status(200).send({
      message: 'Attachment removed successfully',
    });
  }

  async markAttachmentAsSeen(request, reply) {
    const { model, modelId } = request.params;
    const results = await this.attachmentService.markAttachmentAsSeen(
      model,
      modelId,
      request.userFromToken,
    );
    return reply.status(200).send(results);
  }
}

module.exports = AttachmentController;
