class NoteController {
  constructor({ noteService, clientErrors }) {
    this.noteService = noteService;
    this.clientErrors = clientErrors;
  }

  async createNote(request, reply) {
    const { userFromToken, body } = request;
    const { model, modelId, userType, commentType, note } = body;
    const result = await this.noteService.createNote({
      model,
      modelId,
      userName: userFromToken.fullNameUser,
      userType,
      commentType,
      note,
    });

    return reply.status(201).send({
      message: 'Note created successfully.',
      data: result,
    });
  }

  async getNotes(request, reply) {
    const { model, modelId } = request.params;
    const { dateFrom, dateTo } = request.query;
    const { noteService } = this;
    const result = await noteService.getNotes({
      model,
      modelId,
      dateFrom,
      dateTo,
    });
    return reply.status(200).send(result);
  }

  async markNoteAsSeen(request, reply) {
    const { model, modelId } = request.params;
    const { dateFrom, dateTo } = request.query;
    const { noteService } = this;
    const result = await noteService.markNoteAsSeen({
      model,
      modelId,
      userFromToken: request.userFromToken,
      dateFrom,
      dateTo,
    });
    return reply.status(200).send(result);
  }
}

module.exports = NoteController;
