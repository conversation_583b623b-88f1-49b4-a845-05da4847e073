class Project {
  constructor(container) {
    const {
      db,
      entities,
      constants,
      userService,
      tradeService,
      projectService,
      companyService,
      syncRepository,
      projectRepository,
      projectApprovalService,
      utils,
    } = container;

    this.db = db;
    this.entities = entities;
    this.userService = userService;
    this.tradeService = tradeService;
    this.constants = constants;
    this.projectService = projectService;
    this.companyService = companyService;
    this.syncRepository = syncRepository;
    this.projectRepository = projectRepository;
    this.projectApprovalService = projectApprovalService;
    this.utils = utils;
  }

  async getProject(request, reply) {
    const { projectId } = request.params;
    const existingProject =
      await this.projectService.getExistingProject(projectId);

    return reply.status(200).send(existingProject);
  }

  async syncProjects(_request, reply) {
    const userDetails = {
      userId: _request.userFromToken.id,
      username: _request.userFromToken.username,
      role: _request.userFromToken.role?.name,
    };
    const lastSyncedAt = await this.projectService.syncProjects(userDetails);

    return reply.status(200).send({
      lastSyncedAt,
    });
  }

  async getAllProjects(request, reply) {
    const { sortBy, filterBy, ...queries } = request.query;
    const { projectSortSchema, projectFilterSchema } = this.entities.project;
    const parsedSortBy = projectSortSchema.parse(sortBy);
    const parsedFilterBy = projectFilterSchema.parse(filterBy);
    const filterByWhereClause =
      this.utils.buildFilterWhereClause(parsedFilterBy);

    const projectList = await this.projectRepository.getAllProjects({
      ...queries,
      filterBy: filterByWhereClause,
      order: parsedSortBy,
    });

    const projectSync = await this.syncRepository.findByModel('project');

    return reply.status(200).send({
      ...projectList,
      lastSyncedAt: projectSync?.lastSyncedAt || null,
    });
  }

  async updateProject(request, reply) {
    const { projectId } = request.params;
    const projectData = request.body;

    /* Throw error if project does not exist */
    const existingProject =
      await this.projectService.getExistingProject(projectId);

    await this.projectRepository.update(
      { id: existingProject.id },
      projectData,
    );

    return reply.status(200).send({
      message: 'Project updated successfully',
      previousValue: existingProject,
    });
  }

  async getProjectApprovals(request, reply) {
    const projectApprovals =
      await this.projectApprovalService.getProjectApprovals(
        request.params.projectId,
        request.query.type,
      );

    return reply.status(200).send(projectApprovals);
  }

  async setupProjectApprovals(request, reply) {
    const { setupApprovalSchema } = this.entities.approval;
    const parsedBody = setupApprovalSchema.parse(request.body);
    const { approvalTypeCode, approvers, optionalApprovers } = parsedBody;

    const existingProject = await this.projectService.getExistingProject(
      parseInt(request.params.projectId),
    );

    const projectApprovals =
      await this.projectApprovalService.getProjectApprovals(
        existingProject.id,
        request.body.type,
      );

    /* Check if approvers exist / currently active */
    const allApproverIds = [
      ...approvers.map((a) => a.approverId),
      ...optionalApprovers.map((a) => a.approverId),
    ];

    await this.userService.validateMultipleUsers(allApproverIds, {
      roleNames: Object.values(this.constants.user.APPROVERS),
    });

    const projectApprovers =
      await this.projectApprovalService.setupProjectApprovals({
        projectId: existingProject.id,
        approvalTypeCode,
        approvers,
        optionalApprovers,
      });

    return reply
      .status(200)
      .send({ ...projectApprovers, previousValue: projectApprovals });
  }

  async assignProjectEngineers(request, reply) {
    const { USER_TYPES } = this.constants.user;
    const { engineerIds } = request.body;
    const { projectId, tradeId } = request.params;

    const existingProject =
      await this.projectService.getExistingProject(projectId);

    const existingTrade = await this.tradeService.getTradeById(tradeId);

    await this.userService.validateMultipleUsers(engineerIds, {
      roleNames: [USER_TYPES.ENGINEERS],
    });

    await this.projectService.addProjectEngineers({
      projectId: existingProject.id,
      tradeId: existingTrade.id,
      engineerIds,
    });

    return reply.status(200).send({
      message: 'Engineers assigned successfully',
    });
  }

  async getProjectEngineers(request, reply) {
    const { projectId, tradeId } = request.params;

    const engineers = await this.projectService.getProjectEngineers(
      projectId,
      tradeId,
    );

    return reply.status(200).send(engineers);
  }

  async deleteEngineer(request, reply) {
    const { projectId, tradeId, engineerId } = request.params;

    const parsedProjectId = parseInt(projectId);
    const parsedTradeId = parseInt(tradeId);
    const parsedEngineerId = parseInt(engineerId);

    const existingEngineer = await this.projectService.getProjectTradeEngineer({
      projectId: parsedProjectId,
      tradeId: parsedTradeId,
      engineerId: parsedEngineerId,
    });

    await this.projectService.deleteProjectTradeEngineer(existingEngineer.id);

    return reply.status(200).send({
      message: 'Engineer deleted successfully',
    });
  }

  async getAvailableEngineers(request, reply) {
    const { USER_TYPES } = this.constants.user;
    const { projectId, tradeId } = request.params;

    const { data: projectEngineers } =
      await this.projectService.getProjectEngineers(projectId, tradeId);

    const engineers = await this.userService.getUsersByRoleName({
      where: {
        id: {
          [this.db.Sequelize.Op.notIn]: projectEngineers.map(
            (e) => e.engineer.id,
          ),
        },
      },
      roleNames: USER_TYPES.ENGINEERS,
    });

    return reply.status(200).send(engineers);
  }
}

module.exports = Project;
