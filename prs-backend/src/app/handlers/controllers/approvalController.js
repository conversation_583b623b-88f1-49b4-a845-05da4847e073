class Approval {
  constructor(container) {
    const { constants, approvalTypeRepository } = container;

    this.constants = constants;
    this.approvalTypeRepository = approvalTypeRepository;
  }

  async getApprovalTypes(_request, reply) {
    const approvalTypes = await this.approvalTypeRepository.findAll({
      paginate: false,
    });

    return reply.status(200).send(approvalTypes);
  }

  async getApprovalAreas(_request, reply) {
    const { area } = this.constants;
    const areas = Object.values(area.ASSOCIATION_AREAS);

    return reply.status(200).send({
      data: areas,
      total: areas.length,
    });
  }
}

module.exports = Approval;
