const fs = require('node:fs/promises');
const fsSync = require('node:fs');
const path = require('path');

const HTML_FOLDER = path.join(__dirname, '../../../templates/html');
const PDF_FOLDER = path.join(__dirname, '../../../templates/pdf');

class Template {
  constructor(container) {
    const { templateService, fastify, clientErrors } = container;

    this.fastify = fastify;
    this.templateService = templateService;
    this.clientErrors = clientErrors;
    this.handlebarsTemplateFolder = path.join(
      path.resolve(),
      'src',
      'templates',
      'hbs',
    );
  }

  async generateTemplate(request, reply) {
    this.fastify.log.info(`Generating HTML to PDF templates...`);

    try {
      await fs.access(HTML_FOLDER);
      const stats = await fs.stat(HTML_FOLDER);

      if (!stats.isDirectory()) {
        throw this.clientErrors.NOT_FOUND({
          message: `Input folder "${HTML_FOLDER}" is not a directory.`,
        });
      }

      if (!fsSync.existsSync(PDF_FOLDER)) {
        try {
          await fs.mkdir(PDF_FOLDER, { recursive: true });
        } catch (mkdirError) {
          console.error(
            `Error creating output folder "${PDF_FOLDER}":`,
            mkdirError,
          );
          reply.status(500).send({
            error: `Failed to create output folder: ${mkdirError.message}`,
          });
          return;
        }
      }

      const templates = await fs.readdir(HTML_FOLDER);

      if (templates.length === 0) {
        throw this.clientErrors.NOT_FOUND({
          message: `No Templates files found in HTML folder.`,
        });
      }

      await this.templateService.generateTemplate(
        templates,
        HTML_FOLDER,
        PDF_FOLDER,
      );

      return reply.status(200).send({
        meta: 'Successfully created PDF templates',
      });
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Template;
