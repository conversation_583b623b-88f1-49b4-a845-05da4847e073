class Auth {
  constructor(container) {
    const { authService, userService, constants, notificationService } =
      container;

    this.authService = authService;
    this.userService = userService;
    this.userConstants = constants.user;
    this.notificationService = notificationService;
  }

  async login(request, reply) {
    const existingUser = await this.authService.verifyUser(request.body);

    //TODO: Remove after development
    const isByPassOTP =
      process.env.BYPASS_OTP === 'true' || process.env.BYPASS_OTP === true;

    if (isByPassOTP) {
      return this.authService.sendAccessToken(existingUser.id, reply);
    }

    /* Force User to Update temporary password - 5 mins */
    if (existingUser.isPasswordTemporary) {
      return this.authService.sendTempPassToken(existingUser.id, reply);
    }

    /* Force User to Setup OTP if otpSecret does not exist - 10 mins */
    if (!existingUser.otpSecret) {
      return this.authService.sendUserOtpSecret(
        existingUser.id,
        existingUser.username,
        reply,
      );
    }

    /* Send verify OTP token for otp verification - 5 mins */
    return this.authService.sendVerifyOTPToken(existingUser.id, reply);
  }

  async verifyOTP(request, reply) {
    const userDetails = this.authService.verifyOTP(
      request.userFromToken,
      request.body.otp,
    );

    return this.authService.sendAccessToken(userDetails.id, reply);
  }

  async setupOTP(request, reply) {
    const userDetails = await this.authService.saveOTPSecret(
      request.userFromToken,
      request.body,
    );

    return this.authService.sendAccessToken(userDetails.id, reply);
  }

  async updateTempPassword(request, reply) {
    const { userFromToken, body } = request;
    const userDetails = await this.authService.updateTempPassword(
      userFromToken,
      body.password,
    );

    if (!userDetails.otpSecret) {
      return this.authService.sendUserOtpSecret(
        userDetails.id,
        userDetails.username,
        reply,
      );
    }

    return reply.status(200).send({
      message: 'Password updated successfully',
    });
  }

  async refreshUserToken(request, reply) {
    const { userFromToken } = request;
    return this.authService.sendAccessToken(userFromToken.id, reply);
  }

  async forgotPassword(request, reply) {
    const { username } = request.body;
    const existingUser = await this.userService.getUserByUsername(username);
    await this.notificationService.createPasswordResetNotification(
      existingUser,
    );

    return reply.status(200).send({
      message:
        'Password reset request sent, please wait for IT Admin to reset your password',
    });
  }
}

module.exports = Auth;
