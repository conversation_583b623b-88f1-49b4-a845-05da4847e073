class Download {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      leaveService,
      fastify,
      leaveRepository,
      clientErrors,
      downloadService,
      nonRequisitionRepository,
    } = container;
    this.fastify = fastify;
    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.leaveService = leaveService;
    this.leaveRepository = leaveRepository;
    this.clientErrors = clientErrors;
    this.downloadService = downloadService;
    this.nonRequisitionRepository = nonRequisitionRepository;
  }

  async downloadDashboard(request, reply) {
    const { userFromToken } = request;
    const { requestType } = request.query;

    let fileName = 'All';

    const date = new Date(new Date().setHours(0, 0, 0, 0));

    if (requestType === 'my_approval') {
      fileName = 'My Approvals';
    }

    if (requestType === 'my_request') {
      fileName = 'My Requests';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const formattedDate = `${year}${month}${day}`;

    const result = await this.downloadService.downloadDashboard({
      userId: userFromToken.id,
      formattedDate,
      requestType,
    });

    return reply
      .header(
        'Content-Disposition',
        `attachment; filename="${fileName}_${formattedDate}.xlsx"`,
      )
      .send(result);
  }

  async downloadNonRSDashboard(request, reply) {
    const { userFromToken } = request;
    const { category } = request.body;
    const userId = userFromToken.id;
    let fileName = category || 'all';

    const nonRSData = await this.nonRequisitionRepository.getAllNonRs({
      userId,
      category,
      paginate: false,
    });

    if (!nonRSData.total) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No dashboard data to download',
      });
    }

    const downloadPayload = nonRSData.data.map((data) => {
      const {
        chargeTo,
        status,
        totalDiscountedAmount,
        updatedAt,
        requestor,
        nonRsLetter,
        draftNonRsNumber,
        nonRsNumber,
      } = data;

      const isDraft = status === 'draft';
      const nonRSNumber = isDraft
        ? `NRS-TMP-${nonRsLetter}${draftNonRsNumber}`
        : `NRS-${nonRsLetter}${nonRsNumber}`;
      const requestedBy = requestor?.fullName ?? '';

      return {
        'Non-RS Number': nonRSNumber,
        'Charge To': chargeTo,
        'Requested By': requestedBy,
        'Last Updated': updatedAt,
        Amount: totalDiscountedAmount,
        Status: status,
      };
    });

    const excelFile = this.downloadService.generateExcelFile(downloadPayload);

    return reply.status(200).send({ fileName, data: excelFile });
  }
}

module.exports = Download;
