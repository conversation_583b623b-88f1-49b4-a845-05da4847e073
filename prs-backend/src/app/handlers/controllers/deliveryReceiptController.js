const { ZodError } = require('zod');
const fs = require('fs');
const {
  PAGINATION_DEFAULTS,
} = require('../../../domain/constants/deliveryReceiptConstants');
const {
  createDeliveryReceiptSchema,
} = require('../../../domain/entities/deliveryReceiptEntity');
const { convertDateToDDMMMYYYY } = require('../../../app/utils');

class DeliveryReceiptController {
  constructor({ deliveryReceiptService, purchaseOrderService, clientErrors, templateService, utils }) {
    this.deliveryReceiptService = deliveryReceiptService;
    this.purchaseOrderService = purchaseOrderService;
    this.clientErrors = clientErrors;
    this.templateService = templateService;
    this.utils = utils;
  }

  async createDeliveryReceipt(request, reply) {
    const { body, userFromToken } = request;
    createDeliveryReceiptSchema.parse(body);
    const deliveryReceipt = await this.deliveryReceiptService.createDeliveryReceipt(
      body, userFromToken, request.transaction
    );

    return reply.status(201).send({ ...deliveryReceipt });
  }

  async getDeliveryReceiptById(request, reply) {
    const { type } = request.query;
    const id = parseInt(request.params.id);

    const deliveryReceipt =
      await this.deliveryReceiptService.getDeliveryReceiptById(
        id,
        type
      );

    if (!deliveryReceipt) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt with id of ${request.params.id} not found`,
      });
    }

    return reply.status(200).send({ ...deliveryReceipt });
  }

  async getDeliveryReceiptsFromRequisitionId(request, reply) {
    const { query, params } = request;
    const deliveryReceipts = await this.deliveryReceiptService.getDeliveryReceiptsFromRequisitionId(
      params.requisitionId,
      {
        search: query.search,
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
      }
    );

    return reply.status(200).send({ ...deliveryReceipts });
  }

  async getDeliveryReceiptsFromPurchaseOrderId(request, reply) {
    const { params, query } = request;
    const { noInvoice } = query;
    
    const deliveryReceipts = await this.deliveryReceiptService.getDeliveryReceiptsFromPurchaseOrderId(
      params.purchaseOrderId,
      { 
        noInvoice: noInvoice === 'true'
      }
    );

    return reply.status(200).send({ ...deliveryReceipts });
  }

  async getDeliveryReturnsFromRequisitionId(request, reply) {
    const { query, params } = request;
    const deliveryReturns = await this.deliveryReceiptService.getDeliveryReturnsFromRequisitionId(
      params.requisitionId,
      {
        search: query.search,
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
      }
    );

    return reply.status(200).send({ ...deliveryReturns });
  }

  async updateDeliveryReceiptById(request, reply) {
    const { body, params, userFromToken } = request;
    const deliveryReceipt =
      await this.deliveryReceiptService.updateDeliveryReceiptById(
        params.id,
        body,
        userFromToken,
        request.transaction
      );

    if (!deliveryReceipt) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt with id of ${request.params.id} not found`,
      });
    }

    return reply.status(200).send({ 
      id: deliveryReceipt.id
    });
  }

  async getDeliveryReceiptItemHistory(request, reply) {
    const historyRecords =
      await this.deliveryReceiptService.getDeliveryReceiptItemHistory(
        request.params.itemId,
        {
          page: request.query.page || PAGINATION_DEFAULTS.PAGE,
          limit: request.query.limit || PAGINATION_DEFAULTS.LIMIT,
        },
      );

    return reply.status(200).send({ ...historyRecords });
  }

  async getDeliveryReceiptItems(request, reply) {
    const { query, params } = request;
    const deliveryReceiptItems = await this.deliveryReceiptService.getDeliveryReceiptItems(
      params.id,
      query
    );

    return reply.status(200).send({ ...deliveryReceiptItems });
  }

  async generatePdf(request, reply) {
    const { id } = request.params;
    const drId = parseInt(id);

    try {
      const [deliveryReceipt, items] = await Promise.all([
        this.deliveryReceiptService.getDeliveryReceiptById(drId),
        this.deliveryReceiptService.getDeliveryReceiptItems(drId, { paginate: false }),
      ]);

      if (!items || !items.total) {
        throw this.clientErrors.BAD_REQUEST({
          message: 'No items found for this Delivery Receipt',
        });
      }

      const supplier = await this.purchaseOrderService.getSupplierDetails(
        deliveryReceipt.poId
      );

      const headers = {
        companyName: supplier?.name || '---',
        businessStyle: supplier?.name || '---',
        tin: supplier?.tin || '---',
        rrNumber: deliveryReceipt.drNumber || '---',
        poNumber: deliveryReceipt.poNumber || '---',
        issuedDate: convertDateToDDMMMYYYY(deliveryReceipt.issuedDate, true) || '---',
        deliveryInvoiceNo: deliveryReceipt.irNumber || '---',
        supplierIssuedDate: convertDateToDDMMMYYYY(deliveryReceipt.supplierDeliveryIssuedDate, true) || '---',
      };

      const allItems = items.data.map((item, index) => ({
        id: index + 1,
        itemName: `${item.itemDes || '---'}`,
        unit: item.unit || '---',
        requestedQty: Number(item.qtyOrdered || 0).toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 }),
        deliveredQty: Number(item.qtyDelivered || 0).toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 }),
        returnedQty: item.qtyReturned === 0 ? '---' : Number(item.qtyReturned).toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 }),
      }));

      const { pagesData, totalPages, totalItems } = this.utils.paginateItemsWithDifferentSizes(allItems, 18, 21);

      const data = {
        ...headers,
        pagesData,
        totalPages,
        totalItems,
      };

      const result = await this.templateService.generateDynamicTemplate(
        data,
        'receiving-report.hbs',
        'receiving_report_downloads',
        'RR',
      );

      const fileStream = fs.createReadStream(result.filePath);

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(fileStream);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = DeliveryReceiptController;
