const fp = require('fastify-plugin');

async function transactionHooks(fastify, opts) {
  fastify.decorate('useTransaction', function () {
    this.addHook('onRequest', async (request, reply) => {
      const transaction = await fastify.diScope
        .resolve('db')
        .sequelize.transaction();
      request.transaction = transaction;
      request.transactionFinished = false;
    });

    this.addHook('onResponse', async (request, reply) => {
      if (!request.transaction || request.transactionFinished) return;
      
      try {
        if (reply.statusCode < 400) {
          await request.transaction.commit();
        } else {
          await request.transaction.rollback();
        }
        request.transactionFinished = true;
      } catch (error) {
        request.log.error(`Transaction error: ${error.message}`);
        if (
          !request.transactionFinished &&
          !error.message.includes('finished with state')
        ) {
          try {
            await request.transaction.rollback();
          } catch (rollbackError) {
            request.log.error(`Rollback error: ${rollbackError.message}`);
          }
        }
        request.transactionFinished = true;
      }
    });

    this.addHook('onError', async (request, reply, error) => {
      if (!request.transaction || request.transactionFinished) return;

      try {
        await request.transaction.rollback();
        request.transactionFinished = true;
      } catch (rollbackError) {
        request.log.error(`Rollback error: ${rollbackError.message}`);
      }
    });
  });
}

module.exports = fp(transactionHooks);
