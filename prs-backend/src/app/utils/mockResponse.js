const mockSupplierResponse = [
  {
    PAYCOD: "A001",
    LNAME: "AA ALUMINUM SUP",
    FNAME: "PLY, INC.",
    MI: "",
    TEXT1: "TEXT 1",
    TEXT2: "TEXT 2",
    TEXT3: "TEXT 3",
    ADDR1: "1828 EVANGELISTA ST.GEN TINIO",
    ADDR2: "BANGKAL, MAKATI CITY",
    CITICD: "F",
    NATINC: "SUPPLIER",
    TIN: "000-391-826-002-V",
    ICCODE: "C",
  },
  {
    PAYCOD: "A002",
    LNAME: "ABS-CBN BROADCA",
    FNAME: "STING CORPORATION",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "1828 EVANGELISTA ST.GEN TINIO",
    ADDR2: "BANGKAL, MAKATI CITY",
    CITICD: "F",
    NATINC: "BROKER",
    TIN: "000-406-761",
    ICCODE: "C",
  },
  {
    PAYCOD: "A011",
    LNA<PERSON>: "ADVANCE CRAFTS,",
    FNAME: "INC.",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "1828 EVANGELISTA ST.GEN TINIO",
    ADDR2: "BANGKAL, MAKATI CITY",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "000-103-993",
    ICCODE: "C",
  },
  {
    PAYCOD: "A012",
    LNAME: "ADVANCE ENGINEE",
    FNAME: "RING CORPORATION",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "1828 EVANGELISTA ST.GEN TINIO",
    ADDR2: "BANGKAL, MAKATI CITY",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "0429-956-6",
    ICCODE: "C",
  },
  {
    PAYCOD: "A013",
    LNAME: "ADVATECH INDUST",
    FNAME: "RIES, INC.",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "ADVATECH HSE, BUENCONSEJO ST.",
    ADDR2: "MANDALUYONG CITY",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "000-050-390-V",
    ICCODE: "C",
  },
  {
    PAYCOD: "P133",
    LNAME: "PARTIDO'S BEST H",
    FNAME: "OME APPLIANCE",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "CASH & CARRY SHOPPING MALL,SS",
    ADDR2: "H-WAY. MAKATI CITY",
    CITICD: "F",
    NATINC: "SUPPLIER",
    TIN: "321-100-199-352-V",
    ICCODE: "C",
  },
  {
    PAYCOD: "P137",
    LNAME: "PASTORAL STEEL",
    FNAME: "AND ALUMINUM GLASS",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "",
    ADDR2: "",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "151-219-257-NV",
    ICCODE: "C",
  },
  {
    PAYCOD: "P151",
    LNAME: "ACUNA",
    FNAME: "AVELINA PANES",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "12F CC10 T1 HV DELA COSTA ST.,",
    ADDR2: "MAKATI CITY",
    CITICD: "F",
    NATINC: "BROKER",
    TIN: "105-330-809",
    ICCODE: "I",
  },
  {
    PAYCOD: "R011",
    LNAME: "REPUBLIC COMMOD",
    FNAME: "ITIES CORPORATION",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "",
    ADDR2: "",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "000-167-590V",
    ICCODE: "C",
  },
  {
    PAYCOD: "R015",
    LNAME: "ROADSIDE MOTORW",
    FNAME: "ORKS & GENERAL MDSE",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "",
    ADDR2: "",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "4834769-6",
    ICCODE: "C",
  },
  {
    PAYCOD: "R016",
    LNAME: "ROCKBUILT ENTER",
    FNAME: "PRISES, INC.",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "KM 18 ORTIGAS AVE.EXT. CAINTA,",
    ADDR2: "RIZAL",
    CITICD: "F",
    NATINC: "SUPPLIER",
    TIN: "046-000-099-344-V",
    ICCODE: "C",
  },
  {
    PAYCOD: "R017",
    LNAME: "ROMAGO ELECTRIC",
    FNAME: "CO. INC.",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "",
    ADDR2: "",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "000-061-963",
    ICCODE: "C",
  },
  {
    PAYCOD: "R018",
    LNAME: "ROMAGO INC.",
    FNAME: "",
    MI: "",
    TEXT1: "",
    TEXT2: "",
    TEXT3: "",
    ADDR1: "",
    ADDR2: "",
    CITICD: "F",
    NATINC: "CONTRACTOR",
    TIN: "000-061-963",
    ICCODE: "C",
  },
];


// Function to return a promise that resolves with the mock data
const getMockSupplierResponse = async () => {
  return new Promise((resolve) => {
    resolve(mockSupplierResponse);
  });
};

module.exports = {
  getMockSupplierResponse,
};
