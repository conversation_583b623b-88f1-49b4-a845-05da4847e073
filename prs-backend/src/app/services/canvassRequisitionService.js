const fs = require('fs');

class CanvassRequisitionService {
  constructor({
    canvassRequisitionRepository,
    syncRepository,
    fastify,
    utils,
    commentRepository,
    db,
    attachmentRepository,
    clientErrors,
  }) {
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.syncRepository = syncRepository;
    this.fastify = fastify;
    this.commentRepository = commentRepository;
    this.db = db;
    this.attachmentRepository = attachmentRepository;
    this.attachmentModel = db.attachmentModel;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
  }

  async commentBadge(userId, canvassRequisitionId) {
    const commentsCount = await this.commentRepository.count({
      where: {
        commentedBy: userId,
        model: 'canvassRequisition',
        modelId: canvassRequisitionId,
      },
      include: [
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
        },
      ],
    });

    const badgeCount = await this.commentRepository.count({
      where: {
        commentedBy: userId,
        model: 'canvassRequisition',
        modelId: canvassRequisitionId,
      },
      include: [
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
          required: true,
        },
      ],
    });

    return commentsCount === badgeCount ? false : true;
  }

  async attachmentBadge(userId, canvassRequisitionId) {
    const attachmentsCount = await this.attachmentRepository.count({
      where: {
        model: 'canvassRequisition',
        modelId: canvassRequisitionId,
        userId,
      },
      include: [
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
        },
      ],
    });

    const badgeCount = await this.attachmentRepository.count({
      where: {
        model: 'canvassRequisition',
        modelId: canvassRequisitionId,
        userId,
      },
      include: [
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
          required: true,
        },
      ],
    });

    return attachmentsCount === badgeCount ? false : true;
  }
}

module.exports = CanvassRequisitionService;