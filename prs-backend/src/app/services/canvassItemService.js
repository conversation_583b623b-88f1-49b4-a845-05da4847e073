class CanvassItemService {
  constructor({ clientErrors, canvassItemRepository }) {
    this.clientErrors = clientErrors;
    this.canvassItemRepository = canvassItemRepository;
  }

  async getAllCanvassItems(payload = {}, options = {}) {
    const { canvassId, query = {} } = payload;
    const { sortBy, filterBy, ...queries } = query;
    const canvassItemList = await this.canvassItemRepository.getAllCanvassItems(
      {
        ...queries,
        whereClause: {
          canvassRequisitionId: canvassId,
        },
      },
      options,
    );

    return canvassItemList;
  }

  async getCanvassItemById(canvassItemId) {
    const canvassItem =
      await this.canvassItemRepository.getCanvassItemById(canvassItemId);

    if (!canvassItem) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Canvass item not found',
      });
    }

    return canvassItem;
  }
}

module.exports = CanvassItemService;
