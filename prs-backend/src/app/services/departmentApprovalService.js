class DepartmentApprovalService {
  constructor ({
    db,
    clientErrors,
    approvalTypeRepository,
    departmentApprovalRepository,
    requisitionRepository,
    requisitionApproverRepository,
    fastify,
  }) {
    this.db = db;
    this.clientErrors = clientErrors;
    this.approvalTypeRepository = approvalTypeRepository;
    this.departmentApprovalRepository = departmentApprovalRepository;
    this.requisitionRepository = requisitionRepository;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.fastify = fastify;
  }

  async setupDepartmentApprovals(data) {
    const {
      approvers,
      departmentId,
      approvalTypeCode,
      optionalApprovers = [],
    } = data;

    const deptTransaction = await this.db.sequelize.transaction();

    try {
      await this.departmentApprovalRepository.destroy(
        {
          departmentId,
          approvalTypeCode,
        },
        {
          transaction: deptTransaction,
        },
      );

      const mainApprovalsToCreate = approvers.map((approver) => ({
        departmentId,
        approvalTypeCode,
        level: approver.level,
        approverId: approver.approverId,
        isOptional: false,
      }));

      let optionalApprovalsToCreate = [];
      if (optionalApprovers.length === 1) {
        optionalApprovalsToCreate.push({
          level: 1,
          departmentId,
          isOptional: true,
          approvalTypeCode,
          approverId: optionalApprovers[0].approverId,
        });
      }

      const allApprovalsToCreate = [
        ...mainApprovalsToCreate,
        ...optionalApprovalsToCreate,
      ];

      const createdApprovals =
        await this.departmentApprovalRepository.bulkCreate(
          allApprovalsToCreate,
          {
            transaction: deptTransaction,
          },
        );

      await this.#cascadeOptionalApprover({
        optionalApprovalsToCreate,
        deptTransaction,
      });

      await deptTransaction.commit();
      return createdApprovals;
    } catch (error) {
      await deptTransaction.rollback();
      throw error;
    }
  }

  async getDepartmentApprovals(departmentId, approvalTypeCode) {
    const whereClause = {
      departmentId,
    };

    if (approvalTypeCode) {
      whereClause.approvalTypeCode = approvalTypeCode;
    }

    const allApprovals =
      await this.departmentApprovalRepository.getAllApprovals({
        where: whereClause,
      });

    const groupedApprovals = this.transformApprovals(allApprovals.data);

    return {
      data: Object.values(groupedApprovals),
      total: allApprovals.total,
    };
  }

  async #cascadeOptionalApprover({ optionalApprovalsToCreate, deptTransaction }) {
    if (optionalApprovalsToCreate.length === 0) {
      return;
    }

    const { departmentId, approverId } = optionalApprovalsToCreate[0];

    const {
      data: requisitionData,
      total: requisitionTotal,
    } = await this.requisitionRepository.findAll(
      { where: { departmentId } },
      { transaction: deptTransaction },
    );

    if (requisitionTotal === 0) {
      this.fastify.log.info(`NOTHING_TO_CASCADE_FOR_DEPARTMENT_ID: ${departmentId}`);
      return;
    }

    await Promise.all(requisitionData.map(async (requisition) => {
      const requisitionApproverData = await this.requisitionApproverRepository.findOne({
        where: {
          requisitionId: requisition.id,
          modelType: 'department',
          modelId: departmentId,
          approverId: null,
        },
        transaction: deptTransaction,
      });

      if (!requisitionApproverData) {
        this.fastify.log.info(`NOTHING_TO_CASCADE: department_id: ${departmentId}, requisition_id: ${requisition.id}`);
        return true;
      }

      await this.requisitionApproverRepository.update(
        { id: requisitionApproverData.id },
        { approverId },
        { transaction: deptTransaction },
      );

      this.fastify.log.info(`CASCADED_OPTIONAL_APPROVER: department_id: ${departmentId}, requisition_id: ${requisition.id}`);
    }));

    return true;
  }

  transformApprovals(allApprovals = []) {
    const groupedApprovals = allApprovals.reduce((groups, approval) => {
      const type = approval.approvalTypeCode;
      if (!groups[type]) {
        groups[type] = {
          type,
          approvers: [],
          optionalApprovers: [],
        };
      }

      const approverData = {
        id: approval.id,
        departmentId: approval.departmentId,
        level: approval.level,
        createdAt: approval.createdAt,
        updatedAt: approval.updatedAt,
        approver: approval.approver,
      };

      if (approval.isOptional) {
        groups[type].optionalApprovers.push(approverData);
      } else {
        groups[type].approvers.push(approverData);
      }

      return groups;
    }, {});

    return groupedApprovals;
  }
}

module.exports = DepartmentApprovalService;
