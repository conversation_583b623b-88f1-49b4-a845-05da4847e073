const converter = require('json-2-csv');
class LeaveService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      leaveRepository,
      fastify,
      requisitionRepository,
      nonRequisitionRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.leaveRepository = leaveRepository;
    this.fastify = fastify;
    this.Sequelize = db.Sequelize;
    this.requisitionRepository = requisitionRepository;
    this.nonRequisitionRepository = nonRequisitionRepository;
  }

  #generateNewLine() {
    return '\r\n';
  }

  #generateDateLine() {
    const formattedDate = this.utils.formatDateYYYYMMDD();
    const dateLine = { [`Extract as of ${formattedDate}`]: '' };

    return converter.json2csv(dateLine);
  }

  #formatExcelFile(...lines) {
    return lines.join('');
  }

  async downloadDashboard(request) {
    const { userId, formattedDate, requestType } = request;
    const { data } = await this.requisitionRepository.downloadRequisitions({
      paginate: false,
      userId,
      formattedDate,
      requestType,
    });
    const newLine = '\r\n';

    const dateLine = { [`Extract as of ${formattedDate}`]: '' };

    const transformData = [];

    data.forEach((data) =>
      transformData.push({
        [`Ref No`]:
          data.status === 'draft'
            ? `TMP-${data.rs_number}`
            : `${data.rs_number}`,
        Type: `${data.type}`,
        Requester: `${data.createdByUser.fullName}`,
        Company: `${data.company.name}`,
        Project: `${data.project.name}`,
        Department: `${data.department.name}`,
        ['Date Requested']: `${data.date_requested}`,
        ['Last Updated']: `${data.last_updated}`,
        Status: data.status,
      }),
    );

    const csv =
      (await converter.json2csv(dateLine)) +
      newLine +
      (await converter.json2csv(transformData));

    return csv;
  }

  generateExcelFile(data) {
    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(data),
    );
  }
}

module.exports = LeaveService;
