const fs = require('fs');
const path = require('path');
class SupplierService {
  constructor({
    supplierRepository,
    syncRepository,
    fastify,
    utils,
    commentRepository,
    db,
    attachmentRepository,
    attachmentBadgeRepository,
    clientErrors,
    canvassItemSupplierRepository,
  }) {
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}/suppliers`,
    });
    this.supplierRepository = supplierRepository;
    this.syncRepository = syncRepository;
    this.fastify = fastify;
    this.commentRepository = commentRepository;
    this.db = db;
    this.attachmentRepository = attachmentRepository;
    this.attachmentBadgeRepository = attachmentBadgeRepository;
    this.attachmentModel = db.attachmentModel;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.canvassItemSupplierRepository = canvassItemSupplierRepository;
  }

  async syncSuppliers(userId) {
    const result = await this.httpClient.get();
    const suppliers = result.data ?? [];
    const mappedSuppliers = suppliers.map((supplier) => ({
      userId,
      name: `${supplier.FNAME} ${supplier.LNAME} ${supplier.TEXT1} ${supplier.TEXT2} ${supplier.TEXT3}`.trim(),
      tin: supplier.TIN,
      address: `${supplier.ADDR1} ${supplier.ADDR2}`.trim(),
      citizenshipCode: supplier.CITICD,
      natureOfIncome: supplier.NATINC,
      payCode: supplier.PAYCOD,
      iccode: supplier.ICCODE,
      status: 'ACTIVE',
    }));

    await this.supplierRepository.bulkCreate(mappedSuppliers);

    return await this.syncRepository.updateLastSynced('supplier');
  }

  async commentBadge(userId, supplierId) {
    const commentsCount = await this.commentRepository.count({
      where: {
        commentedBy: userId,
        model: 'supplier',
        modelId: supplierId,
      },
      include: [
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
        },
      ],
    });

    const badgeCount = await this.commentRepository.count({
      where: {
        commentedBy: userId,
        model: 'supplier',
        modelId: supplierId,
      },
      include: [
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
          required: true,
        },
      ],
    });

    return commentsCount === badgeCount ? false : true;
  }

  async attachmentBadge(userId, supplierId) {
    const attachmentsCount = await this.attachmentRepository.count({
      where: {
        model: 'supplier',
        modelId: supplierId,
        userId,
      },
      include: [
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
        },
      ],
    });

    const badgeCount = await this.attachmentRepository.count({
      where: {
        model: 'supplier',
        modelId: supplierId,
        userId,
      },
      include: [
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
          required: true,
        },
      ],
    });

    return attachmentsCount === badgeCount ? false : true;
  }

  async editAttachment(payload) {
    const { id, path: filePath } = payload;

    this.fastify.log.info(`Updating Supplier Attachments`);

    const transaction = await this.db.sequelize.transaction();
    try {
      await this.attachmentBadgeRepository.destroy(
        { attachmentId: id },
        { transaction },
      );

      await this.attachmentRepository.destroy({ id }, { transaction });

      const filePathToDelete = path.resolve('upload', `.${filePath}`);
      const isExistingFile = fs.existsSync(filePathToDelete);
      if (isExistingFile) {
        fs.unlinkSync(filePathToDelete);
      }

      await transaction.commit();
      this.fastify.log.info(`Successfully updated Supplier Attachments`);
      return { success: true };
    } catch (error) {
      await transaction.rollback();
      this.fastify.log.error(`Error updating Supplier Attachments`, error);
      throw error;
    }
  }

  async validateSuppliers(payload = {}) {
    const { supplierIds = [] } = payload;
    const uniqueSupplierIds = [...new Set(supplierIds)];

    const { data: suppliers } =
      await this.supplierRepository.findSuppliersByIds(uniqueSupplierIds, {
        attributes: ['id'],
        paginate: false,
        paranoid: true,
      });

    const missingSupplierIds = uniqueSupplierIds.filter(
      (id) => !suppliers.find((item) => item.id === id),
    );

    if (missingSupplierIds.length > 0) {
      throw this.clientErrors.NOT_FOUND({
        message: `Unable to find Suppliers with ID(s): ${missingSupplierIds}`,
      });
    }

    return suppliers;
  }

  async updateCanvassItemSuppliersSuspended(supplierId) {
    const canvassItems = await this.db.canvassItemModel.findAll({
      where: {
        status: 'for_approval',
      },
      include:[
        {
          association: 'suppliers',
          as: 'suppliers',
          where: {
            supplierId,
          },
        },
      ]
    });

    if (canvassItems.length > 0) {
      const canvassItemSupplierIds = canvassItems
        .flatMap((item) => item.suppliers.map((supplier) => supplier.id));

      await this.canvassItemSupplierRepository.update(
        { id: { [this.db.Sequelize.Op.in]: canvassItemSupplierIds } },
        {
          supplierName: null,
          supplierId: null,
        },
      );
    }
  }
}

module.exports = SupplierService;
