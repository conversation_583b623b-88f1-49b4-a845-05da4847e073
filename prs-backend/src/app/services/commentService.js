class CommentService {
  constructor({ db, fastify, commentRepository }) {
    this.db = db;
    this.fastify = fastify;
    this.commentRepository = commentRepository;
  }

  async createComment(payload) {
    let createdComment;
    const { userId, comment, modelId, model, transaction } = payload;
    const options = transaction ? { transaction } : {};

    try {
      this.fastify.log.info(`Adding comment - ${model}`);
      createdComment = await this.commentRepository.create(
        {
          model,
          modelId,
          comment,
          commentedBy: userId,
        },
        options,
      );
    } catch (error) {
      this.fastify.log.error(`[Error] Create Comment`);
      this.fastify.log.error(error);

      throw error;
    }

    this.fastify.log.info(`Successfully added comment - ${model}`);
    return createdComment;
  }

  async getAllComments(payload) {
    const {
      modelId,
      filters,
      commentedBy,
      commentDateTo,
      commentDateFrom,
      model = [],
    } = payload;

    const whereClause = {
      ...(modelId && { modelId }),
      ...(commentedBy && { commentedBy }),
      ...((commentDateFrom || commentDateTo) && { createdAt: {} }),
    };

    if (Array.isArray(model)) {
      whereClause.model = {
        [this.db.Sequelize.Op.in]: model,
      };
    } else {
      whereClause.model = model;
    }

    if (commentDateFrom) {
      whereClause.createdAt[this.db.Sequelize.Op.gte] = commentDateFrom;
    }

    if (commentDateTo) {
      whereClause.createdAt[this.db.Sequelize.Op.lte] = commentDateTo;
    }

    const comments = await this.commentRepository.getComments({
      filters,
      whereClause,
    });

    return comments;
  }
}

module.exports = CommentService;
