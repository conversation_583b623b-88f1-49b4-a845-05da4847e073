class UserService {
  constructor(container) {
    const {
      db,
      utils,
      bcrypt,
      entities,
      constants,
      userErrors,
      clientErrors,
      userRepository,
      roleRepository,
      departmentRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.bcrypt = bcrypt;
    this.constants = constants;
    this.userErrors = userErrors;
    this.clientErrors = clientErrors;
    this.userEntity = entities.user;
    this.userRepository = userRepository;
    this.roleRepository = roleRepository;
    this.departmentRepository = departmentRepository;
  }

  async getUserList(requesterId, requesterRole, filters = {}) {
    const { USER_TYPES, USER_TYPE_IDS } = this.constants.user;
    const { userSortSchema, userFilterSchema } = this.userEntity;
    const {
      limit,
      page,
      filterBy,
      paginate,
      includeCurrentUser = false,
      sortBy = '{"status": "ASC", "createdAt": "DESC"}',
    } = filters;

    let whereClause = {};

    /* Default: Omit the requester from the list */
    if (!includeCurrentUser) {
      whereClause = {
        id: { [this.db.Sequelize.Op.ne]: requesterId },
      };
    }

    if (requesterRole !== USER_TYPES.ROOT_USER) {
      whereClause[this.db.Sequelize.Op.and] = {
        roleId: { [this.db.Sequelize.Op.ne]: USER_TYPE_IDS.ADMIN },
      };
    }

    const parsedSortBy = userSortSchema.parse(sortBy);
    const orderClauses = parsedSortBy?.map(([field, direction]) => {
      if (field === 'userType') {
        return [this.db.Sequelize.literal('"role"."name"'), direction];
      }

      if (field === 'department') {
        if (direction === 'ASC' || direction === 'asc') {
          return [
            this.db.Sequelize.literal('"department"."name"'),
            `${direction} NULLS FIRST`,
          ];
        } else if (direction === 'DESC' || direction === 'desc') {
          return [
            this.db.Sequelize.literal('"department"."name"'),
            `${direction} NULLS LAST`,
          ];
        }
      }

      return [field, direction];
    });

    const parsedFilterBy = userFilterSchema.parse(filterBy);
    const { username } = this.utils.buildFilterWhereClause(parsedFilterBy);

    /**
     * Role condition
     * - Root User can only see Admin users
     * - Admin/Users can only see Non-Root and Non-Admin users
     */
    const roleCondition = {};

    if (requesterRole === USER_TYPES.ROOT_USER) {
      roleCondition['$role.name$'] = USER_TYPES.ADMIN;
    } else if (includeCurrentUser) {
      roleCondition['$role.name$'] = {
        [this.db.Sequelize.Op.notIn]: [USER_TYPES.ROOT_USER],
      };
    } else {
      roleCondition['$role.name$'] = {
        [this.db.Sequelize.Op.notIn]: [USER_TYPES.ROOT_USER, USER_TYPES.ADMIN],
      };
    }

    if (username) {
      whereClause[this.db.Sequelize.Op.or] = [
        { firstName: { [this.db.Sequelize.Op.iLike]: `%${username}%` } },
        { lastName: { [this.db.Sequelize.Op.iLike]: `%${username}%` } },
        { email: { [this.db.Sequelize.Op.iLike]: `%${username}%` } },
        { username: { [this.db.Sequelize.Op.iLike]: `%${username}%` } },
      ];
    }

    const userList = await this.userRepository.getAllUsers({
      page,
      limit,
      paginate,
      order: orderClauses,
      whereClause: {
        ...roleCondition,
        ...whereClause,
      },
    });

    return userList;
  }

  async createUser(userDetails, transaction) {
    const { parseDomain } = this.utils;

    await this.#checkDuplicateUsernameOrEmail({
      email: userDetails.email,
      username: userDetails.username,
    });

    if (userDetails.departmentId) {
      await this.departmentRepository.getDepartmentDetails(
        userDetails.departmentId,
      );
    }

    /* Check if user type is valid - not a super admin */
    await this.#checkUserType(userDetails.roleId);

    const rawPassword = this.utils
      .generateSecret(userDetails.username)
      .substring(0, 9);
    const hashedPassword = this.bcrypt.hashSync(rawPassword, 8);
    const parsedUserData = parseDomain(this.userEntity.userSchema, {
      ...userDetails,
      password: hashedPassword,
    });

    return await this.userRepository.createUser(
      {
        ...parsedUserData,
        tempPass: rawPassword,
      },
      { transaction },
    );
  }

  async updateUser(userId, userFromToken, payload, transaction) {
    const { parseDomain } = this.utils;
    const { USER_STATUS } = this.constants.user;
    const {
      status,
      firstName,
      lastName,
      roleId,
      email,
      departmentId,
      supervisorId,
    } = payload;

    const userToUpdate = await this.userRepository.getUserById(userId);

    if (!userToUpdate) {
      throw this.userErrors.USER_NOT_FOUND();
    }

    // Check permissions and user type
    await this.#checkUserPermissions({
      userUpdater: userFromToken,
      userToUpdate,
      roleId,
    });

    if (departmentId) {
      await this.departmentRepository.getDepartmentDetails(departmentId);
    }

    if (roleId) {
      await this.#checkUserType(roleId, userId);
    }

    if (email) {
      await this.#checkDuplicateUsernameOrEmail({
        email,
        userId: userToUpdate.id,
      });
    }

    const updateData = {
      email: email ?? null,
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(roleId && { roleId }),
      ...(departmentId && { departmentId }),
      ...(supervisorId && { supervisorId }),
    };

    if (payload.username) {
      const adminId = await this.roleRepository.getAdminId();
      const rootId = await this.roleRepository.getRootId();

      if (roleId === adminId) {
        if (userFromToken.role.id !== rootId) {
          throw this.clientErrors.BAD_REQUEST({
            message: 'Only root user can change admin username',
          });
        }
        updateData.username = payload.username;
      } else if (roleId !== rootId) {
        if (userFromToken.role.id !== adminId) {
          throw this.clientErrors.BAD_REQUEST({
            message: 'Only admin role can change others username',
          });
        }
        updateData.username = payload.username;
      } else {
        throw this.clientErrors.BAD_REQUEST({
          message: 'Only admin/root user can change others username',
        });
      }
    }

    if (status) {
      updateData.status = status;
      updateData.deletedAt =
        status === USER_STATUS.INACTIVE ? new Date() : null;
    }

    if (Object.keys(updateData).length === 0) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'No user fields provided for update',
      });
    }

    const { deletedAt, ...rawUpdateData } = updateData;
    const parsedUserData = parseDomain(
      this.userEntity.updateUserRequest,
      rawUpdateData,
    );

    await this.userRepository.updateUserById(userToUpdate.id, {
      ...parsedUserData,
      ...(updateData.deletedAt !== undefined && {
        deletedAt: updateData.deletedAt,
      },
      { transaction }),
    });

    return parsedUserData;
  }

  async updatePassword(userId, oldPassword, newPassword) {
    const isMatch = this.bcrypt.compareSync(newPassword, oldPassword);

    if (isMatch) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'New password cannot be the same as the old password',
      });
    }

    const hashedPassword = this.bcrypt.hashSync(newPassword, 8);

    await this.userRepository.updateUserById(userId, {
      password: hashedPassword,
      isPasswordTemporary: false,
      tempPass: null,
    });
  }

  async resetPassword(userId, userFromToken) {
    const existingUser = await this.userRepository.getUserById(userId);

    if (!existingUser) {
      throw this.userErrors.USER_NOT_FOUND();
    }

    /**
     * Check permissions and user type
     * - Root User can reset password for any user
     * - Admin can reset password except for Root User/Admin
     */
    await this.#checkUserPermissions({
      userUpdater: userFromToken,
      userToUpdate: existingUser,
    });

    const defaultPassword = this.utils
      .generateSecret(existingUser.username)
      .substring(0, 9);
    const hashedPassword = this.bcrypt.hashSync(defaultPassword, 8);
    await this.userRepository.updateUserById(existingUser.id, {
      password: hashedPassword,
      isPasswordTemporary: true,
      tempPass: defaultPassword,
      otpSecret: null,
    });

    return defaultPassword;
  }

  async getUserByUsername(username) {
    const { USER_STATUS } = this.constants.user;
    const existingUser = await this.userRepository.getUser({
      username,
    });

    if (!existingUser || existingUser?.status === USER_STATUS.INACTIVE) {
      throw this.clientErrors.NOT_FOUND({
        message: 'User not found',
      });
    }

    return existingUser;
  }

  async #checkUserType(roleId, userId = null) {
    const { USER_TYPES } = this.constants.user;
    const existingRole = await this.roleRepository.getById(roleId);

    if (!existingRole) {
      throw this.clientErrors.NOT_FOUND({
        message: `User type does not exist`,
      });
    }

    if (existingRole.name === USER_TYPES.ROOT_USER) {
      throw this.clientErrors.FORBIDDEN({
        message: `You're not allowed to create/update user with this role`,
      });
    }

    /* Check if purchasing head is already existing */
    if (existingRole.name === USER_TYPES.PURCHASING_HEAD) {
      const existingPurchasingHead = await this.userRepository.getUser({
        roleId,
      });

      const isSameUser = existingPurchasingHead?.id === userId;

      if (existingPurchasingHead && !isSameUser) {
        throw this.clientErrors.UNPROCESSABLE_ENTITY({
          message: 'Purchasing head already exists',
        });
      }
    }

    /* Check if management role exceeds maximum of 2 users */
    if (existingRole.name === USER_TYPES.MANAGEMENT) {
      const existingManagementUsers = await this.userRepository.findAll({
        where: { roleId },
        attributes: ['id'],
      });

      const isSameUser = existingManagementUsers.data.some(
        (user) => user.id === userId,
      );

      if (existingManagementUsers.data.length >= 2 && !isSameUser) {
        throw this.clientErrors.UNPROCESSABLE_ENTITY({
          message: 'Maximum of 2 management users already exists',
        });
      }
    }
  }

  async #checkDuplicateUsernameOrEmail(payload) {
    let whereClause;
    const { userId, email = '', username = '' } = payload;

    if (email && username) {
      whereClause = {
        [this.db.Sequelize.Op.or]: [{ email }, { username }],
      };
    } else if (email) {
      whereClause = { email };
    } else if (username) {
      whereClause = { username };
    } else {
      return;
    }

    const existingUser = await this.userRepository.getUser(whereClause);

    if (!existingUser) {
      return;
    }

    const isSameUser = existingUser.id === userId;

    if (existingUser.username === username && !isSameUser) {
      throw this.userErrors.USERNAME_ALREADY_USED();
    }

    if (existingUser.email === email && !isSameUser) {
      throw this.userErrors.EMAIL_ALREADY_USED();
    }
  }

  async #checkUserPermissions(payload) {
    const { USER_TYPES } = this.constants.user;
    const { userUpdater, userToUpdate, roleId, departmentId } = payload;

    /* Group role checks */
    const roles = {
      updater: {
        isRoot: userUpdater.role.name === USER_TYPES.ROOT_USER,
        isAdmin: userUpdater.role.name === USER_TYPES.ADMIN,
      },
      target: {
        isRoot: userToUpdate.role.name === USER_TYPES.ROOT_USER,
        isAdmin: userToUpdate.role.name === USER_TYPES.ADMIN,
      },
    };

    /* Group update type checks */
    const updates = {
      isSelf: userUpdater.id === userToUpdate.id,
      isProtected: roleId || departmentId,
    };

    /* Don't allow to update their own role/department */
    if (updates.isSelf && updates.isProtected) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You cannot perform this action',
      });
    }

    /* Root user can update anyone (except self role/department) */
    if (roles.updater.isRoot) {
      return;
    }

    /* Block Admin from updating Root User or other Admins */
    const isAdminUpdatingProtected =
      roles.updater.isAdmin &&
      (roles.target.isRoot || (roles.target.isAdmin && !updates.isSelf));

    if (isAdminUpdatingProtected) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You cannot perform this action',
      });
    }

    /* Allow Admin to update other roles */
    if (roles.updater.isAdmin) {
      return;
    }

    /* Other roles can only update themselves */
    if (!updates.isSelf) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You cannot perform this action',
      });
    }
  }

  async getExistingUser(userId, filters = {}) {
    const { role, paranoid = false } = filters;
    const existingUser = await this.userRepository.getUserById(userId, {
      paranoid,
    });

    if (!existingUser) {
      throw this.clientErrors.NOT_FOUND({
        message: `User not found with ID: ${userId}`,
      });
    }

    const isRoleInValid = role && role !== existingUser.role?.name;

    if (isRoleInValid) {
      throw this.clientErrors.NOT_FOUND({
        message: `${role} not found with ID: ${userId}`,
      });
    }

    return existingUser;
  }

  async validateMultipleUsers(userIds, filters = {}) {
    const { roleNames, ...otherFilters } = filters;
    const uniqueUserIds = [...new Set(userIds)];

    const includeClause = [
      {
        association: 'role',
        attributes: ['name', 'id'],
        ...(roleNames && {
          where: {
            name: {
              [this.db.Sequelize.Op.in]: roleNames,
            },
          },
        }),
      },
    ];

    const { data: users } = await this.userRepository.findUsersByIds(
      uniqueUserIds,
      {
        ...otherFilters,
        attributes: ['id'],
        include: includeClause,
        paginate: false,
        paranoid: true,
      },
    );

    const missingUserIds = uniqueUserIds.filter(
      (id) => !users.find((user) => user.id === id),
    );

    if (missingUserIds.length > 0) {
      const roleMessage = roleNames
        ? ` with role${roleNames.length > 1 ? 's' : ''} (${roleNames.join(', ')})`
        : '';

      throw this.clientErrors.NOT_FOUND({
        message: `Unable to find users${roleMessage}. Missing IDs: ${missingUserIds}`,
      });
    }

    return users;
  }

  async getUsersByRoleName(payload) {
    const { roleNames, paranoid, attributes, where } = payload;

    return await this.userRepository.getUsersByRoleName({
      where,
      roleNames,
      paranoid,
      attributes,
    });
  }

  async getUserDetails(userId, userFromToken) {
    const { USER_TYPES } = this.constants.user;
    const existingUser = await this.getExistingUser(userId);

    /* Remove (permissions, otpSecret, password) from user details */
    const {
      otpSecret,
      password,
      role: { permissions, ...roleDetails },
      ...userDetails
    } = existingUser;

    const isRequesterRootUser =
      userFromToken?.role?.name === USER_TYPES.ROOT_USER;
    const isSameUser = userFromToken?.id === existingUser.id;
    const isProtectedRole = [USER_TYPES.ROOT_USER, USER_TYPES.ADMIN].includes(
      roleDetails.name,
    );

    if (!isRequesterRootUser && isProtectedRole && !isSameUser) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not allowed to access this resource',
      });
    }

    return { ...userDetails, role: roleDetails };
  }
}

module.exports = UserService;
