class ClientService {
  constructor({ apiToken, endpoint }) {
    this.apiToken = apiToken;
    this.endpoint = endpoint;
    this.HTTP_METHODS = {
      GET: 'GET',
      POST: 'POST',
      PUT: 'PUT',
      DELETE: 'DELETE',
    };
  }

  async request(method, body = null) {
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      Accept: 'application/json',
    };

    // TODO: Update API Auth - once api is ready
    if (this.apiToken) {
      headers.Authorization = `Bearer ${this.apiToken}`;
    }

    const options = {
      method,
      headers,
      body: body ? JSON.stringify(body) : null,
    };

    const response = await fetch(this.endpoint, options);
    const result = await response.json();

    // TODO: Update mapping to match the api response
    if (!response.ok) {
      throw new Error(result.message || 'An error occurred');
    }

    return result;
  }

  async get() {
    return await this.request(this.HTTP_METHODS.GET);
  }

  async post(body) {
    return await this.request(this.HTTP_METHODS.POST, body);
  }

  async put(body) {
    return await this.request(this.HTTP_METHODS.PUT, body);
  }

  async delete() {
    return await this.request(this.HTTP_METHODS.DELETE);
  }
}

module.exports = ClientService;
