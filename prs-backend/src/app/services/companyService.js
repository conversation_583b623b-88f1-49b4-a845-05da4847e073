class CompanyService {
  constructor(container) {
    const {
      db,
      utils,
      fastify,
      entities,
      constants,
      clientErrors,
      syncRepository,
      companyRepository,
      projectRepository,
      projectCompanyRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.fastify = fastify;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.companyEntity = entities.company;
    this.syncRepository = syncRepository;
    this.companyRepository = companyRepository;
    this.projectRepository = projectRepository;
    this.projectCompanyRepository = projectCompanyRepository;
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}/v2-companies`,
    });
  }

  // TODO: Update mapping to match the api response
  async syncCompanies() {
    const result = await this.httpClient.get();
    const companies = result.data ?? [];
    const mappedCompanies = companies.map((company) => ({
      code: company.COMPCD,
      name: company.CONAME ?? '',
      initial: company.COINIT ?? '',
      tin: company.TIN ?? '',
      address: company.COMADD,
      contactNumber: company.COMNUM,
    }));

    await this.companyRepository.syncCompanies(mappedCompanies);

    return await this.syncRepository.updateLastSynced('company');
  }

  async createCompany(payload) {
    const {
      code,
      name,
      initial,
      tin,
      address,
      contactNumber,
      areaCode,
      selectedProjectIdTags,
      category = 'association',
    } = this.companyEntity.createCompanySchema.parse(payload);

    const existingCompany = await this.companyRepository.findOne({
      where: { code },
    });

    if (existingCompany) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Company code already exists',
      });
    }

    // Validate project tagging based on company category
    if (category !== 'association' && selectedProjectIdTags.length > 1) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Non-association companies can only be tagged to one project',
      });
    }

    const createdCompany = await this.companyRepository.create({
      code,
      name,
      initial,
      tin,
      address,
      contactNumber,
      areaCode,
      category,
    });

    // Add company project tagging using the repository
    if (selectedProjectIdTags.length > 0) {
      const projectCompanyEntries = selectedProjectIdTags.map((projectId) => ({
        projectId,
        companyId: createdCompany.id,
      }));

      await this.projectCompanyRepository.bulkCreateTags(projectCompanyEntries);
    }

    return createdCompany;
  }

  async updateCompany(companyId, payload) {
    const parsedPayload = this.companyEntity.updateCompanySchema.parse(payload);
    const existingCompany = await this.getExistingCompany(parseInt(companyId));

    if (existingCompany.category !== 'association') {
      // For non-association companies, ensure only one project is selected
      if (
        parsedPayload.selectedProjectIdTags &&
        parsedPayload.selectedProjectIdTags.length > 1
      ) {
        throw this.clientErrors.BAD_REQUEST({
          message:
            'Non-association companies can only be tagged to one project',
        });
      }
    }

    if (Object.keys(parsedPayload).length === 0) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'No company fields provided for update',
      });
    }

    // Remove selectedProjectIdTags from the payload as it's not a column in companies table
    const { selectedProjectIdTags, ...companyData } = parsedPayload;

    const updatedCompany = await this.companyRepository.update(
      { id: existingCompany.id },
      companyData,
    );

    // Update company project tagging using the repository
    if (selectedProjectIdTags) {
      // First, remove all existing associations
      await this.projectCompanyRepository.removeAllCompanyTags(
        existingCompany.id,
      );

      // Then create new associations
      if (selectedProjectIdTags.length > 0) {
        const projectCompanyEntries = selectedProjectIdTags.map(
          (projectId) => ({
            projectId,
            companyId: existingCompany.id,
          }),
        );

        await this.projectCompanyRepository.bulkCreateTags(
          projectCompanyEntries,
        );
      }
    }

    return updatedCompany;
  }

  async deleteCompany(companyId) {
    return await this.companyRepository.destroy({
      id: companyId,
    });
  }

  async getExistingCompany(companyId) {
    const { ASSOCIATION_AREAS } = this.constants.area;
    const existingCompany = await this.companyRepository.getByCompanyId(
      parseInt(companyId),
    );

    if (!existingCompany) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Company not found',
      });
    }

    const { areaCode, ...companyDetails } = existingCompany;

    return {
      ...companyDetails,
      area: ASSOCIATION_AREAS[areaCode] ?? null,
    };
  }

  async validateCompany(payload = {}) {
    const { companyIds = [] } = payload;
    const uniqueCompanyIds = [...new Set(companyIds)];

    const { data: companies } = await this.companyRepository.findCompaniesByIds(
      uniqueCompanyIds,
      {
        attributes: ['id'],
        paginate: false,
        paranoid: true,
      },
    );

    const missingCompanyIds = uniqueCompanyIds.filter(
      (id) => !companies.find((item) => item.id === id),
    );

    if (missingCompanyIds.length > 0) {
      throw this.clientErrors.NOT_FOUND({
        message: `Unable to find Company with ID(s): ${missingCompanyIds}`,
      });
    }

    console.log({
      companies,
    });

    return companies;
  }

  async validateSelectedProjectTagging(
    selectedProjectIdTags = [],
    companyId,
    isUpdate = false,
  ) {
    if (selectedProjectIdTags.length > 0) {
      // Get all projects that are already tagged to companies
      const taggedProjectIds =
        await this.projectCompanyRepository.getTaggedProjectIds(
          isUpdate ? companyId : null,
        );

      // Find intersecting IDs
      const alreadyTaggedProjects = selectedProjectIdTags.filter((id) =>
        taggedProjectIds.includes(id),
      );

      if (alreadyTaggedProjects.length > 0) {
        throw this.clientErrors.BAD_REQUEST({
          message: `The following project ids are already tagged and cannot be reused: ${alreadyTaggedProjects.join(', ')}`,
        });
      }

      return true;
    }

    return false;
  }
}

module.exports = CompanyService;
