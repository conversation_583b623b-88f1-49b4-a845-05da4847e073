class ProjectApprovalService {
  constructor ({
    db,
    clientErrors,
    approvalTypeRepository,
    projectApprovalRepository,
    requisitionRepository,
    requisitionApproverRepository,
    fastify,
  }) {
    this.db = db;
    this.clientErrors = clientErrors;
    this.approvalTypeRepository = approvalTypeRepository;
    this.projectApprovalRepository = projectApprovalRepository;
    this.requisitionRepository = requisitionRepository;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.fastify = fastify;
  }

  async setupProjectApprovals(data) {
    const {
      approvers,
      projectId,
      approvalTypeCode,
      optionalApprovers = [],
    } = data;

    const projectTransaction = await this.db.sequelize.transaction();

    try {
      await this.projectApprovalRepository.destroy(
        {
          projectId,
          approvalTypeCode,
        },
        {
          transaction: projectTransaction,
        },
      );

      const mainApprovalsToCreate = approvers.map((approver) => ({
        projectId,
        approvalTypeCode,
        level: approver.level,
        approverId: approver.approverId,
        isOptional: false,
      }));

      let optionalApprovalsToCreate = [];
      if (optionalApprovers.length === 1) {
        optionalApprovalsToCreate.push({
          level: 1,
          projectId,
          isOptional: true,
          approvalTypeCode,
          approverId: optionalApprovers[0].approverId,
        });
      }

      const allApprovalsToCreate = [
        ...mainApprovalsToCreate,
        ...optionalApprovalsToCreate,
      ];

      const createdApprovals = await this.projectApprovalRepository.bulkCreate(
        allApprovalsToCreate,
        {
          transaction: projectTransaction,
        },
      );

      await this.#cascadeOptionalApprover({
        optionalApprovalsToCreate,
        projectTransaction,
      });

      await projectTransaction.commit();
      return createdApprovals;
    } catch (error) {
      await projectTransaction.rollback();
      throw error;
    }
  }

  async getProjectApprovals(projectId, approvalTypeCode) {
    const whereClause = {
      projectId,
    };

    if (approvalTypeCode) {
      whereClause.approvalTypeCode = approvalTypeCode;
    }

    const allApprovals = await this.projectApprovalRepository.getAllApprovals({
      where: whereClause,
    });

    const groupedApprovals = this.transformApprovals(allApprovals.data);

    return {
      data: Object.values(groupedApprovals),
      total: allApprovals.total,
    };
  }

  async #cascadeOptionalApprover({ optionalApprovalsToCreate, projectTransaction }) {
    if (optionalApprovalsToCreate.length === 0) {
      return;
    }

    const { projectId, approverId } = optionalApprovalsToCreate[0];

    const {
      data: requisitionData,
      total: requisitionTotal,
    } = await this.requisitionRepository.findAll(
      { where: { projectId } },
      { transaction: projectTransaction },
    );

    if (requisitionTotal === 0) {
      this.fastify.log.info(`NOTHING_TO_CASCADE_FOR_PROJECT_ID: ${projectId}`);
      return;
    }

    await Promise.all(requisitionData.map(async (requisition) => {
      const requisitionApproverData = await this.requisitionApproverRepository.findOne({
        where: {
          requisitionId: requisition.id,
          modelType: 'project',
          modelId: projectId,
          approverId: null,
        },
        transaction: projectTransaction,
      });

      if (!requisitionApproverData) {
        this.fastify.log.info(`NOTHING_TO_CASCADE: project_id: ${projectId}, requisition_id: ${requisition.id}`);
        return true;
      }

      await this.requisitionApproverRepository.update(
        { id: requisitionApproverData.id },
        { approverId },
        { transaction: projectTransaction },
      );

      this.fastify.log.info(`CASCADED_OPTIONAL_APPROCER: project_id: ${projectId}, requisition_id: ${requisition.id}`);
    }));

    return true;
  }

  // Private methods
  transformApprovals(allApprovals = []) {
    const groupedApprovals = allApprovals.reduce((groups, approval) => {
      const type = approval.approvalTypeCode;
      if (!groups[type]) {
        groups[type] = {
          type,
          approvers: [],
          optionalApprovers: [],
        };
      }

      const approverData = {
        id: approval.id,
        projectId: approval.projectId,
        level: approval.level,
        createdAt: approval.createdAt,
        updatedAt: approval.updatedAt,
        approver: approval.approver,
      };

      if (approval.isOptional) {
        groups[type].optionalApprovers.push(approverData);
      } else {
        groups[type].approvers.push(approverData);
      }

      return groups;
    }, {});

    return groupedApprovals;
  }
}

module.exports = ProjectApprovalService;
