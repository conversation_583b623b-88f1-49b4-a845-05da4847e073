class ChargeToService {
  constructor(container) {
    const {
      constants,
      projectRepository,
      companyRepository,
      supplierRepository,
    } = container;

    this.constants = constants;
    this.projectRepository = projectRepository;
    this.companyRepository = companyRepository;
    this.supplierRepository = supplierRepository;
  }

  /* TODO: This function can be used in RS - get charge to */
  async isValidChargeTo({ chargeTo, chargeToId }) {
    const isValidCharge =
      this.constants.nonRS.NON_RS_CHARGE_TO_LIST.includes(chargeTo);

    if (!isValidCharge || !chargeToId) {
      return false;
    }

    const repositoryMap = {
      project: this.projectRepository,
      company: this.companyRepository,
      supplier: this.supplierRepository,
      association: this.companyRepository,
    };

    const repository = repositoryMap[chargeTo];

    if (!repository) {
      return false;
    }

    const options =
      chargeTo === 'association' ? { category: 'association' } : {};

    return !!(await repository.getById(chargeToId, options));
  }
}

module.exports = ChargeToService;
