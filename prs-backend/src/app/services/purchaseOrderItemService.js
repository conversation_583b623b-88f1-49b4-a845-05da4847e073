class PurchaseOrderItemService {
  constructor(container) {
    const {
      db,
      entities,
      constants,
      clientErrors,
      fastify,
      purchaseOrderItemRepository,
    } = container;

    this.db = db;
    this.Sequelize = db.Sequelize;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
  }

  async getPOItemsById(payload) {
    let poItems;
    
    if (payload.type === 'receiving') {
      poItems = await this.purchaseOrderItemRepository.getPOItemsForReceiving(payload);
    } else {
      poItems = await this.purchaseOrderItemRepository.getPOItemsById(payload);
      
      poItems['summary'] = 
        await this.purchaseOrderItemRepository.getPOItemsSummary(payload);
    }
    
    return poItems;
  }
}

module.exports = PurchaseOrderItemService;
