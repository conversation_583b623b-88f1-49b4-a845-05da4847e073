class TradeService {
  constructor (container) {
    const { db, projectTradeRepository, clientErrors } = container;

    this.db = db;
    this.clientErrors = clientErrors;
    this.projectTradeRepository = projectTradeRepository;
  }

  async getUserTrade(userId) {
    const projectTrade = await this.projectTradeRepository.findOne({
      attributes: ['tradeId'],
      where: { engineerId: userId },
    });

    if (!projectTrade) {
      return null;
    }

    return projectTrade.tradeId;
  }
}

module.exports = TradeService;
