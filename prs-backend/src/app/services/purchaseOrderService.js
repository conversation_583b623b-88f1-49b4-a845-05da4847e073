class PurchaseOrderService {
  constructor({
    db,
    utils,
    fastify,
    constants,
    clientErrors,
    roleRepository,
    userRepository,
    requisitionRepository,
    canvassItemRepository,
    purchaseOrderRepository,
    purchaseOrderItemRepository,
    canvassRequisitionRepository,
    canvassItemSupplierRepository,
    purchaseOrderApproverRepository,
    purchaseOrderCancelledItemsRepository,
    supplierRepository,
    companyRepository,
    deliveryReceiptService,
    deliveryReceiptItemService,
    notificationService,
  }) {
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.utils = utils;
    this.fastify = fastify;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.roleRepository = roleRepository;
    this.userRepository = userRepository;
    this.requisitionRepository = requisitionRepository;
    this.canvassItemRepository = canvassItemRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.canvassItemSupplierRepository = canvassItemSupplierRepository;
    this.purchaseOrderApproverRepository = purchaseOrderApproverRepository;
    this.purchaseOrderCancelledItemsRepository =
      purchaseOrderCancelledItemsRepository;
    this.supplierRepository = supplierRepository;
    this.companyRepository = companyRepository;
    this.deliveryReceiptService = deliveryReceiptService;
    this.deliveryReceiptItemService = deliveryReceiptItemService;
    this.notificationService = notificationService;
  }

  async #mapPurchaseOrderItems(purchaseOrderId) {
    const [purchaseOrderItems, deliveryItems] = await Promise.all([
      this.purchaseOrderItemRepository.findAll({
        where: { purchaseOrderId },
      }),
      this.deliveryReceiptItemService.getAllDeliveryReceiptItemsByPOId(
        purchaseOrderId,
      ),
    ]);

    const mappedQuantityDelivered = deliveryItems.data.reduce((acc, item) => {
      const { poItemId, qtyDelivered } = item;
      const delivered = parseFloat(qtyDelivered) || 0;
      acc[poItemId] = (acc[poItemId] || 0) + delivered;
      return acc;
    }, {});

    const purchaseOrderMappedItems = purchaseOrderItems.data.map((poItem) => {
      const {
        id: poItemId,
        purchaseOrderId,
        quantityPurchased,
        canvassItemId,
        canvassItemSupplierId,
      } = poItem;

      const purchased = parseFloat(quantityPurchased) || 0;
      const delivered = parseFloat(mappedQuantityDelivered[poItemId]) || 0;
      const quantityToCancel = +(purchased - delivered).toFixed(3);
      const quantityFulfilled = +purchased.toFixed(3) === +delivered.toFixed(3);

      return {
        poItemId,
        purchaseOrderId,
        quantityPurchased: +purchased.toFixed(3),
        quantityDelivered: +delivered.toFixed(3),
        quantityToCancel,
        quantityFulfilled,
        canvassItemId,
        canvassItemSupplierId,
      };
    });

    return purchaseOrderMappedItems;
  }

  #shouldAllowCancellationByPOStatus(purchaseOrderStatus) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    return (
      purchaseOrderStatus === PO_STATUS.FOR_PO_APPROVAL ||
      purchaseOrderStatus === PO_STATUS.FOR_PO_REVIEW
    );
  }

  async #shouldAllowCancellationByDeliveryStatus(purchaseOrderId) {
    const { DELIVERY_ITEM_STATUSES } = this.constants.deliveryReceiptItem;

    const deliveryReceipts =
      await this.deliveryReceiptService.getDeliveryReceiptsFromPurchaseOrderId(
        purchaseOrderId,
        {
          attributes: ['latest_delivery_status'],
        },
      );

    // IF PO HAS NO DELIVERY RECEIPT YET - ALLOW CANCELLATION
    if (!deliveryReceipts.total) return true;

    // IF PO IS FULLY DELIVERED - PREVENT CANCELLATION
    const isFullyDelivered = deliveryReceipts.data.some(
      (dr) =>
        dr.latest_delivery_status === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED,
    );

    if (isFullyDelivered) return false;

    // IF ALL PO ITEMS ARE HAVE QTY FULLFILLED - PREVENT CANCELLATION
    const purchaseOrderMappedItems =
      await this.#mapPurchaseOrderItems(purchaseOrderId);

    const canCancelOnFullfilledQty = !purchaseOrderMappedItems.every(
      (item) => item.quantityFulfilled,
    );

    return canCancelOnFullfilledQty;
  }

  async getAllPurchaseOrders(payload = {}) {
    const {
      requisitionId,
      limit,
      page,
      sortBy = '{"createdAt": "desc"}',
      selection,
    } = payload;

    if (selection === 'invoice') {
      const purchaseOrderList = await this.purchaseOrderRepository.findAll({
        attributes: [
          'id',
          [
            this.Sequelize.fn(
              'CONCAT',
              'PO-',
              this.Sequelize.col('requisition.company_code'),
              this.Sequelize.col('po_letter'),
              this.Sequelize.col('po_number'),
            ),
            'poNumber',
          ],
        ],
        include: [
          {
            association: 'requisition',
            attributes: [],
          },
          {
            association: 'deliveryReceipts',
            attributes: [],
            required: true,
            where: {
              invoiceId: null,
            },
          },
        ],
        where: { requisitionId, status: 'for_delivery' },
        order: [['poNumber', 'ASC']],
        subQuery: false,
      });
      return purchaseOrderList;
    } else {
      // Convert the parsedSortBy object into an array format for Sequelize ordering
      const order = Object.entries(JSON.parse(sortBy)).map(([key, value]) => {
        if (key === 'supplier') {
          // Use the literal directly in the ORDER BY clause
          return [
            this.db.Sequelize.literal(
              `(CASE
                WHEN purchase_orders.supplier_name IS NOT NULL 
                  THEN COALESCE(purchase_orders.supplier_name, 'N/A')
                ELSE (CASE purchase_orders.supplier_type 
                  WHEN 'company' THEN company.name
                  WHEN 'project' THEN project.name
                  WHEN 'supplier' THEN supplier.name
                END)
              END)`,
            ),
            value.toUpperCase(),
          ];
        }

        return [key, value.toUpperCase()];
      });

      const purchaseOrderList = await this.purchaseOrderRepository.findAll({
        limit,
        page,
        order,
        where: { requisitionId },
        include: [
          {
            association: 'requisition',
            attributes: ['company_code'],
          },
          {
            association: 'supplier',
            attributes: ['id', 'name'],
          },
          {
            association: 'company',
            attributes: ['id', 'name'],
          },
          {
            association: 'project',
            attributes: ['id', 'name'],
          },
          {
            association: 'warranty',
            attributes: ['id', 'name'],
          },
          {
            required: false,
            association: 'purchaseOrderApprovers',
            attributes: ['id', 'userId', 'level', 'status', 'isAdhoc'],
            where: { status: 'approved' },
            order: [
              ['level', 'DESC'],
              ['isAdhoc', 'DESC'],
            ],
            limit: 1,
            include: [
              {
                association: 'approver',
                as: 'approver',
                attributes: ['id', 'firstName', 'lastName'],
              },
            ],
          },
        ],
        attributes: {
          exclude: ['supplierId', 'warrantyId', 'poNumber'],
          include: [
            [
              this.db.Sequelize.literal(
                `(CASE
                    WHEN purchase_orders.supplier_name IS NOT NULL 
                      THEN COALESCE(purchase_orders.supplier_name, 'N/A')
                    ELSE (CASE purchase_orders.supplier_type 
                      WHEN 'company' THEN company.name
                      WHEN 'project' THEN project.name
                      WHEN 'supplier' THEN supplier.name
                    END)
                  END)`,
              ),
              'supplierName',
            ],
            [
              this.db.Sequelize.literal(
                `(SELECT COALESCE(CONCAT(u.first_name, ' ', u.last_name), 'N/A') 
                  FROM purchase_order_approvers poa
                  LEFT JOIN users u ON poa.user_id = u.id
                  WHERE poa.purchase_order_id = purchase_orders.id
                  AND poa.status = 'approved'
                  ORDER BY poa.level DESC, poa.is_adhoc DESC
                  LIMIT 1)`,
              ),
              'lastApproverName',
            ],
            [
              this.Sequelize.fn(
                'CONCAT',
                'PO-',
                this.Sequelize.col('requisition.company_code'),
                this.Sequelize.col('po_letter'),
                this.Sequelize.col('po_number'),
              ),
              'poNumber',
            ],
          ],
        },
      });

      return purchaseOrderList;
    }
  }

  async getPODetails(purchaseOrderId) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'requisition',
          attributes: ['id', 'dateRequired', 'deliveryAddress'],
        },
        {
          association: 'warranty',
          attributes: ['id', 'name'],
        },
        {
          association: 'requisition',
          attributes: ['company_code'],
        },
      ],
      attributes: {
        exclude: ['warrantyId', 'deliveryAddress', 'poNumber'],
        include: [
          [
            this.Sequelize.fn(
              'CONCAT',
              'PO-',
              this.Sequelize.col('requisition.company_code'),
              this.Sequelize.col('po_letter'),
              this.Sequelize.col('po_number'),
            ),
            'poNumber',
          ],
          'isNewDeliveryAddress',
          'newDeliveryAddress',
        ],
      },
    });

    if (!purchaseOrder) return null;

    let supplier;
    if (purchaseOrder.supplierType === 'supplier') {
      const result = await this.supplierRepository.findOne({
        attributes: ['id', 'name'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });

      supplier = result;
    } else if (purchaseOrder.supplierType === 'company') {
      const result = await this.companyRepository.findOne({
        attributes: ['id', 'name'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });

      supplier = result;
    }

    // identify deposit percentage using term type
    const termsResult = this.utils.Terms(purchaseOrder);

    const getDepositPercent = (terms) => {
      const TERMS = this.constants.purchaseOrder.TERMS;
      switch (terms) {
        case TERMS.DP_10:
        case TERMS.DP_10_RETENTION_10:
          return 10;
        case TERMS.DP_20:
        case TERMS.DP_20_RETENTION_10:
          return 20;
        case TERMS.DP_30:
        case TERMS.DP_30_RETENTION_10:
          return 30;
        case TERMS.DP_50:
          return 50;
        case TERMS.DP_80:
          return 80;
        default:
          return '0';
      }
    };
    const depositPercent = getDepositPercent(purchaseOrder.terms);
    const depositAmount = termsResult?.remainingBalance
      ? parseFloat(purchaseOrder.totalAmount) -
        parseFloat(termsResult.remainingBalance)
      : 0;
    const remainingBalance = termsResult?.remainingBalance
      ? termsResult.remainingBalance
      : purchaseOrder.totalAmount;

    const canCancelPOByDeliveryStatus =
      await this.#shouldAllowCancellationByDeliveryStatus(purchaseOrderId);

    const canCancelPOByStatus = this.#shouldAllowCancellationByPOStatus(
      purchaseOrder.status,
    );

    const canCancelPO =
      (canCancelPOByDeliveryStatus || canCancelPOByStatus) &&
      !purchaseOrder.wasCancelled;

    return {
      ...purchaseOrder,
      depositPercent,
      depositAmount,
      remainingBalance,
      supplier,
      canCancelPO,
    };
  }

  async getPurchaseOrderApprovers(purchaseOrderId) {
    const today = new Date(new Date().setHours(0, 0, 0, 0));

    const local = new Date(
      today.getTime() - today.getTimezoneOffset() * 60 * 1000,
    );

    const purchaseOrderApprovers =
      await this.purchaseOrderApproverRepository.findAll({
        where: {
          purchaseOrderId,
        },
        include: [
          {
            association: 'approver',
            attributes: ['id', 'firstName', 'lastName'],
            include: [
              { association: 'role', as: 'role', attributes: ['id', 'name'] },
              {
                model: this.db.leaveModel,
                attributes: ['id', 'startDate', 'endDate', 'totalDays'],
                as: 'userLeaves',
                required: false,
                where: {
                  [this.Sequelize.Op.and]: [
                    {
                      startDate: {
                        [this.Sequelize.Op.lte]: local,
                      },
                    },
                    {
                      endDate: {
                        [this.Sequelize.Op.gte]: local,
                      },
                    },
                  ],
                },
              },
            ],
          },
          {
            model: this.db.userModel,
            as: 'altApprover',
            attributes: [
              'id',
              [
                this.Sequelize.fn(
                  'CONCAT',
                  this.Sequelize.col('altApprover.first_name'),
                  ' ',
                  this.Sequelize.col('altApprover.last_name'),
                ),
                'fullName',
              ],
            ],
            include: [
              { association: 'role', as: 'role', attributes: ['id', 'name'] },
            ],
          },
          {
            association: 'role',
            attributes: ['name'],
          },
        ],
        attributes: {
          exclude: ['userId'],
        },
      });

    return purchaseOrderApprovers;
  }

  async getPurchaseOrderAssignee(purchaseOrderId) {
    const existingPurchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      attributes: ['assignedTo'],
    });

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    // if no assignee then return temporary placeholders
    if (!existingPurchaseOrder.assignedTo) {
      return {
        data: {},
      };
    }

    const assignedToDetails = await this.userRepository.findOne({
      where: {
        id: existingPurchaseOrder.assignedTo,
      },
      attributes: ['id', 'firstName', 'lastName'],
      include: [
        {
          association: 'role',
          attributes: ['name'],
        },
      ],
    });

    return {
      data: {
        approver: {
          id: assignedToDetails.id,
          firstName: assignedToDetails.firstName,
          lastName: assignedToDetails.lastName,
        },
        ...assignedToDetails.role,
      },
    };
  }

  async getExistingPurchaseOrder(purchaseOrderId) {
    const existingPurchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'canvassRequisition',
          attributes: ['id'],
          include: [
            {
              association: 'requisition',
              attributes: ['id', 'companyCode', 'assignedTo', 'createdBy'],
            },
          ],
        },
      ],
    });

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    return existingPurchaseOrder;
  }

  async createPurchaseOrder(payload = {}) {
    const { existingCanvass, transaction } = payload;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const canvassItems = await this.canvassItemRepository.findAll({
      where: { canvassRequisitionId: existingCanvass.id },
      attributes: ['id'],
      paginate: false,
    });

    const selectedSuppliers =
      await this.canvassItemSupplierRepository.getSelectedSupplierByCanvassId(
        canvassItems?.data?.map((item) => item.id) || [],
      );

    if (!selectedSuppliers.total) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No selected suppliers found for this canvass requisition',
      });
    }

    const supplierGroups = this.#groupBySupplier(selectedSuppliers.data);
    for (const supplierGroup of supplierGroups) {
      try {
        await this.#createPOForSupplier({
          transaction,
          supplierGroup,
          requisition: existingCanvass.requisition,
          canvassRequisitionId: existingCanvass.id,
        });
      } catch (error) {
        this.fastify.log.error('[ERROR] Creating PO for supplier');
        this.fastify.log.error(error);
        throw error;
      }
    }

    await this.requisitionRepository.update(
      { id: existingCanvass.requisition.id },
      { status: PO_STATUS.FOR_PO_REVIEW },
      { transaction },
    );
  }

  #groupBySupplier(selectedSuppliers) {
    const groups = new Map();

    for (const supplier of selectedSuppliers) {
      const key = `${supplier.supplierType}_${supplier.supplierId}_${supplier.term}`;

      if (!groups.has(key)) {
        groups.set(key, {
          supplierId: supplier.supplierId,
          supplierType: supplier.supplierType,
          term: supplier.term,
          items: [],
          totalAmount: 0,
          totalDiscount: 0,
          totalDiscountedAmount: 0,
        });
      }

      const group = groups.get(key);
      const itemAmount = supplier.unitPrice * supplier.quantity;
      let itemDiscount = supplier.discountValue ?? 0;

      if (supplier.discountType === 'percent') {
        itemDiscount = (supplier.discountValue / 100) * itemAmount;
      }

      group.totalAmount += itemAmount;
      group.totalDiscount += itemDiscount;
      group.totalDiscountedAmount = group.totalAmount - group.totalDiscount;
      group.items.push(supplier);
    }

    return Array.from(groups.values());
  }

  async #createPOForSupplier(payload = {}) {
    const { transaction, requisition, supplierGroup, canvassRequisitionId } =
      payload;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const [poIdentifier, poItems] = await Promise.all([
      this.#generatePONumberCode(transaction),
      Promise.resolve(
        supplierGroup.items.map((supplier) => ({
          canvassItemSupplierId: supplier.id,
          quantityPurchased: supplier.quantity,
          canvassItemId: supplier.canvassItemId,
          requisitionItemListId: supplier.canvassItem.requisitionItemListId,
        })),
      ),
    ]);

    const fixedTotalAmount = (supplierGroup.totalAmount ?? 0).toFixed(2);
    const fixedTotalDiscount = (supplierGroup.totalDiscount ?? 0).toFixed(2);
    const totalDiscountedAmount = fixedTotalAmount - fixedTotalDiscount;

    const purchaseOrder = await this.purchaseOrderRepository.create(
      {
        canvassRequisitionId,
        terms: supplierGroup.term,
        status: PO_STATUS.FOR_PO_REVIEW,
        requisitionId: requisition.id,
        poNumber: poIdentifier.poNumber,
        poLetter: poIdentifier.poLetter,
        supplierId: supplierGroup.supplierId,
        supplierType: supplierGroup.supplierType,
        totalAmount: fixedTotalAmount,
        totalDiscount: fixedTotalDiscount,
        totalDiscountedAmount: totalDiscountedAmount.toFixed(2),
        assignedTo: requisition.assignedTo,
        deliveryAddress: requisition.deliveryAddress,
      },
      { transaction },
    );

    const poItemsWithId = poItems.map((item) => ({
      ...item,
      purchaseOrderId: purchaseOrder.id,
    }));

    await Promise.all([
      this.purchaseOrderItemRepository.bulkCreate(poItemsWithId, {
        transaction,
      }),
      this.#generatePOApprovers({
        requisition,
        transaction,
        purchaseOrderId: purchaseOrder.id,
      }),
    ]);
  }

  async #generatePOApprovers({ purchaseOrderId, requisition, transaction }) {
    const { USER_TYPES } = this.constants.user;
    const [supervisorRole, purchasingHeadRole, assignedUser] =
      await Promise.all([
        this.roleRepository.findOne({
          where: { name: USER_TYPES.SUPERVISOR },
        }),
        this.roleRepository.findOne({
          where: { name: USER_TYPES.PURCHASING_HEAD },
        }),
        this.userRepository.getUserById(requisition.assignedTo, {
          paranoid: false,
        }),
      ]);

    if (!supervisorRole || !purchasingHeadRole) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Required approver roles for purchase order are not set',
      });
    }

    const userPurchaseHead = await this.userRepository.findOne({
      where: { roleId: purchasingHeadRole.id },
    });

    const approvers = [
      {
        level: 1,
        roleId: supervisorRole.id,
        purchaseOrderId,
        ...(assignedUser?.supervisor?.id && {
          userId: assignedUser.supervisor.id,
        }),
      },
      {
        level: 2,
        roleId: purchasingHeadRole.id,
        purchaseOrderId,
        ...(userPurchaseHead?.id && {
          userId: userPurchaseHead.id,
        }),
      },
    ];

    await this.purchaseOrderApproverRepository.bulkCreate(approvers, {
      transaction,
    });
  }

  async #generatePONumberCode(transaction) {
    const lastPurchaseOrder = await this.purchaseOrderRepository.findOne({
      order: [
        ['poLetter', 'DESC'],
        ['poNumber', 'DESC'],
      ],
      transaction,
      lock: true,
    });

    const { nextLetter, nextNumber } = this.utils.getNextNumberAndLetter(
      lastPurchaseOrder?.poNumber,
      lastPurchaseOrder?.poLetter,
    );

    return {
      poNumber: nextNumber,
      poLetter: nextLetter,
    };
  }

  async updatePurchaseOrder({ purchaseOrderId, body }) {
    const updateResults = await Promise.all(
      body.map(async (item) => {
        try {
          return await this.purchaseOrderItemRepository.update(
            { id: item.purchaseOrderItemId, purchaseOrderId },
            { quantityPurchased: item.quantityPurchased },
          );
        } catch (error) {
          this.fastify.log.error(
            `Failed to update item ${item.purchaseOrderItemId}:`,
            error,
          );
          return null;
        }
      }),
    );

    const failedUpdates = updateResults.filter((result) => result === null);
    if (failedUpdates.length > 0) {
      return false;
    }

    return true;
  }

  async submitPurchaseOrder({
    purchaseOrderId,
    warrantyId,
    isNewDeliveryAddress,
    newDeliveryAddress,
    addedDiscount,
    isAddedDiscountFixedAmount,
    isAddedDiscountPercentage,
  }) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    const alreadySubmitted =
      await this.purchaseOrderRepository.getById(purchaseOrderId);

    // validate added discount should be less than purchase order's total amount
    const { totalAmount: purchaseOrderAmount } =
      await this.purchaseOrderItemRepository.getPOItemsSummary({
        purchaseOrderId,
      });

    let purchaseOrderDiscount = 0;
    if (addedDiscount > 0 && isAddedDiscountFixedAmount) {
      purchaseOrderDiscount = parseFloat(addedDiscount).toFixed(2);
    } else if (addedDiscount > 0 && isAddedDiscountPercentage) {
      purchaseOrderDiscount =
        (parseFloat(addedDiscount).toFixed(2) / 100) * purchaseOrderAmount;
    }

    if (parseFloat(purchaseOrderDiscount) > parseFloat(purchaseOrderAmount)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Added discount value should not be larger than purchase order total amount. (₱ ${purchaseOrderAmount})`,
      });
    }

    if (alreadySubmitted.status === PO_STATUS.FOR_PO_APPROVAL) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Purchase order already submitted',
      });
    }

    return await this.purchaseOrderRepository.update(
      { id: purchaseOrderId },
      {
        status: PO_STATUS.FOR_PO_APPROVAL,
        warrantyId,
        isNewDeliveryAddress,
        newDeliveryAddress,
        addedDiscount,
        isAddedDiscountFixedAmount,
        isAddedDiscountPercentage,
      },
    );
  }

  async cancelPurchaseOrder(poId, transaction) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    const purchaseOrderId = parseInt(poId);
    const existingPurchaseOrder =
      await this.purchaseOrderRepository.getById(purchaseOrderId);

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID ${purchaseOrderId} not found`,
      });
    }

    if (
      existingPurchaseOrder.status === PO_STATUS.CANCELLED_PO ||
      existingPurchaseOrder.wasCancelled
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order with ID ${purchaseOrderId} has already been cancelled.`,
      });
    }

    const canCancelPOByDeliveryStatus =
      await this.#shouldAllowCancellationByDeliveryStatus(purchaseOrderId);

    const canCancelPOByStatus = this.#shouldAllowCancellationByPOStatus(
      existingPurchaseOrder.status,
    );

    if (!(canCancelPOByStatus || canCancelPOByDeliveryStatus)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order cannot be cancelled current status`,
      });
    }

    const purchaseOrderData =
      await this.purchaseOrderItemRepository.getPOItemsById({
        purchaseOrderId: existingPurchaseOrder.id,
      });

    if (!purchaseOrderData.total) {
      return;
    }

    // INCREMENT "CANCELLED QTY" BASED ON DR FULFILLMENT
    const mappedPurchaseOrderItems =
      await this.#mapPurchaseOrderItems(purchaseOrderId);

    console.log('mappedPurchaseOrderItems', mappedPurchaseOrderItems);

    const toCancelCanvassItems = mappedPurchaseOrderItems.filter(
      (poItem) => !poItem.quantityFulfilled,
    );

    const hasAtLeastOneDelivered = mappedPurchaseOrderItems.some(
      (poItem) => poItem.quantityDelivered > 0,
    );

    const poStatus = hasAtLeastOneDelivered
      ? PO_STATUS.FOR_DELIVERY
      : PO_STATUS.CANCELLED_PO;

    // INCREMENT CANCELLED QUANTITY
    for (const cancelledItem of toCancelCanvassItems) {
      await this.canvassItemRepository.update(
        { id: cancelledItem.canvassItemId },
        {
          cancelledQty: this.Sequelize.literal(
            `cancelled_qty + ${cancelledItem.quantityToCancel}`,
          ),
        },
        { transaction },
      );
    }

    return await this.purchaseOrderRepository.update(
      { id: purchaseOrderId },
      { status: poStatus, wasCancelled: true },
      { transaction },
    );
  }

  async approvePurchaseOrder(payload = {}) {
    const { existingPurchaseOrder, approver, transaction } = payload;

    const { PO_STATUS, PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    const forApprovalStatuses = [
      PO_STATUS.FOR_PO_APPROVAL,
      PO_STATUS.REJECT_PO,
    ];

    const isReadyForApproval = !forApprovalStatuses.includes(
      existingPurchaseOrder.status,
    );

    if (isReadyForApproval) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order sheet is not for approval`,
      });
    }

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { purchaseOrderId: existingPurchaseOrder.id },
      paginate: false,
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const currentApproverIndex = approvers.data.findIndex((approverRecord) => {
      const isApprover =
        approverRecord.userId === approver.id ||
        approverRecord.altApproverId === approver.id;

      return isApprover;
    });

    if (currentApproverIndex === -1) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to approve this purchase order`,
      });
    }

    const currentApprover = approvers.data[currentApproverIndex];

    if (currentApprover.status === PO_APPROVER_STATUS.APPROVED) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You already approved this purchase order`,
      });
    }

    if (currentApprover.isAdhoc) {
      const primaryApprover = approvers.data.find(
        (approver) =>
          approver.level === currentApprover.level && !approver.isAdhoc,
      );

      if (primaryApprover?.status !== PO_APPROVER_STATUS.APPROVED) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Level ${currentApprover.level} primary approver must approve first`,
        });
      }
    }

    await this.purchaseOrderApproverRepository.update(
      {
        userId: currentApprover.userId,
        purchaseOrderId: existingPurchaseOrder.id,
      },
      { status: PO_APPROVER_STATUS.APPROVED },
      { transaction },
    );

    const updatedApprovers = approvers.data.map((approver) => ({
      ...approver,
      status:
        approver.userId === currentApprover.userId
          ? PO_APPROVER_STATUS.APPROVED
          : approver.status,
    }));

    const allApproved = updatedApprovers.every(
      (approver) => approver.status === PO_APPROVER_STATUS.APPROVED,
    );

    if (allApproved) {
      await this.purchaseOrderRepository.update(
        { id: existingPurchaseOrder.id },
        { status: PO_STATUS.FOR_DELIVERY },
        { transaction },
      );

      await this.requisitionRepository.update(
        { id: existingPurchaseOrder.requisitionId },
        { status: PO_STATUS.FOR_DELIVERY },
        { transaction },
      );
    }

    return allApproved;
  }

  async rejectPurchaseOrder(payload = {}) {
    const { existingPurchaseOrder, approverId, transaction } = payload;
    const { PO_STATUS, PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { purchaseOrderId: existingPurchaseOrder.id },
      paginate: false,
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const currentApprover = approvers.data.find(
      (approver) => approver.userId === approverId,
    );

    if (!currentApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to reject this purchase order',
      });
    }

    if (currentApprover.status === PO_APPROVER_STATUS.REJECTED) {
      return;
    }

    if (currentApprover.status === PO_APPROVER_STATUS.APPROVED) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Cannot reject purchase order after approval',
      });
    }

    if (currentApprover.isAdhoc) {
      const primaryApprover = approvers.data.find(
        (approver) =>
          approver.level === currentApprover.level && !approver.isAdhoc,
      );

      if (primaryApprover?.status !== PO_APPROVER_STATUS.APPROVED) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Level ${currentApprover.level} primary approver must approve first`,
        });
      }
    }

    await this.purchaseOrderApproverRepository.update(
      {
        userId: currentApprover.userId,
        purchaseOrderId: existingPurchaseOrder.id,
      },
      { status: PO_APPROVER_STATUS.REJECTED },
      { transaction },
    );

    await this.purchaseOrderRepository.update(
      { id: existingPurchaseOrder.id },
      { status: PO_STATUS.REJECT_PO },
      { transaction },
    );
  }

  async addPurchaseOrderAdhocApprover(payload = {}) {
    const {
      purchaseOrderId,
      approver,
      creatorId,
      requisitionId,
      defaultApproverName,
      transaction,
    } = payload;
    const { PO_APPROVER_STATUS } = this.constants.purchaseOrder;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { userId: creatorId, purchaseOrderId },
      paginate: false,
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    if (!approvers.data.length) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Only existing approvers can add adhoc approvers',
      });
    }

    const creatorApprover = approvers.data[0];

    if (creatorApprover.isAdhoc) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adhoc approvers cannot add other adhoc approvers',
      });
    }

    if (creatorApprover.status !== PO_APPROVER_STATUS.PENDING) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Cannot add adhoc approver after approval/reject',
      });
    }

    const isExistingApprover =
      await this.purchaseOrderApproverRepository.findOne({
        where: {
          purchaseOrderId,
          userId: approver.id,
        },
      });

    if (isExistingApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Approver already exists',
      });
    }

    const existingAdhoc = await this.purchaseOrderApproverRepository.findOne({
      where: {
        purchaseOrderId,
        level: creatorApprover.level,
        isAdhoc: true,
      },
    });

    if (existingAdhoc) {
      await this.purchaseOrderApproverRepository.update(
        { id: existingAdhoc.id },
        { userId: approver.id },
        { transaction },
      );

      return;
    }

    await this.purchaseOrderApproverRepository.create(
      {
        purchaseOrderId,
        userId: approver.id,
        level: creatorApprover.level,
        isAdhoc: true,
        roleId: approver.role.id,
      },
      { transaction },
    );

    await this.notificationService.sendNotification({
      transaction,
      senderId: creatorId,
      type: NOTIFICATION_TYPES.PURCHASE_ORDER,
      title: NOTIFICATION_DETAILS.PURCHASE_ORDER_ADDITIONAL_APPROVER.title,
      message:
        NOTIFICATION_DETAILS.PURCHASE_ORDER_ADDITIONAL_APPROVER.message(
          defaultApproverName,
        ),
      recipientUserIds: [approver.id],
      metaData: {
        addedBy: creatorId,
        adhocApprover: approver.id,
        purchaseOrderId,
        requisitionId,
      },
    });
  }

  async removePurchaseOrderAdhocApprover(payload = {}) {
    const { purchaseOrderId, primaryApproverId } = payload;
    const { PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      paginate: false,
      where: {
        purchaseOrderId,
      },
    });

    const primaryApprover = approvers.data.find(
      (approver) => approver.userId === primaryApproverId && !approver.isAdhoc,
    );

    if (!primaryApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to remove adhoc approvers',
      });
    }

    if (primaryApprover.status !== PO_APPROVER_STATUS.PENDING) {
      throw this.clientErrors.FORBIDDEN({
        message:
          'You cannot remove an adhoc approver if your status of approval is not in pending state',
      });
    }

    const adhocApprover = approvers.data.find(
      (approver) =>
        approver.isAdhoc && approver.level === primaryApprover.level,
    );

    if (!adhocApprover) {
      throw this.clientErrors.NOT_FOUND({
        message: `No adhoc approver found for level ${primaryApprover.level}`,
      });
    }

    await this.purchaseOrderApproverRepository.destroy({
      id: adhocApprover.id,
    });
  }

  async getAllAllowedPOAdhocApprovers() {
    const { USER_TYPES } = this.constants.user;

    const allowablePOAdhocApproversList = await this.userRepository.findAll({
      where: {
        status: 'active',
        deletedAt: null,
      },
      include: [
        {
          association: 'role',
          attributes: ['id', 'name'],
          where: {
            name: {
              [this.db.Sequelize.Op.notIn]: [
                USER_TYPES.ROOT_USER,
                USER_TYPES.ADMIN,
              ],
            },
          },
        },
      ],
      attributes: {
        exclude: [
          'password',
          'tempPass',
          'otpSecret',
          'isPasswordTemporary',
          'supervisorId',
          'createdAt',
          'updatedAt',
          'deletedAt',
        ],
      },
    });

    return allowablePOAdhocApproversList;
  }

  async resubmitRejectedPurchaseOrder(payload = {}) {
    const { existingPurchaseOrder, transaction } = payload;

    const { PO_STATUS, PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    /*
    const isValidForResubmission =
      existingPurchaseOrder.status === PO_STATUS.REJECT_PO;

    if (!isValidForResubmission) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order sheet is not ready for resubmission.`,
      });
    }
    */

    await this.purchaseOrderApproverRepository.update(
      {
        purchaseOrderId: existingPurchaseOrder.id,
        status: PO_APPROVER_STATUS.REJECTED,
      },
      { status: PO_APPROVER_STATUS.PENDING },
      { transaction },
    );

    /*
    await this.purchaseOrderRepository.update(
      { id: existingPurchaseOrder.id, status: PO_STATUS.REJECT_PO },
      { status: PO_STATUS.FOR_PO_APPROVAL },
      { transaction },
    );
    */
  }

  async getPODetailsSupplierWarranty(purchaseOrderId) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'supplier',
        },
        {
          association: 'warranty',
        },
      ],
      attributes: {
        exclude: ['warrantyId'],
      },
    });

    return purchaseOrder;
  }

  async getPOListForDelivery(requisitionId) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    const poList = await this.purchaseOrderRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'PO-',
            this.db.Sequelize.col('requisition.company_code'),
            this.db.Sequelize.col('po_letter'),
            this.db.Sequelize.col('po_number'),
          ),
          'poNumber',
        ],
      ],
      where: {
        requisitionId,
        status: PO_STATUS.FOR_DELIVERY,
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.literal(`
            EXISTS (
              SELECT 1 FROM purchase_order_items poi
              WHERE poi.purchase_order_id = "purchase_orders"."id"
              AND poi.quantity_purchased != (
                SELECT COALESCE(SUM(dri.qty_delivered), 0)
                FROM delivery_receipt_items dri
                JOIN delivery_receipts dr ON dri.dr_id = dr.id
                WHERE dri.po_item_id = poi.id
                AND dr.is_draft = false
              )
            )
          `),
        ],
      },
      include: [
        {
          association: 'requisition',
          attributes: [],
        },
      ],
      order: [['id', 'ASC']],
    });

    return poList?.data;
  }

  async getPODetailsForDelivery(purchaseOrderId) {
    const pOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'canvassRequisition',
          attributes: ['id'],
          include: [
            {
              association: 'requisition',
              attributes: ['id', 'companyCode'],
            },
          ],
        },
      ],
    });

    if (!pOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    return {
      id: pOrder.id,
      poNumber: `PO-${pOrder.canvassRequisition.requisition.companyCode}${pOrder.poLetter}${pOrder.poNumber}`,
      supplier: { name: pOrder?.supplierName },
    };
  }

  async getPOListWithNoDeliveryReceipt(requisitionId) {
    const poList = await this.purchaseOrderRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'PO-',
            this.db.Sequelize.col('requisition.company_code'),
            this.db.Sequelize.col('po_letter'),
            this.db.Sequelize.col('po_number'),
          ),
          'poNumber',
        ],
      ],
      include: [
        {
          association: 'requisition',
          attributes: ['id', 'companyCode'],
        },
        {
          association: 'deliveryReceipts',

          attributes: ['id'],
          required: false,
        },
      ],
      where: {
        requisitionId,
        status: {
          [this.db.Sequelize.Op.or]: [
            this.constants.purchaseOrder.PO_STATUS.FOR_DELIVERY,
          ],
        },
      },
      order: [['poNumber', 'ASC']],
      subQuery: false,
    });

    return poList?.data;
  }

  async areSomePOItemsPartiallyDelivered(requisitionId) {
    try {
      const purchaseOrderCount = await this.purchaseOrderRepository.count({
        where: { requisitionId },
      });

      if (purchaseOrderCount === 0) {
        return false;
      }

      const query = `
        SELECT 
          CASE 
            WHEN EXISTS (
              SELECT 1 FROM purchase_orders 
              WHERE requisition_id = :requisitionId 
              AND status = '${this.constants.purchaseOrder.PO_STATUS.FOR_DELIVERY}'
            ) AND EXISTS (
              SELECT 1
              FROM (
                SELECT 
                  poi.id,
                  poi.quantity_purchased,
                  COALESCE(SUM(dri.qty_delivered), 0) as total_delivered
                FROM 
                  purchase_order_items poi
                JOIN 
                  purchase_orders po ON poi.purchase_order_id = po.id
                LEFT JOIN 
                  delivery_receipt_items dri ON dri.po_item_id = poi.id
                LEFT JOIN 
                  delivery_receipts dr ON dri.dr_id = dr.id AND dr.is_draft = false
                WHERE 
                  po.requisition_id = :requisitionId
                GROUP BY 
                  poi.id, poi.quantity_purchased
                HAVING 
                  poi.quantity_purchased > COALESCE(SUM(dri.qty_delivered), 0)
              ) as undelivered_items
            ) THEN true
            ELSE false
          END as has_for_delivery_and_undelivered_items`;

      const [results] = await this.db.sequelize.query(query, {
        replacements: { requisitionId },
        type: this.Sequelize.QueryTypes.SELECT,
      });

      return results.has_for_delivery_and_undelivered_items;
    } catch (error) {
      this.fastify.log.error(
        '[ERROR] Checking if all PO items are fully delivered',
      );
      this.fastify.log.error(error);
      throw error;
    }
  }

  async closePurchaseOrder(purchaseOrderId) {
    try {
      const { PO_STATUS } = this.constants.purchaseOrder;

      // Get purchase order details
      const purchaseOrder = await this.purchaseOrderRepository.findOne({
        where: { id: purchaseOrderId },
        attributes: [
          'id',
          'totalDiscountedAmount',
          'status',
          'poNumber',
          'poLetter',
          'terms',
        ],
        include: [
          {
            association: 'supplier',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'company',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'project',
            attributes: ['id', 'name'],
            required: false,
          },
        ],
        raw: false,
      });

      if (!purchaseOrder) {
        throw this.clientErrors.NOT_FOUND({
          message: `Purchase order not found with ID: ${purchaseOrderId}`,
        });
      }

      // Check if PO is in FOR_DELIVERY status
      if (purchaseOrder.status !== PO_STATUS.FOR_DELIVERY) {
        throw this.clientErrors.BAD_REQUEST({
          message: 'Only purchase orders in FOR_DELIVERY status can be closed',
        });
      }

      // Get PO items with their requisition item details
      const poItemsResult = await this.purchaseOrderItemRepository.findAll({
        where: { purchaseOrderId },
        include: [
          {
            association: 'requisitionItemList',
            attributes: ['notes', 'itemType'],
            required: true,
            include: [
              {
                association: 'item',
                required: false,
                attributes: ['id', 'itmDes', 'unit'],
              },
              {
                association: 'nonOfmItem',
                required: false,
                attributes: ['id', 'itemName', 'unit'],
              },
            ],
          },
        ],
        paginate: false,
      });

      if (
        !poItemsResult ||
        !poItemsResult.data ||
        poItemsResult.data.length === 0
      ) {
        this.fastify.log.error(
          `No items found for purchase order ID: ${purchaseOrderId}`,
        );
        throw this.clientErrors.BAD_REQUEST({
          message: `Cannot close purchase order: No items found for purchase order ID: ${purchaseOrderId}`,
        });
      }

      // Process the items to get a consistent format
      const poItems = poItemsResult.data.map((item) => {
        const isOfm = item.requisitionItemList?.item;
        return {
          id: item.id,
          quantityPurchased: item.quantityPurchased,
          itemName: isOfm
            ? item.requisitionItemList.item.itmDes
            : item.requisitionItemList.nonOfmItem?.itemName || 'Unknown Item',
          unit: isOfm
            ? item.requisitionItemList.item.unit
            : item.requisitionItemList.nonOfmItem?.unit || '',
        };
      });

      const deliveryStatus = [];
      const undeliveredItems = [];

      // Check if all items are fully delivered with detailed monitoring
      for (const item of poItems) {
        // Get delivered quantity from delivery receipt items
        const deliveredQuantity = await this.db.sequelize.query(
          `
          SELECT COALESCE(SUM(dri.qty_delivered), 0) as total_delivered
          FROM delivery_receipt_items dri
          JOIN delivery_receipts dr ON dri.dr_id = dr.id
          WHERE dri.po_item_id = :poItemId AND dr.is_draft = false
          `,
          {
            replacements: { poItemId: item.id },
            type: this.Sequelize.QueryTypes.SELECT,
            plain: true,
          },
        );

        // Get item description from delivery receipt items
        const itemDescriptionResult = await this.db.sequelize.query(
          `
          SELECT DISTINCT item_des
          FROM delivery_receipt_items
          WHERE po_item_id = :poItemId
          LIMIT 1
          `,
          {
            replacements: { poItemId: item.id },
            type: this.Sequelize.QueryTypes.SELECT,
            plain: true,
          },
        );

        const purchasedQty = parseFloat(item.quantityPurchased);
        const deliveredQty = parseFloat(deliveredQuantity.total_delivered);

        // Use delivery receipt item description if available, otherwise use the original item name
        const itemName = itemDescriptionResult?.item_des || item.itemName;

        // Track delivery status for each item
        deliveryStatus.push({
          itemId: item.id,
          itemName: itemName,
          quantityPurchased: purchasedQty,
          quantityDelivered: deliveredQty,
          isFullyDelivered: purchasedQty === deliveredQty,
        });

        // Identify items that are not fully delivered
        if (purchasedQty !== deliveredQty) {
          undeliveredItems.push({
            itemId: item.id,
            itemName: itemName,
            quantityPurchased: purchasedQty,
            quantityDelivered: deliveredQty,
            remaining: (purchasedQty - deliveredQty).toFixed(2),
          });
        }
      }

      // Throw error if any items are not fully delivered
      if (undeliveredItems.length > 0) {
        const itemDetailsList = undeliveredItems.map((item) => ({
          name: item.itemName,
          ordered: item.quantityPurchased,
          delivered: item.quantityDelivered,
          remaining: item.remaining,
        }));

        throw this.clientErrors.BAD_REQUEST({
          message: `Cannot close purchase order: Not all items have been fully delivered.`,
          description: {
            metadata: itemDetailsList,
          },
        });
      }

      // Get payment requests separately to verify total amount
      const paymentRequests = await this.db.sequelize.query(
        `
        SELECT id, total_amount, status, created_at
        FROM rs_payment_requests
        WHERE purchase_order_id = :purchaseOrderId
      `,
        {
          replacements: { purchaseOrderId },
          type: this.Sequelize.QueryTypes.SELECT,
        },
      );

      const paymentRequestAmount = paymentRequests.reduce(
        (sum, payment) => sum + parseFloat(payment.total_amount || 0),
        0,
      );

      const purchaseOrderAmount = parseFloat(
        purchaseOrder.totalDiscountedAmount,
      );
      const paymentAmount = parseFloat(paymentRequestAmount.toFixed(2));

      // Get supplier name based on supplier type
      let supplierName = 'N/A';
      if (purchaseOrder.supplier) {
        supplierName = purchaseOrder.supplier.name;
      } else if (purchaseOrder.company) {
        supplierName = purchaseOrder.company.name;
      } else if (purchaseOrder.project) {
        supplierName = purchaseOrder.project.name;
      }

      // Verify that payment amount matches purchase order amount
      if (purchaseOrderAmount !== paymentAmount) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Cannot close purchase order: Total payment amount (${paymentAmount}) does not match purchase order amount (${purchaseOrderAmount})`,
          description: {
            metadata: {
              purchaseOrderId: purchaseOrder.id,
              poNumber: `PO-${purchaseOrder.poLetter}${purchaseOrder.poNumber}`,
              supplier: supplierName,
              terms: purchaseOrder.terms,
              purchaseOrderAmount: purchaseOrderAmount,
              paymentRequestAmount: paymentAmount,
              difference: (purchaseOrderAmount - paymentAmount).toFixed(2),
              paymentRequests: paymentRequests.map((pr) => ({
                id: pr.id,
                amount: parseFloat(pr.total_amount || 0),
                status: pr.status,
                createdAt: pr.created_at,
              })),
              deliveryStatus: {
                totalItems: poItems.length,
                fullyDeliveredItems: poItems.length - undeliveredItems.length,
                itemDetails: deliveryStatus,
              },
            },
          },
        });
      }

      // Update PO status to CLOSED_PO
      await this.purchaseOrderRepository.update(
        { id: purchaseOrderId },
        { status: PO_STATUS.CLOSED_PO },
      );

      return {
        success: true,
        message: 'Purchase order closed successfully',
        metadata: {
          purchaseOrderId: purchaseOrder.id,
          poNumber: `PO-${purchaseOrder.poLetter}${purchaseOrder.poNumber}`,
          supplier: supplierName,
          terms: purchaseOrder.terms,
          purchaseOrderAmount: purchaseOrderAmount,
          paymentRequestAmount: paymentAmount,
          paymentRequests: paymentRequests.map((pr) => ({
            id: pr.id,
            amount: parseFloat(pr.total_amount || 0),
            status: pr.status,
            createdAt: pr.created_at,
          })),
          deliveryStatus: {
            totalItems: poItems.length,
            fullyDeliveredItems: poItems.length,
            itemDetails: deliveryStatus,
          },
        },
      };
    } catch (error) {
      this.fastify.log.error('[ERROR] Closing purchase order');
      this.fastify.log.error(error);
      throw error;
    }
  }

  async getSupplierDetails(purchaseOrderId) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      attributes: ['id', 'supplierId', 'supplierType'],
    });

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    let supplier;
    if (purchaseOrder.supplierType === 'supplier') {
      supplier = await this.supplierRepository.findOne({
        attributes: ['id', 'name', 'tin'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });
    } else if (purchaseOrder.supplierType === 'company') {
      supplier = await this.companyRepository.findOne({
        attributes: ['id', 'name', 'tin'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });
    }

    return supplier;
  }
  async generatePurchaseOrderPdf(purchaseOrderId) {
    // Get Purchase Order details
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { id: purchaseOrderId },
      attributes: [
        'id',
        'poLetter',
        'poNumber',
        'totalDiscountedAmount',
        'terms',
        'isNewDeliveryAddress',
        'newDeliveryAddress',
        'supplierName',
      ],
      include: [
        {
          association: 'requisition',
          attributes: ['companyCode', 'deliveryAddress', 'dateRequired'],
          include: [
            {
              association: 'company',
              attributes: ['name', 'tin'],
            },
            {
              association: 'project',
              attributes: ['name'],
              include: [
                {
                  association: 'company',
                  attributes: ['id', 'contactNumber'],
                },
              ],
            },
            {
              association: 'assignee',
            },
          ],
        },
        {
          association: 'supplier',
          attributes: [
            'name',
            'tin',
            'contactPerson',
            'contactNumber',
            'address',
          ],
        },
        {
          association: 'warranty',
          attributes: ['name'],
        },
        {
          association: 'purchaseOrderApprovers',
          attributes: ['id'],
          include: [
            {
              association: 'approver',
              attributes: ['firstName', 'lastName'],
            },
            {
              association: 'role',
              attributes: ['name'],
            },
          ],
        },
      ],
    });

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID ${purchaseOrderId} not found`,
      });
    }

    // Get PO items with correct associations
    const poItems = await this.purchaseOrderItemRepository.findAll({
      where: { purchaseOrderId },
      include: [
        {
          association: 'canvassItemSupplier',
          include: [
            {
              association: 'canvassItem',
            },
          ],
        },
        {
          association: 'requisitionItemList',
          include: [
            {
              association: 'item',
              required: false,
            },
            {
              association: 'nonOfmItem',
              required: false,
            },
          ],
        },
      ],
      paginate: false,
    });

    if (!poItems || !poItems.data || !poItems.data.length) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No items found for this Purchase Order',
      });
    }

    // Get PO summary details for grandTotal
    const poSummary = await this.purchaseOrderItemRepository.getPOItemsSummary({
      purchaseOrderId,
    });

    // Format date in the required format (e.g., "12 MAY 2025")
    const formatDateToUpperCase = (date) => {
      if (!date) return 'N/A';
      const d = new Date(date);
      const months = [
        'JAN',
        'FEB',
        'MAR',
        'APR',
        'MAY',
        'JUN',
        'JUL',
        'AUG',
        'SEP',
        'OCT',
        'NOV',
        'DEC',
      ];
      return `${d.getDate()} ${months[d.getMonth()]} ${d.getFullYear()}`;
    };

    // Helper function to format numbers since formatNumber is not available
    const formatNumber = (value, decimals = 2) => {
      if (value === null || value === undefined) return '0.00';
      return parseFloat(value)
        .toFixed(decimals)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    };

    // Format items for template
    const formattedItems = poItems.data.map((item, index) => {
      // Get item name and unit based on item type
      let itemName = 'N/A';
      let unit = 'Unit';
      let notes = '';

      if (item.requisitionItemList) {
        notes = item.requisitionItemList.notes || '';

        if (item.requisitionItemList.item) {
          itemName = item.requisitionItemList.item.itmDes || 'N/A';
          unit = item.requisitionItemList.item.unit || 'Unit';
        } else if (item.requisitionItemList.nonOfmItem) {
          itemName = item.requisitionItemList.nonOfmItem.itemName || 'N/A';
          unit = item.requisitionItemList.nonOfmItem.unit || 'Unit';
        }
      }

      // Get price information from canvassItemSupplier
      const unitPrice = item.canvassItemSupplier?.unitPrice || 0;
      const discount =
        parseFloat(item.canvassItemSupplier?.discountValue) *
          item.quantityPurchased || 0;
      const totalPrice = item.quantityPurchased * unitPrice - discount;

      return {
        id: index + 1,
        itemName: itemName,
        latestNote: notes || `PO-${purchaseOrder.poNumber}`,
        requestedQty: item.quantityPurchased,
        unit: unit,
        unitPrice: `₱${formatNumber(unitPrice)}`,
        discount: `₱${formatNumber(discount)}`,
        totalPrice: `₱${formatNumber(totalPrice)}`,
      };
    });

    // Paginate items
    const { pagesData, totalPages, totalItems } =
      this.utils.paginateItemsWithDifferentSizes(formattedItems, 15, 20);

    // Get company and supplier details
    const company = purchaseOrder.requisition?.company;
    const project = purchaseOrder.requisition?.project;
    const supplier = purchaseOrder.supplier;

    // Generate PO number
    const poNumber = `PO-${purchaseOrder.requisition?.companyCode || ''}${purchaseOrder.poLetter}${purchaseOrder.poNumber}`;

    // Find approvers by role
    let supervisorApprover = 'N/A';
    let purchasingHeadApprover = 'N/A';

    if (
      purchaseOrder.purchaseOrderApprovers &&
      purchaseOrder.purchaseOrderApprovers.length > 0
    ) {
      // Find supervisor approver
      const supervisor = purchaseOrder.purchaseOrderApprovers.find(
        (approver) =>
          approver.role &&
          approver.role.name === 'Supervisor' &&
          approver.approver,
      );

      if (supervisor && supervisor.approver) {
        supervisorApprover =
          `${supervisor.approver.firstName || ''} ${supervisor.approver.lastName || ''}`.trim();
      }

      // Find purchasing head approver
      const purchasingHead = purchaseOrder.purchaseOrderApprovers.find(
        (approver) =>
          approver.role &&
          approver.role.name === 'Purchasing Head' &&
          approver.approver,
      );

      if (purchasingHead && purchasingHead.approver) {
        purchasingHeadApprover =
          `${purchasingHead.approver.firstName || ''} ${purchasingHead.approver.lastName || ''}`.trim();
      }
    }

    // Prepare data in the exact format required
    const poData = {
      poNumber: poNumber,
      dateExtracted: formatDateToUpperCase(new Date()),
      projectName: project?.name || company?.name || 'N/A',
      tin: company?.tin || 'N/A',
      businessStyle: project?.name || company?.name || 'N/A',
      companyName: supplier?.name || purchaseOrder.supplierName || 'N/A',
      vendorTin: supplier?.tin || 'N/A',
      contactName: supplier?.contactPerson || 'N/A',
      contactNumber: supplier?.contactNumber || 'N/A',
      streetAddress: supplier?.address || 'N/A',
      deliverToStreetAddress: purchaseOrder.isNewDeliveryAddress
        ? purchaseOrder.newDeliveryAddress
        : purchaseOrder.requisition?.deliveryAddress || 'N/A',
      deliverToContactPerson: project?.company.contactNumber || 'N/A',

      termsOfPayment: purchaseOrder.terms || 'N/A',
      deliveryTime: formatDateToUpperCase(
        purchaseOrder.requisition?.dateRequired,
      ),
      warranty: purchaseOrder.warranty?.name || 'N/A',
      otherSpecifications: '',

      purchasingStaffName:
        purchaseOrder.requisition?.assignee?.fullNameUser || 'N/A',
      notedBy: supervisorApprover,
      approvedBy: purchasingHeadApprover,

      totalPages,
      totalItems,
      pagesData,
      totalDiscount: `₱${formatNumber(poSummary.discount)}`,
      grandTotal: `₱${formatNumber(poSummary.totalAmount)}`,
      amountInWords: this.utils.amountInWordsPHP(poSummary.totalAmount),
    };

    return {
      templateData: poData,
      fileName: poNumber.replace(/\s+/g, '-'),
    };
  }
}

module.exports = PurchaseOrderService;
