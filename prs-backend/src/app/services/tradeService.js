class TradeService {
  constructor(container) {
    const { db, tradeRepository, clientErrors } = container;

    this.db = db;
    this.clientErrors = clientErrors;
    this.tradeRepository = tradeRepository;
  }

  async getTradeById(tradeId) {
    const existingTrade = await this.tradeRepository.getById(tradeId);

    if (!existingTrade) {
      throw this.clientErrors.NOT_FOUND({
        message: `Trade not found with ID: ${tradeId}`,
      });
    }

    return existingTrade;
  }
}

module.exports = TradeService;
