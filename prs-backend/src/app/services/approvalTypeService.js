class ApprovalTypeService {
  constructor(container) {
    const { approvalTypeRepository, clientErrors } = container;
    this.clientErrors = clientErrors;
    this.approvalTypeRepository = approvalTypeRepository;
  }

  async getApprovalTypeByCode(code) {
    const approvalType = await this.approvalTypeRepository.findOne({
      where: { code },
    });

    if (!approvalType) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Invalid approval type code',
      });
    }

    return approvalType;
  }
}

module.exports = ApprovalTypeService;
