const { note } = require('../../domain/entities');
const { convertDateToDDMMMYYYY } = require('../utils');

class NoteService {
  constructor({
    db,
    noteRepository,
    noteBadgeRepository,
    clientErrors,
    fastify,
    entities,
    utils,
  }) {
    this.db = db;
    this.noteRepository = noteRepository;
    this.noteBadgeRepository = noteBadgeRepository;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.entities = entities;
    this.utils = utils;
  }

  async createNote(note, options = {}) {
    const parsedNote = this.utils.parseDomain(
      this.entities.note.createNoteSchema,
      note,
    );
    const result = await this.noteRepository.create(parsedNote, options);
    return this._formatNote(result);
  }

  async getNotes({ model, modelId, dateFrom, dateTo }) {
    const result = await this.noteRepository.getByModel(model, modelId, {
      startDate: dateFrom,
      endDate: dateTo,
    });
    const formattedResult = await Promise.all(
      result.map((note) => this._formatNote(note)),
    );

    // if note has an empty badge data array it means that it has not been seen by the user
    const hasNewNotifications = formattedResult.some((note) =>
      note.badges.length === 0 ? true : false,
    );

    return {
      hasNewNotifications,
      formattedResult,
    };
  }

  async _formatNote(note) {
    return {
      id: note.id,
      userName: note.userName,
      userType: note.userType,
      commentType: note.commentType,
      note: note.note,
      createdAt: convertDateToDDMMMYYYY(note.createdAt),
      badges: note.badges,
    };
  }

  async markNoteAsSeen({ model, modelId, userFromToken, dateFrom, dateTo }) {
    const existingNotesByModule = await this.noteRepository.getByModel(
      model,
      modelId,
      {
        startDate: dateFrom,
        endDate: dateTo,
      },
    );

    if (existingNotesByModule.length === 0) {
      throw this.clientErrors.NOT_FOUND({
        message: `Notes related to  "${model}" with an id ${modelId} was not found.`,
      });
    }

    const unseenNotes = existingNotesByModule.filter((note) => {
      return note.badges.length === 0;
    });

    try {
      await this.noteBadgeRepository.bulkCreate(
        unseenNotes.map((note) => ({
          userId: parseInt(userFromToken.id),
          noteId: note.id,
        })),
      );

      return {
        message: `Notes related to ${model} with id of ${modelId} was marked as seen`,
      };
    } catch (error) {
      throw error;
    }
  }

  async getLastNotes({ model, modelId }) {
    const result = await this.noteRepository.findAll({
      attributes: ['id', 'note'],
      where: {
        model,
        modelId,
      },
      order: [['createdAt', 'DESC']],
    });

    this.fastify.log.info(`GET_LAST_NOTES: ${JSON.stringify(result)}`);

    if (!result) {
      return null;
    }

    return result;
  }
}

module.exports = NoteService;
