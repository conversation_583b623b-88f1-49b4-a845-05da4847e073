async function invoiceReportRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const invoiceReportController = fastify.diScope.resolve(
    'invoiceReportController',
  );

  // POST /v1/invoice-reports
  fastify.route({
    method: 'POST',
    url: '/',
    schema: {
      body: entities.invoiceReport.createUpdateInvoiceReportSchema,
    },
    handler: invoiceReportController.createInvoiceReport.bind(
      invoiceReportController,
    ),
  });

  // GET /v1/invoice-reports/:id
  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.invoiceReport.getInvoiceReportByIdSchema,
    },
    handler: invoiceReportController.getInvoiceReportById.bind(
      invoiceReportController,
    ),
  });

  // GET /v1/invoice-reports/:id/delivery-reports
  fastify.route({
    method: 'GET',
    url: '/:id/delivery-reports',
    schema: {
      params: entities.invoiceReport.getInvoiceReportByIdSchema,
      query: entities.invoiceReport.filterDeliveryReportsSchema,
    },
    handler: invoiceReportController.getDeliveryReportsByInvoiceReportId.bind(
      invoiceReportController,
    ),
  });

  // GET /v1/invoice-reports/:id/delivery-report-items
  fastify.route({
    method: 'GET',
    url: '/:id/delivery-report-items',
    schema: {
      params: entities.invoiceReport.getInvoiceReportByIdSchema,
      query: entities.invoiceReport.filterDeliveryReportItemsSchema,
    },
    handler:
      invoiceReportController.getDeliveryReportItemsByInvoiceReportId.bind(
        invoiceReportController,
      ),
  });

  // PUT /v1/invoice-reports/:id
  fastify.route({
    method: 'PUT',
    url: '/:id',
    schema: {
      params: entities.invoiceReport.getInvoiceReportByIdSchema,
      body: entities.invoiceReport.createUpdateInvoiceReportSchema,
    },
    handler: invoiceReportController.updateInvoiceReport.bind(
      invoiceReportController,
    ),
  });
}

module.exports = invoiceReportRoutes;
