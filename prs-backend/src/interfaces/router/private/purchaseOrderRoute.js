async function purchaseOrderRouts(fastify) {
  const purchaseOrderController = fastify.diScope.resolve(
    'purchaseOrderController',
  );
  const deliveryReceiptController = fastify.diScope.resolve(
    'deliveryReceiptController',
  );
  const entities = fastify.diScope.resolve('entities');

  fastify.route({
    method: 'GET',
    url: '/cancelled-items',
    handler: purchaseOrderController.getCancelledItemsByPOIds.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.getPOById.bind(purchaseOrderController),
  });

  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/items',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.getPOItemsById.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/delivery-receipts',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
      query:
        entities.deliveryReceipt
          .getDeliveryReceiptsFromPurchaseOrderQuerySchema,
    },
    handler:
      deliveryReceiptController.getDeliveryReceiptsFromPurchaseOrderId.bind(
        deliveryReceiptController,
      ),
  });

  fastify.route({
    method: 'PATCH',
    url: '/:purchaseOrderId/items',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
      body: entities.purchaseOrder.updatePurchaseOrderParams,
    },
    handler: purchaseOrderController.updatePurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/submit',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
      body: entities.purchaseOrder.submitPurchaseOrderBodySchema,
    },
    handler: purchaseOrderController.submitPurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/cancel',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.cancelPurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/warranties',
    schema: {
      query: entities.warranty.getWarrantiesSchema,
    },
    handler: purchaseOrderController.getWarranties.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/warranties',
    schema: {
      body: entities.warranty.createWarrantySchema,
    },
    handler: purchaseOrderController.createWarranty.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/approvers',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.getPurchaseOrderApprovers.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/assignee',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.getPurchaseOrderAssignee.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/approve',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
      body: entities.note.approveReasonSchema,
    },
    handler: purchaseOrderController.approvePurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/reject',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
      body: entities.note.rejectReasonSchema,
    },
    handler: purchaseOrderController.rejectPurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/add-adhoc-approver',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
      body: entities.purchaseOrder.addPurchaseOrderAdhocApproverSchema,
    },
    handler: purchaseOrderController.addPurchaseOrderAdhocApprover.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:purchaseOrderId/remove-adhoc-approver',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.removePurchaseOrderAdhocApprover.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/allowed-po-adhoc-approvers',
    handler: purchaseOrderController.getAllAllowedPOAdhocApprovers.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/resubmit-rejected-po',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.resubmitRejectedPurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/for-delivery',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.getPODetailsForDelivery.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/close',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.closePurchaseOrder.bind(
      purchaseOrderController,
    ),
  });
}

module.exports = purchaseOrderRouts;
