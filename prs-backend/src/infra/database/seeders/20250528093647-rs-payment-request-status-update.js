'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const statusMap = {
      draft: 'PR Draft',
      rejected: 'PR Rejected',
      for_pr_approval: 'For PR Approval',
      submitted: 'submitted',
      approved: 'Closed',
    };

    const tablesToUpdate = [
      'rs_payment_requests',
      'requisition_payment_histories',
    ];

    for (const oldStatus in statusMap) {
      if (statusMap.hasOwnProperty(oldStatus)) {
        const newStatus = statusMap[oldStatus];

        for (const tableName of tablesToUpdate) {
          await queryInterface.bulkUpdate(
            tableName,
            { status: newStatus },
            { status: oldStatus },
          );
        }
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    const statusMapReverse = {
      'PR Draft': 'draft',
      'PR Rejected': 'rejected',
      'For PR Approval': 'for_pr_approval',
      submitted: 'submitted',
      Closed: 'approved',
    };

    const tablesToUpdate = [
      'rs_payment_requests',
      'requisition_payment_histories',
    ];

    for (const newStatus in statusMapReverse) {
      if (statusMapReverse.hasOwnProperty(newStatus)) {
        const oldStatus = statusMapReverse[newStatus];

        for (const tableName of tablesToUpdate) {
          await queryInterface.bulkUpdate(
            tableName,
            { status: oldStatus },
            { status: newStatus },
          );
        }
      }
    }
  },
};
