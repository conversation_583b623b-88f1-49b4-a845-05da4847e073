require('dotenv').config();

const { Sequelize } = require('sequelize');
const dbConfig = require('./dbConfig');

async function getConnection() {
  const sequelize = new Sequelize(
    dbConfig[
      process.env.NODE_ENV === 'production' ? 'production' : 'development'
    ],
  );

  await sequelize.authenticate();

  console.log('Database connected.');

  return sequelize;
}

module.exports = getConnection;
