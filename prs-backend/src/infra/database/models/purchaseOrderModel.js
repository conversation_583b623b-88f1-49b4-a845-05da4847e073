const {
  PO_STATUS,
} = require('../../../domain/constants/purchaseOrderConstants');
const { DISCOUNT_TYPE } = require('../../../domain/constants/canvassConstants');

module.exports = (sequelize, Sequelize) => {
  const PurchaseOrderModel = sequelize.define(
    'purchase_orders',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      poNumber: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'po_number',
      },
      poLetter: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'po_letter',
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'supplier_id',
      },
      supplierType: {
        allowNull: false,
        type: Sequelize.STRING(50),
        field: 'supplier_type',
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: PO_STATUS.FOR_PO_REVIEW,
      },
      deliveryAddress: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'delivery_address',
      },
      newDeliveryAddress: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'new_delivery_address',
      },
      isNewDeliveryAddress: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_new_delivery_address',
      },
      terms: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      warrantyId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'warranty_id',
      },
      addedDiscount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        defaultValue: 0,
        field: 'added_discount',
      },
      isAddedDiscountFixedAmount: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_added_discount_fixed_amount',
      },
      isAddedDiscountPercentage: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_added_discount_percentage',
      },
      depositPercent: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'deposit_percent',
      },
      totalAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_amount',
      },
      totalDiscount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_discount',
      },
      totalDiscountedAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_discounted_amount',
      },
      wasCancelled: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'was_cancelled',
      },
      supplierDetails: {
        type: Sequelize.VIRTUAL,
        get() {
          switch (this.supplierType) {
            case 'project':
              return this.project;
            case 'company':
              return this.company;
            case 'supplier':
              return this.supplier;
            default:
              return null;
          }
        },
      },
      supplierName: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'supplier_name',
      },
      supplierNameLocked: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'supplier_name_locked',
      },
      assignedTo: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'assigned_to',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  const createRequestHistoryRecord = async (instance, { transaction }) => {
    const requisition = await sequelize.model('requisitions').findOne({
      attributes: ['companyCode'],
      where: {
        id: instance.requisitionId,
      },
    });

    let supplier;
    if (instance.supplierType === 'supplier') {
      supplier = await sequelize
        .model('suppliers')
        .findOne({ attributes: ['name'], where: { id: instance.supplierId } });
    } else if (instance.supplierType === 'project') {
      supplier = await sequelize
        .model('projects')
        .findOne({ attributes: ['name'], where: { id: instance.supplierId } });
    } else {
      supplier = await sequelize
        .model('companies')
        .findOne({ attributes: ['name'], where: { id: instance.supplierId } });
    }

    await sequelize.model('requisition_order_histories').create(
      {
        requisitionId: instance.requisitionId,
        poNumber: `PO-${requisition.companyCode}${instance.poLetter}${instance.poNumber}`,
        supplier: supplier.name,
        poPrice: instance.totalDiscountedAmount,
        dateOrdered: new Date(),
        status: instance.status,
      },
      {
        transaction,
      },
    );
  };

  PurchaseOrderModel.afterCreate(async (instance, { transaction }) => {
    await createRequestHistoryRecord(instance, { transaction });
  });

  const getSupplier = async (supplierId, supplierType) => {
    let supplier;
    if (supplierType === 'supplier') {
      supplier = await sequelize.model('suppliers').findOne({
        where: {
          id: supplierId,
        },
      });
    } else if (supplierType === 'project') {
      supplier = await sequelize.model('projects').findOne({
        where: {
          id: supplierId,
        },
      });
    } else if (supplierType === 'company') {
      supplier = await sequelize.model('companies').findOne({
        where: {
          id: supplierId,
        },
      });
    }
    return supplier;
  };

  // locks supplierName when status is changed to FOR_DELIVERY
  PurchaseOrderModel.beforeUpdate(async (purchaseOrder, { transaction }) => {
    if (
      purchaseOrder.changed('status') &&
      purchaseOrder.status === PO_STATUS.FOR_DELIVERY
    ) {
      const supplier = await getSupplier(
        purchaseOrder.supplierId,
        purchaseOrder.supplierType,
      );
      purchaseOrder.supplierName = supplier.name;
      purchaseOrder.supplierNameLocked = true;
    }
  });

  // locks supplierName when status is changed to FOR_DELIVERY in canvass_item_suppliers
  PurchaseOrderModel.afterUpdate(async (purchaseOrder, { transaction }) => {
    if (
      purchaseOrder.changed('supplierNameLocked') &&
      purchaseOrder.supplierNameLocked
    ) {
      const canvassRequisition = await sequelize
        .model('canvass_requisitions')
        .findOne({
          where: {
            id: purchaseOrder.canvassRequisitionId,
          },
          include: [
            {
              model: sequelize.model('canvass_items'),
              as: 'canvassItems',
              include: [
                {
                  model: sequelize.model('canvass_item_suppliers'),
                  as: 'suppliers',
                  where: {
                    supplierId: purchaseOrder.supplierId,
                    supplierType: purchaseOrder.supplierType,
                  },
                },
              ],
            },
          ],
        });

      const itemSupplierIds = canvassRequisition.canvassItems.flatMap((item) =>
        item.suppliers.map((supplier) => supplier.id),
      );

      const supplier = await getSupplier(
        purchaseOrder.supplierId,
        purchaseOrder.supplierType,
      );

      await sequelize.model('canvass_item_suppliers').update(
        {
          supplierName: supplier.name,
          supplierNameLocked: true,
        },
        {
          where: {
            id: {
              [Sequelize.Op.in]: itemSupplierIds,
            },
          },
        },
        { transaction },
      );
    }
  });

  // add history record
  PurchaseOrderModel.afterUpdate(async (instance, { transaction }) => {
    if (instance.changed('status')) {
      await createRequestHistoryRecord(instance, { transaction });

      if (instance.status === PO_STATUS.FOR_DELIVERY) {
        const requisition = await sequelize.model('requisitions').findOne({
          where: {
            id: instance.requisitionId,
          },
        });

        const purchaseOrderItems = await sequelize
          .model('purchase_order_items')
          .findAll({
            where: {
              purchaseOrderId: instance.id,
            },
            include: [
              {
                model: sequelize.model('canvass_item_suppliers'),
                as: 'canvassItemSupplier',
              },
              {
                model: sequelize.model('requisition_item_lists'),
                as: 'requisitionItemList',
              },
            ],
          });

        for (const item of purchaseOrderItems) {
          const { canvassItemSupplier, requisitionItemList } = item;

          let price = null;
          if (canvassItemSupplier.discountType === DISCOUNT_TYPE.PERCENT) {
            price =
              canvassItemSupplier.unitPrice -
              canvassItemSupplier.unitPrice *
                (canvassItemSupplier.discountValue / 100);
          } else if (canvassItemSupplier.discountType === DISCOUNT_TYPE.FIXED) {
            price =
              canvassItemSupplier.unitPrice - canvassItemSupplier.discountValue;
          }

          const historyRecord = await sequelize.model('histories').findOne({
            where: {
              rsLetter: requisition.rsLetter,
              rsNumber: requisition.rsNumber,
              companyId: requisition.companyId,
              itemId: requisitionItemList.itemId,
            },
          });

          if (historyRecord) {
            await historyRecord.update(
              {
                price: `₱ ${price.toFixed(2)}`,
              },
              { transaction },
            );
          }
        }
      }
    }
  });

  PurchaseOrderModel.associate = (models) => {
    PurchaseOrderModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });
    PurchaseOrderModel.belongsTo(models.supplierModel, {
      foreignKey: 'supplierId',
      as: 'supplier',
    });

    PurchaseOrderModel.belongsTo(models.projectModel, {
      foreignKey: 'supplierId',
      as: 'project',
    });

    PurchaseOrderModel.belongsTo(models.companyModel, {
      foreignKey: 'supplierId',
      as: 'company',
    });

    PurchaseOrderModel.belongsTo(models.warrantyModel, {
      foreignKey: 'warrantyId',
      as: 'warranty',
    });

    PurchaseOrderModel.hasMany(models.purchaseOrderApproverModel, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrderApprovers',
    });

    PurchaseOrderModel.hasMany(models.attachmentModel, {
      foreignKey: 'modelId',
      as: 'attachments',
    });

    PurchaseOrderModel.belongsTo(models.canvassRequisitionModel, {
      foreignKey: 'canvassRequisitionId',
      as: 'canvassRequisition',
    });

    PurchaseOrderModel.hasMany(models.deliveryReceiptModel, {
      foreignKey: 'poId',
      as: 'deliveryReceipts',
    });

    PurchaseOrderModel.hasMany(models.rsPaymentRequestModel, {
      foreignKey: 'purchaseOrderId',
      as: 'rsPaymentRequests',
    });
  };

  return PurchaseOrderModel;
};
