module.exports = (sequelize, Sequelize) => {
  const RSPaymentRequest = sequelize.define(
    'rs_payment_requests',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      prDraftNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_pr_number',
      },
      prNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'pr_number',
      },
      prLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'pr_letter',
      },
      isDraft: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        field: 'is_draft',
        defaultValue: false,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'purchase_order_id',
      },
      deliveryInvoiceId: {
        type: Sequelize.INTEGER,
        field: 'delivery_invoice_id',
      },
      termsData: {
        type: Sequelize.JSONB,
        allowNull: true,
        field: 'terms_data',
      },
      payableDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'payable_date',
      },
      discountIn: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'discount_in',
      },
      discountPercentage: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'discount_percentage',
      },
      discountAmount: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'discount_amount',
      },
      withholdingTaxDeduction: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'withholding_tax_deduction',
      },
      deliveryFee: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'delivery_fee',
      },
      tip: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'tip',
      },
      extraCharges: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'extra_charges',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      totalAmount: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'total_amount',
      },
      lastApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'last_approver_id',
        references: {
          model: 'users',
          key: 'id',
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RSPaymentRequest.associate = (models) => {
    RSPaymentRequest.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });
    RSPaymentRequest.belongsTo(models.purchaseOrderModel, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });
    RSPaymentRequest.belongsTo(models.deliveryReceiptInvoiceModel, {
      foreignKey: 'deliveryInvoiceId',
      as: 'deliveryInvoice',
    });
    RSPaymentRequest.hasMany(models.rsPaymentRequestApproverModel, {
      foreignKey: 'paymentRequestId',
      as: 'paymentRequestApprovers',
    });
    RSPaymentRequest.hasOne(models.userModel, {
      foreignKey: 'id',
      sourceKey: 'lastApproverId',
      as: 'lastApprover',
    });
  };

  const createRequisitionPaymentHistoryRecord = async (
    paymentRequest,
    options,
  ) => {
    const { transaction } = options;

    try {
      const purchaseOrder = await sequelize.model('purchase_orders').findOne({
        attributes: ['id', 'supplierId', 'supplierType'],
        where: { id: paymentRequest.purchaseOrderId },
      });

      let supplier;
      if (purchaseOrder.supplierType === 'supplier') {
        supplier = await sequelize
          .model('suppliers')
          .findOne({ attributes: ['name'], where: { id: purchaseOrder.supplierId } });
      } else if (purchaseOrder.supplierType === 'project') {
        supplier = await sequelize
          .model('projects')
          .findOne({ attributes: ['name'], where: { id: purchaseOrder.supplierId } });
      } else {
        supplier = await sequelize
          .model('companies')
          .findOne({ attributes: ['name'], where: { id: purchaseOrder.supplierId } });
      }

      await sequelize.model('requisition_payment_histories').create(
        {
          requisitionId: paymentRequest.requisitionId,
          prNumber: paymentRequest.prNumber || '-',
          amount: paymentRequest.totalAmount || 0,
          supplier: supplier.name,
          status: paymentRequest.status,
        },
        { transaction },
      );
    } catch (error) {
      console.error('HOOK_ERROR - rsPaymentRequestModel - createRequisitionPaymentHistoryRecord: ', error.stack);
    }
  };

  RSPaymentRequest.afterCreate(createRequisitionPaymentHistoryRecord);
  RSPaymentRequest.afterUpdate(createRequisitionPaymentHistoryRecord);

  return RSPaymentRequest;
};
