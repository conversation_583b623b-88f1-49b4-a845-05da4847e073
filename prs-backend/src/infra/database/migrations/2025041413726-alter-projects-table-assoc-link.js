'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const projectTable = await queryInterface.describeTable('projects');
    if (!projectTable.company_id) {
      await queryInterface.addColumn('projects', 'company_id', {
        type: Sequelize.INTEGER,
        field: 'company_id',
      });

      await queryInterface.addIndex('projects', ['company_id']);
    }
  },

  async down(queryInterface, Sequelize) {
    const projectTable = await queryInterface.describeTable('projects');

    if (projectTable.company_id) {
      await queryInterface.removeColumn('projects', 'company_id');
      await queryInterface.removeIndex('projects', ['company_id']);
    }
  },
};
