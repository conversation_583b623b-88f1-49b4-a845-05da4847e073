'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('invoice_report_histories', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'purchase_order_id',
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
      },
      invoiceReportId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'invoice_report_id',
        references: {
          model: 'invoice_reports',
          key: 'id',
        },
      },
      irNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'ir_number',
      },
      supplierInvoiceNo: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier_invoice_no',
      },
      issuedInvoiceDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'issued_invoice_date',
      },
      invoiceAmount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        field: 'invoice_amount',
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: '--',
        field: 'status',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('invoice_report_histories');
  },
};
