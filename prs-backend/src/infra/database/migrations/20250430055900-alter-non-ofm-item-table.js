'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('non_requisition_items', 'quantity', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('non_requisition_items', 'quantity', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
  },
};
