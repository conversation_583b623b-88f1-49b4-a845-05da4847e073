'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('histories', 'quantity_requested', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: true,
    });
    await queryInterface.changeColumn('histories', 'quantity_delivered', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: true,
    });
    await queryInterface.changeColumn(
      'requisition_return_histories',
      'quantity_returned',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'requisition_return_histories',
      'quantity_ordered',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'requisition_delivery_histories',
      'quantity_ordered',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'requisition_delivery_histories',
      'quantity_delivered',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('histories', 'quantity_requested', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
    await queryInterface.changeColumn('histories', 'quantity_delivered', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
    await queryInterface.changeColumn(
      'requisition_return_histories',
      'quantity_returned',
      {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'requisition_return_histories',
      'quantity_ordered',
      {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'requisition_delivery_histories',
      'quantity_ordered',
      {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'requisition_delivery_histories',
      'quantity_delivered',
      {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
    );
  },
};
