'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.changeColumn('delivery_receipt_items_history', 'qty_ordered', {
      type: Sequelize.DECIMAL(10, 3),
      allowNull: false,
    });
    await queryInterface.changeColumn('delivery_receipt_items_history', 'qty_delivered', {
      type: Sequelize.DECIMAL(10, 3),
      allowNull: false,
    });
    await queryInterface.changeColumn('delivery_receipt_items_history', 'qty_returned', {
      type: Sequelize.DECIMAL(10, 3),
      allowNull: false,
    });
  },

  async down (queryInterface, Sequelize) {
  }
};
