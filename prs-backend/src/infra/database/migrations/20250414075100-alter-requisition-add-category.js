'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('requisitions', 'category', {
      type: Sequelize.ENUM('company', 'association', 'project'),
      allowNull: false,
      defaultValue: 'company',
    });

    await queryInterface.changeColumn('requisitions', 'chargeTo', {
      type: Sequelize.STRING(255),
      allowNull: true,
      field: 'charge_to',
    });

    await queryInterface.addColumn('requisitions', 'chargeToId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      field: 'charge_to_id',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('requisitions', 'category');
    await queryInterface.changeColumn('requisitions', 'chargeTo', {
      type: Sequelize.STRING(255),
      allowNull: false,
      field: 'charge_to',
    });
    await queryInterface.changeColumn('requisitions', 'chargeToId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      field: 'charge_to_id',
    });
  },
};
