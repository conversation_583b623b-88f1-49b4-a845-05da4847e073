'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('canvass_items', 'cancelled_qty', {
      type: Sequelize.DECIMAL(13, 3),
      defaultValue: 0,
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('canvass_items', 'cancelled_qty');
  },
};
