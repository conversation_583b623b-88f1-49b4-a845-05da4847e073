'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // requisition_item_lists
    await queryInterface.changeColumn('requisition_item_lists', 'quantity', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: false,
    });

    // purchase_order_items
    await queryInterface.changeColumn(
      'purchase_order_items',
      'quantity_purchased',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        defaultValue: 0,
      },
    );

    // delivery_receipt_items
    await queryInterface.changeColumn('delivery_receipt_items', 'qty_ordered', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: false,
    });
    await queryInterface.changeColumn(
      'delivery_receipt_items',
      'qty_delivered',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: true,
      },
    );
    await queryInterface.changeColumn(
      'delivery_receipt_items',
      'qty_returned',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: true,
      },
    );

    // items
    await queryInterface.changeColumn('items', 'gfq', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: true,
    });
    await queryInterface.changeColumn('items', 'remaining_gfq', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: true,
    });

    // tom_items
    await queryInterface.changeColumn('tom_items', 'quantity', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    // requisition_item_lists
    await queryInterface.changeColumn('requisition_item_lists', 'quantity', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });

    // purchase_order_items
    await queryInterface.changeColumn(
      'purchase_order_items',
      'quantity_purchased',
      {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
    );

    // delivery_receipt_items
    await queryInterface.changeColumn('delivery_receipt_items', 'qty_ordered', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
    await queryInterface.changeColumn(
      'delivery_receipt_items',
      'qty_delivered',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
    );
    await queryInterface.changeColumn(
      'delivery_receipt_items',
      'qty_returned',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
    );

    // items
    await queryInterface.changeColumn('items', 'gfq', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.changeColumn('items', 'remaining_gfq', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    // tom_items
    await queryInterface.changeColumn('tom_items', 'quantity', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
  },
};
