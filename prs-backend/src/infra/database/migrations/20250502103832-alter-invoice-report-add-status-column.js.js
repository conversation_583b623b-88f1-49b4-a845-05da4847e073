'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('invoice_reports');
    if (!table.status) {
      await queryInterface.addColumn('invoice_reports', 'status', {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: '--',
        field: 'status',
      });
    }
  },

  async down(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('invoice_reports');

    if (table.status) {
      await queryInterface.removeColumn('invoice_reports', 'status');
    }
  },
};
