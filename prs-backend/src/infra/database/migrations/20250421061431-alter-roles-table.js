'use strict';

const { USER_TYPES } = require('../../../domain/constants/userConstants');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const existingAreaStaff = await queryInterface.select(null, 'roles', {
      where: {
        name: 'Area Staff',
      },
    });

    const existingDeptSecretary = await queryInterface.select(null, 'roles', {
      where: {
        name: 'Department Secretary',
      },
    });

    const tables = await queryInterface.sequelize.query(`
      SELECT
    tc.table_name,
    kcu.column_name
FROM
    information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
      AND tc.table_name = kcu.table_name
    JOIN information_schema.referential_constraints AS rc
      ON tc.constraint_name = rc.constraint_name
      AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND rc.unique_constraint_name IN (
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name = 'roles' -- Assuming lowercase table name
      AND table_schema = 'public' -- Assuming public schema
  );
  `);

    if (existingDeptSecretary.length > 0) {
      await queryInterface.bulkDelete('role_permissions', {
        role_id: existingDeptSecretary[0].id,
      });

      console.log('Removed roles');
    }

    if (
      tables[1].rows.length > 0 &&
      existingDeptSecretary.length > 0 &&
      existingAreaStaff.length > 0
    ) {
      tables[1].rows.map(
        async (row) => (
          await queryInterface.sequelize.query(
            `
            UPDATE ${row.table_name} SET ${row.column_name} = ${existingAreaStaff[0].id} WHERE ${row.column_name} = ${existingDeptSecretary[0].id}
          `,
          ),
          console.log(`Successfully Updated the roles for ${row.table_name}`)
        ),
      );
    } else {
      console.log(
        'Skipping role reference updates: One or more required roles not found',
      );
    }

    if (existingAreaStaff.length > 0) {
      await queryInterface.sequelize.query(
        `
        UPDATE roles SET name = 'Area Staff/Department Secretary'  WHERE name = 'Area Staff'
        `,
      );
      console.log('Successfully updated role name.');
    } else {
      console.log('Area Staff role not found, skipping role name update');
    }

    // Only attempt to delete if the role exists
    if (existingDeptSecretary.length > 0) {
      await queryInterface.bulkDelete('roles', {
        name: 'Department Secretary',
      });
      console.log('Successfully deleted Department Secretary role');
    } else {
      console.log('Department Secretary role not found, skipping deletion');
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `
      UPDATE roles SET name = 'Area Staff' WHERE name = 'Area Staff/Department Secretary'
      `,
    ),
      await queryInterface.bulkInsert(
        'roles',
        [
          {
            name: 'Department Secretary',
            is_permanent: false,
            created_at: new Date(), // Use 'created_at'
            updated_at: new Date(), // Use 'updated_at'
          },
        ],
        {},
      );

    const roles = await queryInterface.sequelize.query(
      `SELECT id, name FROM roles WHERE name = :name`,
      {
        replacements: { name: USER_TYPES.DEPARTMENT_SECRETARY },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (!roles.length) {
      console.log('Department Secretary role not found');
      return;
    }

    /**
     * Department Secretary
     * - View/Get Dashboard
     * - View/Approval Canvass
     * - View Orders
     * - View Delivery
     * - View Payments
     * - View Non-RS Payments
     */
    const departmentSecretaryRole = roles[0];
    const permissions = await queryInterface.sequelize.query(
      `SELECT id, module, action FROM permissions
       WHERE (module = 'dashboard' AND action IN ('view', 'get'))
       OR (module = 'canvass' AND action IN ('view', 'get', 'approval'))
       OR (module = 'orders' AND action IN ('view', 'get'))
       OR (module = 'delivery' AND action IN ('view', 'get'))
       OR (module = 'payments' AND action IN ('view', 'get'))
       OR (module = 'non_rs_payments' AND action IN ('view', 'get'))`,
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    const dateNow = new Date();
    const rolePermissions = permissions.map((permission) => ({
      role_id: departmentSecretaryRole.id,
      permission_id: permission.id,
      created_at: dateNow,
      updated_at: dateNow,
    }));

    /* Check existing role permissions */
    const existingRolePermissions = await queryInterface.sequelize.query(
      `SELECT role_id, permission_id
       FROM role_permissions
       WHERE role_id = :roleId`,
      {
        replacements: { roleId: departmentSecretaryRole.id },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    /* Filter out existing role permissions */
    const newRolePermissions = rolePermissions.filter(
      (rp) =>
        !existingRolePermissions.some(
          (existing) => existing.permission_id === rp.permission_id,
        ),
    );

    if (newRolePermissions.length > 0) {
      console.log(
        'Department Secretary - permissions to add',
        newRolePermissions,
      );
      await queryInterface.bulkInsert(
        'role_permissions',
        newRolePermissions,
        {},
      );
      console.log(
        `Added ${newRolePermissions.length} permissions to Department Secretary role`,
      );
    } else {
      console.log('No new permissions to add');
    }

    /* Log assigned permissions for verification */
    console.log('Department Secretary permissions:');
    permissions.forEach((p) => {
      console.log(`- ${p.module} ${p.action}`);
    });
  },
};
